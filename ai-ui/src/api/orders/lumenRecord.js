import request from '@/utils/request'

// 查询lumen实时记录列表
export function listLumenRecord(query) {
  return request({
    url: '/orders/lumenRecord/list',
    method: 'get',
    params: query
  })
}

// 查询lumen实时记录详细
export function getLumenRecord(id) {
  return request({
    url: '/orders/lumenRecord/' + id,
    method: 'get'
  })
}

// 新增lumen实时记录
export function addLumenRecord(data) {
  return request({
    url: '/orders/lumenRecord',
    method: 'post',
    data: data
  })
}

// 修改lumen实时记录
export function updateLumenRecord(data) {
  return request({
    url: '/orders/lumenRecord',
    method: 'put',
    data: data
  })
}

// 删除lumen实时记录
export function delLumenRecord(id) {
  return request({
    url: '/orders/lumenRecord/' + id,
    method: 'delete'
  })
}

// 减少lumen量
export function reduceLumen(id,lumen) {
  return request({
    url: '/orders/lumenRecord/reduceLumen/' + id + '/'+lumen,
    method: 'put'
  })
}

// 赠送lumen量
export function getGiftLumen(id,lumen) {
  return request({
    url: '/orders/lumenRecord/getGiftLumen/' + id + '/'+lumen,
    method: 'put'
  })
}

// 设置无效
export function setLumenInvalid(id) {
  return request({
    url: '/orders/lumenRecord/setLumenInvalid/' + id,
    method: 'put'
  })
}

// 查看lumen 操作日志
export function getLumenAdminOperateLogList(id) {
  return request({
    url: '/orders/lumenRecord/getLumenAdminOperateLogList/' + id,
    method: 'get'
  })
}

// 查看lumen 消费日志
export function lumenList(query) {
  return request({
    url: '/orders/lumenRecord/lumen-list',
    method: 'get',
    params: query
  })
}

