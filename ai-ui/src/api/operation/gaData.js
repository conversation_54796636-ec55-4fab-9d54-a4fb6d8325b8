import request from '@/utils/request'

// 查询GA数据列表
export function listGaData(query) {
  return request({
    url: '/operation/gaData/list',
    method: 'get',
    params: query
  })
}

// 查询GA数据详细
export function getGaData(id) {
  return request({
    url: '/operation/gaData/' + id,
    method: 'get'
  })
}

// 新增GA数据
export function addGaData(data) {
  return request({
    url: '/operation/gaData',
    method: 'post',
    data: data
  })
}

// 修改GA数据
export function updateGaData(data) {
  return request({
    url: '/operation/gaData',
    method: 'put',
    data: data
  })
}

// 删除GA数据
export function delGaData(id) {
  return request({
    url: '/operation/gaData/' + id,
    method: 'delete'
  })
}

// 返回ga 需要的每日数据
export function selectGaDateList(data) {
  return request({
    url: '/operation/gaData/ga-data-list?date=' + data,
    method: 'get'
  })
}