import request from '@/utils/request'

// 同步comfy
export function comfyInit() {
    return request({
        url: '/comfy/init',
        method: 'post'
    })
}

//添加comy连接
export function comfyAdd(data) {
    return request({
        url: '/comfy/add',
        method: 'post',
        data: data
    })
}

//移除comy连接
export function comfyRemove(data) {
    return request({
        url: '/comfy/remove',
        method: 'post',
        data: data
    })
}

// 检查comy 连接状态
export function comfyCheck(data) {
    return request({
        url: '/comfy/check-instance',
        method: 'post',
        data: data
    })
}

// 检查comy 报错message
export function comfyErroMessage(query) {
    return request({
        url: '/comfy/query-error-message',
        method: 'post',
        params: query
    })
}