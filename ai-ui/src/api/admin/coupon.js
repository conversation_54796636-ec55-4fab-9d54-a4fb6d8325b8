import request from '@/utils/request'

// 查询通用优惠券列表
export function listCoupon(query) {
  return request({
    url: '/admin/coupon/list',
    method: 'get',
    params: query
  })
}

// 查询通用优惠券详细
export function getCoupon(id) {
  return request({
    url: '/admin/coupon/' + id,
    method: 'get'
  })
}

// 新增通用优惠券
export function addCoupon(data) {
  return request({
    url: '/admin/coupon',
    method: 'post',
    data: data
  })
}

// 修改通用优惠券
export function updateCoupon(data) {
  return request({
    url: '/admin/coupon',
    method: 'put',
    data: data
  })
}

// 删除通用优惠券
export function delCoupon(id) {
  return request({
    url: '/admin/coupon/' + id,
    method: 'delete'
  })
}

// 启用/禁用通用优惠券
export function updateCouponStatus(id, valid) {
  return request({
    url: '/admin/coupon/status/' + id + '/' + valid,
    method: 'put'
  })
}

// 导出通用优惠券
export function exportCoupon(query) {
  return request({
    url: '/admin/coupon/export',
    method: 'post',
    params: query
  })
}
