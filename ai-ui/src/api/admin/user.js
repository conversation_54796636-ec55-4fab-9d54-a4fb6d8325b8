import request from '@/utils/request'

// 查询用户信息列表
export function listUser(query) {
  return request({
    url: '/admin/user/list',
    method: 'get',
    params: query
  })
}

// 查询用户信息详细
export function getUser(id) {
  return request({
    url: '/admin/user/' + id,
    method: 'get'
  })
}

// 新增用户信息
export function addUser(data) {
  return request({
    url: '/admin/user',
    method: 'post',
    data: data
  })
}

// 修改用户信息
export function updateUser(data) {
  return request({
    url: '/admin/user',
    method: 'put',
    data: data
  })
}

// 删除用户信息
export function delUser(id) {
  return request({
    url: '/admin/user/' + id,
    method: 'delete'
  })
}



// 查询注册用户数量
export function getUserCount(query) {
  return request({
    url: '/admin/user/count',
    method: 'get',
    params: query
  })
}



// 查询用户信息列表
export function vipLst(query) {
  return request({
    url: '/admin/user/vip-list',
    method: 'get',
    params: query
  })
}

// 通过登录名获取用户列表
export function getUserByLoginName(loginName) {
  return request({
    url: '/admin/user/getUserByLoginName?loginName='+loginName,
    method: 'get'
  })
}