import request from '@/utils/request'

// 查询优惠配置列表
export function listPromotion(query) {
  return request({
    url: '/admin/promotion/list',
    method: 'get',
    params: query
  })
}

// 查询优惠配置详细
export function getPromotion(id) {
  return request({
    url: '/admin/promotion/' + id,
    method: 'get'
  })
}

// 新增优惠配置
export function addPromotion(data) {
  return request({
    url: '/admin/promotion',
    method: 'post',
    data: data
  })
}

// 修改优惠配置
export function updatePromotion(data) {
  return request({
    url: '/admin/promotion',
    method: 'put',
    data: data
  })
}

// 删除优惠配置
export function delPromotion(id) {
  return request({
    url: '/admin/promotion/' + id,
    method: 'delete'
  })
}

// 启用/禁用优惠配置
export function updatePromotionStatus(id, enable) {
  return request({
    url: '/admin/promotion/status/' + id + '/' + enable,
    method: 'put'
  })
}

// 导出优惠配置
export function exportPromotion(query) {
  return request({
    url: '/admin/promotion/export',
    method: 'post',
    params: query
  })
}
