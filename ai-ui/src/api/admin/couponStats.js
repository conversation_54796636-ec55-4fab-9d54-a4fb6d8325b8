import request from '@/utils/request'

// 查询所有优惠码统计数据
export function listCouponStats(query) {
  return request({
    url: '/admin/coupon/stats/list',
    method: 'get',
    params: query
  })
}

// 查询所有类型列表
export function getCouponTypes() {
  return request({
    url: '/admin/coupon/stats/types',
    method: 'get'
  })
}

// 根据优惠码查询统计数据
export function getCouponStats(couponCode) {
  return request({
    url: '/admin/coupon/stats/detail/' + couponCode,
    method: 'get'
  })
}

// 根据优惠码查询购买详情列表
export function getCouponPurchaseDetails(couponCode, query) {
  return request({
    url: '/admin/coupon/stats/purchases/' + couponCode,
    method: 'get',
    params: query
  })
}
