import request from '@/utils/request'

// 查询kpi 数据追踪-lumen消耗列表
export function listCost(query) {
  return request({
    url: '/tracking/cost/list',
    method: 'get',
    params: query
  })
}

// 查询kpi 数据追踪-lumen消耗详细
export function getCost(id) {
  return request({
    url: '/tracking/cost/' + id,
    method: 'get'
  })
}

// 新增kpi 数据追踪-lumen消耗
export function addCost(data) {
  return request({
    url: '/tracking/cost',
    method: 'post',
    data: data
  })
}

// 修改kpi 数据追踪-lumen消耗
export function updateCost(data) {
  return request({
    url: '/tracking/cost',
    method: 'put',
    data: data
  })
}

// 删除kpi 数据追踪-lumen消耗
export function delCost(id) {
  return request({
    url: '/tracking/cost/' + id,
    method: 'delete'
  })
}
