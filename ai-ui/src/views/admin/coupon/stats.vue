<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span style="font-size: 18px; font-weight: bold;">折扣统计</span>
          </div>

          <!-- Tab切换 -->
          <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="mb20">
            <el-tab-pane label="全部" name="all"></el-tab-pane>
            <el-tab-pane
              v-for="type in typeList"
              :key="type"
              :label="getTypeLabel(type)"
              :name="type">
            </el-tab-pane>
          </el-tabs>

          <el-table :data="statsData" style="width: 100%" @row-click="handleRowClick" v-loading="statsLoading">
            <el-table-column prop="couponCode" label="折扣码" width="120" align="center" />
            <el-table-column prop="type" label="类型" width="100" align="center">
              <template slot-scope="scope">
                {{ getTypeLabel(scope.row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalLumenAmount" label="会员总Lumen购买金额" width="200" align="center">
              <template slot-scope="scope">
                ${{ scope.row.totalLumenAmount || '0.00' }}
              </template>
            </el-table-column>
            <el-table-column prop="memberCount" label="购买会员人数" width="150" align="center">
              <template slot-scope="scope">
                {{ scope.row.memberCount || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="totalMemberAmount" label="购买会员总金额" width="150" align="center">
              <template slot-scope="scope">
                ${{ scope.row.totalMemberAmount || '0.00' }}
              </template>
            </el-table-column>
            <el-table-column prop="lumenPackageCount" label="购买Lumen套餐包人数" width="180" align="center">
              <template slot-scope="scope">
                {{ scope.row.lumenPackageCount || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="lumenAmount" label="购买Lumen金额" width="150" align="center">
              <template slot-scope="scope">
                ${{ scope.row.lumenAmount || '0.00' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 购买列表 -->
    <el-card class="box-card" v-if="selectedCouponCode">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: bold;">购买列表 - {{ selectedCouponCode }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="closePurchaseList">关闭</el-button>
      </div>
      
      <el-table v-loading="purchaseLoading" :data="purchaseList" style="width: 100%">
        <el-table-column prop="loginName" label="用户名称" width="150" align="center" />
        <el-table-column prop="productName" label="购买项名称" width="200" align="center" />
        <el-table-column prop="qty" label="购买数量" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.row.qty || 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="实付金额" width="120" align="center">
          <template slot-scope="scope">
            ${{ scope.row.amount || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="usedTime" label="付款日期" width="180" align="center">
          <template slot-scope="scope">
            {{ formatTimestamp(scope.row.usedTime) }}
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="purchaseTotal > 0"
        :total="purchaseTotal"
        :page.sync="purchaseQueryParams.pageNum"
        :limit.sync="purchaseQueryParams.pageSize"
        @pagination="getPurchaseList"
      />
    </el-card>
  </div>
</template>

<script>
import { listCouponStats, getCouponPurchaseDetails, getCouponTypes } from "@/api/admin/couponStats";

export default {
  name: "CouponStats",
  data() {
    return {
      // 当前激活的Tab
      activeTab: 'all',
      // 类型列表
      typeList: [],
      // 统计数据
      statsData: [],
      // 统计数据加载状态
      statsLoading: false,
      // 选中的优惠码
      selectedCouponCode: '',
      // 购买列表数据
      purchaseList: [],
      // 购买列表加载状态
      purchaseLoading: false,
      // 购买列表总数
      purchaseTotal: 0,
      // 购买列表查询参数
      purchaseQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 类型标签映射
      typeLabelMap: {
        'promotion': '促销',
        'discount': '折扣',
        'coupon': '优惠券',
        'rebate': '返利'
      }
    };
  },
  created() {
    this.getTypeList();
    this.initFromQuery();
    this.getStatsList();
  },
  methods: {
    /** 格式化时间戳为东八区时间 */
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      // 时间戳是秒，需要转换为毫秒
      const date = new Date(timestamp * 1000);
      // 使用toLocaleString直接转换为东八区时间
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-');
    },
    /** 获取类型标签 */
    getTypeLabel(type) {
      return this.typeLabelMap[type] || type || '-';
    },
    /** 查询类型列表 */
    getTypeList() {
      getCouponTypes().then(response => {
        this.typeList = response.data || [];
      });
    },
    /** 查询统计数据列表 */
    getStatsList() {
      this.statsLoading = true;
      const params = {};
      if (this.activeTab !== 'all') {
        params.type = this.activeTab;
      }
      listCouponStats(params).then(response => {
        this.statsData = response.rows || [];
        this.statsLoading = false;
      }).catch(() => {
        this.statsLoading = false;
      });
    },
    /** Tab切换事件 */
    handleTabClick(tab) {
      this.activeTab = tab.name;
      this.getStatsList();
      // 切换Tab时关闭购买列表
      this.closePurchaseList();
    },
    /** 行点击事件 */
    handleRowClick(row) {
      this.selectedCouponCode = row.couponCode;
      this.purchaseQueryParams.pageNum = 1;
      this.getPurchaseList();
    },
    /** 查询购买详情列表 */
    getPurchaseList() {
      if (!this.selectedCouponCode) return;
      
      this.purchaseLoading = true;
      getCouponPurchaseDetails(this.selectedCouponCode, this.purchaseQueryParams).then(response => {
        this.purchaseList = response.rows || [];
        this.purchaseTotal = response.total || 0;
        this.purchaseLoading = false;
      }).catch(() => {
        this.purchaseLoading = false;
      });
    },
    /** 关闭购买列表 */
    closePurchaseList() {
      this.selectedCouponCode = '';
      this.purchaseList = [];
      this.purchaseTotal = 0;
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.el-table {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.el-tabs {
  margin-bottom: 20px;
}

.el-tabs .el-tabs__header {
  margin-bottom: 15px;
}
</style>
