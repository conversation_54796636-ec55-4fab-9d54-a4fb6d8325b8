<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="优惠码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入优惠码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select v-model="queryParams.productType" placeholder="请选择产品类型" clearable>
          <el-option label="计划" value="plan" />
          <el-option label="单次" value="one" />
          <el-option label="全部" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="适用等级" prop="level">
        <el-select v-model="queryParams.level" placeholder="请选择适用等级" clearable>
          <el-option label="All" value="all" />
          <el-option label="Standard Month" value="standard.month" />
          <el-option label="Standard Year" value="standard.year" />
          <el-option label="Pro Month" value="pro.month" />
          <el-option label="Pro Year" value="pro.year" />
          <el-option label="Lumen 100" value="lumen.100" />
          <el-option label="Lumen 1000" value="lumen.1000" />
          <el-option label="Lumen 10000" value="lumen.10000" />
        </el-select>
      </el-form-item>
      <el-form-item label="有效状态" prop="valid">
        <el-select v-model="queryParams.valid" placeholder="请选择有效状态" clearable>
          <el-option label="有效" :value="true" />
          <el-option label="无效" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="红人名称" prop="celebrityName">
        <el-input
          v-model="queryParams.celebrityName"
          placeholder="请输入红人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['admin:coupon:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['admin:coupon:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['admin:coupon:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['admin:coupon:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="couponList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="优惠码" align="center" prop="code" />
      <el-table-column label="折扣力度" align="center" prop="percentOff">
        <template slot-scope="scope">
          <span v-if="scope.row.percentOff != null && scope.row.percentOff !== ''">{{ scope.row.percentOff }}% Off</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="折扣范围" align="center" prop="productType">
        <template slot-scope="scope">
          <span v-if="scope.row.productType === 'plan'">计划</span>
          <span v-else-if="scope.row.productType === 'one'">单次</span>
          <span v-else-if="scope.row.productType === 'all'">全部</span>
          <span v-else>{{ scope.row.productType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="折扣时间" align="center" width="350">
        <template slot-scope="scope">
          <span v-if="scope.row.startTime && scope.row.redeemBy">
            {{ formatTimestamp(scope.row.startTime) }} - {{ formatTimestamp(scope.row.redeemBy) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="mark" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="valid">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.valid"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['admin:coupon:edit']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['admin:coupon:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['admin:coupon:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改通用优惠券对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="优惠码" prop="code" v-if="form.id">
              <el-input v-model="form.code" placeholder="系统自动生成" :disabled="true" />
              <div style="font-size: 12px; color: #999; margin-top: 5px;">优惠码由系统自动生成</div>
            </el-form-item>
            <el-form-item v-else>
              <div style="font-size: 14px; color: #666;">
                <i class="el-icon-info"></i> 优惠码将在保存时自动生成（8位大写字母+数字）
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折扣力度" prop="percentOff">
<!--              <el-input-number v-model="form.percentOff" :min="1" :max="100" placeholder="请输入折扣百分比" />-->
              <el-select v-model="form.percentOff" placeholder="请输入折扣百分比" @change="handleProductTypeChange">
                <el-option label="5% OFF" value="5" />
                <el-option label="10% OFF" value="10" />
                <el-option label="15% OFF" value="15" />
                <el-option label="20% OFF" value="20" />
                <el-option label="25% OFF" value="25" />
                <el-option label="30% OFF" value="30" />
                <el-option label="35% OFF" value="35" />
                <el-option label="40% OFF" value="40" />
                <el-option label="45% OFF" value="45" />
                <el-option label="50% OFF" value="50" />
                <el-option label="55% OFF" value="55" />
                <el-option label="60% OFF" value="60" />
                <el-option label="65% OFF" value="65" />
                <el-option label="70% OFF" value="70" />
                <el-option label="75% OFF" value="75" />
                <el-option label="80% OFF" value="80" />
                <el-option label="85% OFF" value="85" />
                <el-option label="90% OFF" value="90" />
                <el-option label="95% OFF" value="95" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="折扣范围" prop="productType">
              <el-select v-model="form.productType" placeholder="请选择产品类型" @change="handleProductTypeChange">
                <el-option label="All Things" value="all" />
                <el-option label="计划" value="plan" />
                <el-option label="单次" value="one" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用等级" prop="level">
              <el-select v-model="form.level" placeholder="请选择适用等级" :disabled="!form.productType">
                <el-option
                  v-for="option in levelOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="折扣时间" prop="timeRange">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="timestamp"
            @change="handleTimeRangeChange">
          </el-date-picker>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="使用人数限制" prop="maxRedemptions">
              <el-radio-group v-model="redemptionType">
                <el-radio label="unlimited">无限制</el-radio>
                <el-radio label="limited">限制人数</el-radio>
              </el-radio-group>
              <el-input-number
                v-if="redemptionType === 'limited'"
                v-model="form.maxRedemptions"
                :min="1"
                placeholder="请输入最大使用次数"
                style="margin-top: 10px; width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="红人名称" prop="celebrityName">
              <el-input v-model="form.celebrityName" placeholder="请输入红人名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="mark">
          <el-input v-model="form.mark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="有效状态" prop="valid">
          <el-radio-group v-model="form.valid">
            <el-radio :label="true">有效</el-radio>
            <el-radio :label="false">无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCoupon, getCoupon, delCoupon, addCoupon, updateCoupon, updateCouponStatus } from "@/api/admin/coupon";

export default {
  name: "Coupon",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 通用优惠券表格数据
      couponList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 时间范围
      timeRange: [],
      // 使用次数限制类型
      redemptionType: 'unlimited',
      // 等级选项
      levelOptions: [],
      // 等级选项映射
      levelOptionsMap: {
        all: [
          { label: 'All', value: 'all' }
        ],
        plan: [
          { label: 'All', value: 'all' },
          { label: 'Standard Month', value: 'standard.month' },
          { label: 'Standard Year', value: 'standard.year' },
          { label: 'Standard', value: 'standard' },
          { label: 'Pro Month', value: 'pro.month' },
          { label: 'Pro Year', value: 'pro.year' },
          { label: 'Pro', value: 'pro' }
        ],
        one: [
          { label: 'All', value: 'all' },
          { label: 'Lumen 100', value: 'lumen.100' },
          { label: 'Lumen 1000', value: 'lumen.1000' },
          { label: 'Lumen 10000', value: 'lumen.10000' }
        ]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        productType: null,
        level: null,
        valid: null,
        celebrityName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        percentOff: [
          { required: true, message: "折扣百分比不能为空", trigger: "blur" }
        ],
        productType: [
          { required: true, message: "产品类型不能为空", trigger: "change" }
        ],
        level: [
          { required: true, message: "适用等级不能为空", trigger: "change" },
          { validator: this.validateLevel, trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 格式化时间戳为东八区时间 */
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      // 时间戳是秒，需要转换为毫秒
      const date = new Date(timestamp * 1000);
      // 使用toLocaleString直接转换为东八区时间
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-');
    },
    /** 查询通用优惠券列表 */
    getList() {
      this.loading = true;
      listCoupon(this.queryParams).then(response => {
        this.couponList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        productType: null,
        level: null,
        code: null,
        percentOff: null,
        startTime: null,
        redeemBy: null,
        maxRedemptions: null,
        valid: true,
        mark: null,
        celebrityName: null
      };
      this.timeRange = [];
      this.redemptionType = 'unlimited';
      this.levelOptions = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "折扣码新增";
      if (this.form.productType) {
        console.log(this.form.productType);
        this.levelOptions = this.levelOptionsMap[this.form.productType] || [];
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCoupon(id).then(response => {
        this.form = response.data;

        // 根据产品类型设置等级选项
        if (this.form.productType) {
          console.log(this.form.productType);
          this.levelOptions = this.levelOptionsMap[this.form.productType] || [];
        }

        // 设置时间范围
        if (this.form.startTime && this.form.redeemBy) {
          this.timeRange = [parseInt(this.form.startTime) * 1000, parseInt(this.form.redeemBy) * 1000];
        }
        // 设置使用次数限制类型
        if (this.form.maxRedemptions === -1 || this.form.maxRedemptions === null) {
          this.redemptionType = 'unlimited';
        } else {
          this.redemptionType = 'limited';
        }
        this.open = true;
        this.title = "修改通用优惠券";
      });
    },
    /** 产品类型变化处理 */
    handleProductTypeChange(value) {
      // 根据产品类型更新等级选项
      this.levelOptions = this.levelOptionsMap[value] || [];

      // 重置等级选择
      if (value === 'all') {
        // all类型只能选择all等级
        this.form.level = 'all';
      } else {
        // 其他类型默认选择all
        this.form.level = 'all';
      }
    },
    /** 时间范围变化处理 */
    handleTimeRangeChange(value) {
      if (value && value.length === 2) {
        this.form.startTime = Math.floor(value[0] / 1000);
        this.form.redeemBy = Math.floor(value[1] / 1000);
      } else {
        this.form.startTime = null;
        this.form.redeemBy = null;
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理使用次数限制
          if (this.redemptionType === 'unlimited') {
            this.form.maxRedemptions = -1;
          }

          if (this.form.id != null) {
            updateCoupon(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCoupon(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除通用优惠券编号为"' + ids + '"的数据项？').then(function() {
        return delCoupon(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('admin/coupon/export', {
        ...this.queryParams
      }, `coupon_${new Date().getTime()}.xlsx`)
    },
    /** 等级验证 */
    validateLevel(rule, value, callback) {
      if (!this.form.productType) {
        callback(new Error('请先选择产品类型'));
        return;
      }

      // 验证等级是否符合产品类型的要求
      const validLevels = this.levelOptionsMap[this.form.productType] || [];
      const isValid = validLevels.some(option => option.value === value);

      if (!isValid) {
        callback(new Error('所选等级与产品类型不匹配'));
        return;
      }

      // all类型只能选择all等级
      if (this.form.productType === 'all' && value !== 'all') {
        callback(new Error('All Things类型只能选择All等级'));
        return;
      }

      callback();
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.valid ? "启用" : "禁用";
      this.$modal.confirm('确认要"' + text + '""' + row.code + '"优惠券吗？').then(function() {
        return updateCouponStatus(row.id, row.valid);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.valid = row.valid === false ? true : false;
      });
    }
  }
};
</script>
