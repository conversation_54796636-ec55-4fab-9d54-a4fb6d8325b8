<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="loginName">
        <el-input v-model="queryParams.loginName" placeholder="请输入用户账号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="任务id" prop="promptId">
        <el-input v-model="queryParams.promptId" placeholder="请输入任务id" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="公开状态" prop="isPublic">
        <el-select v-model="queryParams.isPublic" placeholder="请选择公开状态" clearable>
          <el-option v-for="dict in dict.type.is_public_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col v-if="displayMode === 'grid'" :span="1.5">
          <el-checkbox size="mini"   v-model="isCheckedAll" label="全选当页" border
            @change="handleSelectAll" />
        </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['admin:file:add']">新增</el-button>
          
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['admin:file:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['admin:file:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['admin:file:export']">导出</el-button>
      </el-col>
      <batch-download :imageUrls="imgUrls"/>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      <el-tooltip class="item" effect="dark" :content="showSearchImg ? '图片展示' : '列表展示'" placement="top">
        <el-button size="mini" circle :icon=iconImg  @click="toggleSearchImg()" style="float: right;"/>
      </el-tooltip>
    </el-row>
    <div v-if="displayMode === 'grid'" v-loading="loading" class="grid">
        <!-- 判断 reviewList 是否为空 -->
        <div v-if="fileList.length === 0" class="no-data">暂无数据</div>

        <!-- 如果有数据，显示图片列表 -->
        <div v-else class="grid-item" v-for="(item) in fileList" :key="item.id">
          <!-- <el-image style="width: 100%; height: 100%" :src="item.highMiniUrl" :preview-src-list="[item.highMiniUrl]"
            fit="contain" /> -->
    
            <el-image class="grid-image" style="width: 100%; height: 100%" :src="item.highMiniUrl || item.thumbnailUrl"
              :preview-src-list="[ item.thumbnailUrl]" fit="contain" lazy />
            <!-- 鼠标悬浮时显示提示词 -->
          <el-checkbox class="grid-item-checked" v-model="item.reviewChecked" label="选中"
            @change="changeChecked"></el-checkbox>

         

        </div>
      </div>
    <el-table v-if="displayMode === 'table'"  v-loading="loading" :data="fileList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="用户账号" align="center" prop="loginName" />
      <el-table-column label="任务id" align="center" prop="promptId" />
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="缩略图名称" align="center" prop="thumbnailName" />
      <el-table-column label="缩略图路径" align="center" prop="thumbnailUrl" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.thumbnailUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公开状态" align="center" prop="reviewStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_public_type" :value="scope.row.isPublic" />
        </template>
      </el-table-column>
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="拒绝内容描述" align="center" prop="rejectionContent" />
      <el-table-column label="图片结果宽度" align="center" prop="width" />
      <el-table-column label="图片结果高度" align="center" prop="height" />
      <el-table-column label="图片点赞数" align="center" prop="likeNums" />
      <el-table-column label="逻辑删除" align="center" prop="del">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.del" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['admin:file:edit']">修改</el-button>
            <el-button  v-if="scope.row.isPublic === 3" size="mini" type="text" icon="el-icon-edit" @click="toResumingAudit(scope.row)"
           >恢复</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['admin:file:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" @prev="prevClick" @next="nextClick" />


    <!-- 添加或修改任务图片对应关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户账号" prop="loginName">
          <el-input v-model="form.loginName" placeholder="请输入用户账号" />
        </el-form-item>
        <el-form-item label="任务id" prop="promptId">
          <el-input v-model="form.promptId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入文件名称" />
        </el-form-item>
        <el-form-item label="缩略图名称" prop="thumbnailName">
          <el-input v-model="form.thumbnailName" placeholder="请输入缩略图名称" />
        </el-form-item>
        <el-form-item label="图片路径" prop="fileUrl">
          <el-input v-model="form.fileUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="缩略图路径" prop="thumbnailUrl">
          <image-upload v-model="form.thumbnailUrl" />
        </el-form-item>
        <el-form-item label="涉黄：NSFW" prop="sensitiveMessage">
          <el-input v-model="form.sensitiveMessage" placeholder="请输入涉黄：NSFW" />
        </el-form-item>
        <el-form-item label="图片结果宽度" prop="width">
          <el-input v-model="form.width" placeholder="请输入图片结果宽度" />
        </el-form-item>
        <el-form-item label="图片结果高度" prop="height">
          <el-input v-model="form.height" placeholder="请输入图片结果高度" />
        </el-form-item>
        <el-form-item label="图片点赞数" prop="likeNums">
          <el-input v-model="form.likeNums" placeholder="请输入图片点赞数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFile, getFile, delFile, addFile, updateFile,resumingAudit } from "@/api/admin/file";
import BatchDownload from '@/components/BatchDownload/index.vue'
export default {
  name: "File",
  dicts: ['is_del', 'is_public_type'],
  components: { BatchDownload },
  data() {
    
    return {
      imgUrls: [,],
    iconImg:'el-icon-picture',
    isCheckedAll: false, //是否全选
    showSearchImg:true,

    displayMode: 'table',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      loginNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务图片对应关系表格数据
      fileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        isPublic: null,
        pageNum: 1,
        pageSize: 10,
        loginName: null,
        promptId: null,
        fileName: null,
        thumbnailName: null,
        id: null,
        isNext: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    toResumingAudit(row){
      const id = row.id
      const loginName = row.loginName 
      this.$modal.confirm('是否确认恢复此数据').then(function () {
        return resumingAudit(id, loginName);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("恢复成功");
      }).catch(() => { });
    },
    toggleSearchImg(){
        if (this.displayMode==='grid'){
          this.displayMode='table'
          this.showSearchImg=false
          this.iconImg='el-icon-s-order'

        }else{
          this.displayMode='grid'
          this.showSearchImg=true
          this.iconImg='el-icon-picture'
        }
    },
        //单个状态变更
    changeChecked() {
      this.ids = this.fileList.filter(item => item.reviewChecked).map(item => item.id);
      this.imgUrls = this.fileList.filter(item => item.reviewChecked).map(item => item.thumbnailUrl);
      this.isCheckedAll = this.ids.length === this.fileList.length;
    },
    //批量选中
    handleSelectAll(reviewChecked) {
      this.fileList = this.fileList.map(item => ({ ...item, reviewChecked }));
      if (reviewChecked) {
        this.ids = this.fileList.map(item => item.id);
        this.imgUrls = this.fileList.map(item => item.thumbnailUrl);

      } else {
        this.ids = [];
      }
    },
    nextClick(page) {
      this.queryParams.id = this.fileList[this.fileList.length - 1].id
      this.isNext = 1;
    },
    prevClick(page) {
      this.queryParams.id = this.fileList[0].id
      this.isNext = 0;
    },
    parseTime(time, format) {
      if (!time) return '';
      const date = new Date(time);
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      const formatted = parts.map(part => part.value).join('');
      return formatted;
    },
    /** 查询任务图片对应关系列表 */
    getList() {
      this.loading = true;
      listFile(this.queryParams).then(response => {

        this.fileList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(
        (error) => {
          this.loading = false;
          this.resetQuery();
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        loginName: null,
        promptId: null,
        fileName: null,
        thumbnailName: null,
        fileUrl: null,
        thumbnailUrl: null,
        sensitiveMessage: null,
        width: null,
        height: null,
        likeNums: null,
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        del: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.loginNames = selection.map(item => item.loginName)
      this.imgUrls = selection.map(item => item.thumbnailUrl)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务图片对应关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      const loginName = row.loginName
      getFile(id, loginName).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改任务图片对应关系";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFile(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFile(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const loginNames = row.loginName || this.loginNames;
      this.$modal.confirm('是否确认删除任务图片对应关系编号为"' + ids + '"的数据项？').then(function () {
        return delFile(ids, loginNames);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('admin/file/export', {
        ...this.queryParams
      }, `file_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.grid {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

/* 为页面内容添加上边距，以避免与固定的按钮重叠 */
.content {
  margin-top: 50px;
  /* 可以根据固定工具栏的高度调整 */
}

.fixed-toolbar {
  position: fixed;
  left: 250px;
  right: 0;
  top:84px;
  background-color: #fff;
  padding: 10px;
  z-index: 101;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 防止内容溢出 */
}

.image-prompt {
  position: absolute;
  bottom: 5px;
  /* 提示词距离图片顶部的距离 */
  left: 5px;
  /* 提示词距离图片左边的距离 */
  background-color: rgba(0, 0, 0, 0.6);
  /* 半透明背景 */
  color: white;
  /* 白色字体 */
  padding: 5px;
  font-size: 12px;
  /* 字号偏小 */
  border-radius: 5px;
  /* 边角圆润 */
  display: none;
  /* 默认不显示 */
}

.image-container:hover .image-prompt {
  display: block;
  /* 鼠标悬浮时显示提示词 */
}

.fixed-toolbar1 {
  display: none;
}

@media screen and (min-width:1024px) and (max-width:1800px) {
  .content {
    margin-top: 70px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar1 {
    display: none;
  }
}

@media screen and (min-width:767px) and (max-width:1024px) {
  .grid {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .content {
    margin-top: 80px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar {
    left: 180px;
  }

  .fixed-toolbar1 {
    display: none;
  }
}

@media screen and (max-width:767px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .content {
    margin-top: 20px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar {
    display: none;

    left: 120px;
  }

  .fixed-toolbar1 {
    display: block;
  }
}

@media screen and (max-width:480px) {
  .grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .content {
    margin-top: 20px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar {
    display: none;
    left: 80px;


  }

  .fixed-toolbar1 {
    display: block;
  }
}

.grid-item {
  aspect-ratio: 1;
  font-size: 0;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.grid-item-checked {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  width: 75px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background-color: #000;
}

.el-checkbox {
  display: flex;
  align-items: center;
}

.no-data {
  text-align: center;
  font-size: 16px;
  color: #888;
  padding: 20px;
}
.item-img{
  right: 30px;

}
.publicType-label {
  position: absolute;
  top: 60px;
  right: 10px;
  color: white;
  font-size: 14px;
  padding: 2px 6px;
  border-radius: 4px;
  z-index: 100;
  padding: 5px;
}
</style>

