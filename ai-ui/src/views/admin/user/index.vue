<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="googleId" prop="googleId">
        <el-input v-model="queryParams.googleId" placeholder="请输入googleId" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="登录账号" prop="loginName">
        <el-input v-model="queryParams.loginName" placeholder="请输入登录账号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="用户邮箱" prop="email">
        <el-input v-model="queryParams.email" placeholder="请输入用户邮箱" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable>
          <el-option v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="注册用户所在国家" prop="registCountry">
        <el-input v-model="queryParams.registCountry" placeholder="请输入注册用户所在国家" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="是否进入黑名单" prop="blackListFlag">
        <el-select v-model="queryParams.blackListFlag" placeholder="请选择是否进入黑名单" clearable>
          <el-option v-for="dict in black_list_flag" :key="dict.value" :label="dict.key" :value="dict.value" />
        </el-select>
      </el-form-item>


      <el-form-item label="是否进入反馈黑名单" prop="blackListFlag">
        <el-select v-model="queryParams.contactBlackListFlag" placeholder="请选择是否进入反馈黑名单" clearable>
          <el-option v-for="dict in black_list_flag" :key="dict.value" :label="dict.key" :value="dict.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="用户是否逻辑删除" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择是否逻辑删除" clearable>
          <el-option v-for="dict in dict.type.is_del" :key="dict.value" :label="dict.key" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="会员类型" prop="vipType">
        <el-select v-model="queryParams.vipType" placeholder="请选择会员类型" clearable>
          <el-option v-for="dict in dict.type.vip_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="价格间隔" prop="priceInterval">
        <el-select v-model="queryParams.priceInterval" placeholder="请选择价格间隔" clearable>
          <el-option v-for="dict in dict.type.stripe_schedule_record_interval" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['admin:user:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['admin:user:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['admin:user:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['admin:user:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="id" />
      <el-table-column label="googleID" align="center" prop="googleId" />
      <el-table-column label="apple登录唯一标识" align="center" prop="appleId" />
      <el-table-column label="登录账号" align="center" prop="loginName" />
      <el-table-column label="用户昵称" align="center" prop="userName" />
      <el-table-column label="用户邮箱" align="center" prop="email" />
      <el-table-column label="性别" align="center" prop="sex">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="黑名单" align="center" prop="blackListFlag">
        <template slot-scope="scope">
          <dict-tag :options="black_list_flag" :value="scope.row.blackListFlag ? '是' : '否'" />
        </template>
      </el-table-column>
      <el-table-column label="反馈黑名单" align="center" prop="contactBlackListFlag">
        <template slot-scope="scope">
          <dict-tag :options="black_list_flag" :value="scope.row.contactBlackListFlag ? '是' : '否'" />
        </template>
      </el-table-column>
      <el-table-column label="生图总数(包括删除)" align="center" prop="totalImgNum" />
      <el-table-column label="注册国家" align="center" prop="registCountry" />
      <el-table-column label="逻辑删除" align="center" prop="delFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_del" :value="scope.row.delFlag" />
        </template>
      </el-table-column>
      <el-table-column label="头像图片缩略图" align="center" prop="thumbnailAvatarUrl" width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.thumbnailAvatarUrl">
            <image-preview :src="scope.row.thumbnailAvatarUrl" :width="50" :height="50" />
          </div>
          <div v-else>
            <span>用户暂无上传头像</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="用户相册已经上传了图片数量" align="center" prop="albumImgNum" />
      <el-table-column label="用户已收藏数量" align="center" prop="usedCollectNum" />
      <el-table-column label="会员类型" align="center" prop="vipType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vip_type" :value="scope.row.vipType" />
        </template>
      </el-table-column>
      <el-table-column label="每日免费点数" align="center" prop="dailyLumens" />
      <el-table-column label="每日已使用免费点数" align="center" prop="useDailyLumens" />
      <el-table-column label="价格间隔" align="center" prop="priceInterval">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.stripe_schedule_record_interval" :value="scope.row.priceInterval" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['admin:user:edit']">修改</el-button>
          <el-button  v-if="scope.row.vipType != 'basic'"  size="mini" type="text" icon="el-icon-edit" @click="handleLumens(scope.row)">lumen情况</el-button>
          <el-button  v-if="scope.row.vipType != 'basic'"  size="mini" type="text" icon="el-icon-edit" @click="handleLumenList(scope.row)">查看消费日志</el-button>
          <el-button v-if="scope.row.blackListFlag === false || scope.row.blackListFlag == null" size="mini" type="text"
            icon="el-icon-edit" @click="handleFlag(scope.row)" v-hasPermi="['admin:user:edit']">拉入黑名单
          </el-button>
          <el-button v-if="scope.row.blackListFlag === true" size="mini" type="text" icon="el-icon-edit"
            @click="cancelHandleFlag(scope.row)" v-hasPermi="['admin:user:edit']">取消黑名单
          </el-button>
          <el-button v-if="scope.row.contactBlackListFlag === false || scope.row.contactBlackListFlag == null" size="mini"
            type="text" icon="el-icon-edit" @click="handleContactFlag(scope.row)"
            v-hasPermi="['admin:user:edit']">拉入反馈黑名单
          </el-button>
          <el-button v-if="scope.row.contactBlackListFlag === true" size="mini" type="text" icon="el-icon-edit"
            @click="cancelhandleContactFlag(scope.row)" v-hasPermi="['admin:user:edit']">取消反馈黑名单
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['admin:user:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改用户信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="google登录唯一标识" prop="googleId">
          <el-input v-model="form.googleId" placeholder="请输入google登录唯一标识" />
        </el-form-item>
        <el-form-item label="登录账号" prop="loginName">
          <el-input v-model="form.loginName" placeholder="请输入登录账号" />
        </el-form-item>
        <el-form-item label="用户昵称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="用户邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入用户邮箱" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option v-for="dict in dict.type.sys_user_sex" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="帐号状态" prop="state">
          <el-select v-model="form.state" placeholder="请选择帐号状态">
            <el-option v-for="dict in dict.type.gpt_user_state" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="头像图片名称" prop="avatarName">
          <el-input v-model="form.avatarName" placeholder="请输入头像图片名称" />
        </el-form-item>
        <el-form-item label="头像图片缩略图名称" prop="thumbnailAvatarName">
          <el-input v-model="form.thumbnailAvatarName" placeholder="请输入头像图片缩略图名称" />
        </el-form-item>
        <el-form-item label="头像图片缩略图路径" prop="thumbnailAvatarUrl">
          <image-upload v-model="form.thumbnailAvatarUrl" />
        </el-form-item>
        <el-form-item label="用户相册已经上传了图片数量" prop="albumImgNum">
          <el-input v-model="form.albumImgNum" placeholder="请输入用户相册已经上传了图片数量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisible" title="查看Lumen消费日志" width="80%" :before-close="handleClose">
      <el-table :data="lumenCosts" stripe style="width: 100%">
        <el-table-column label="用户ID" prop="userId" />
        <el-table-column label="账号" prop="loginName" />
        <el-table-column label="任务id" prop="payLumenRecordId" />
        <!-- 任务ID 列，添加点击事件 -->
        <!-- <el-table-column label="任务id">
                    <template #default="{ row }">
                        <span style="color: blue; cursor: pointer;"
                            @click="viewRecord(row.payLumenRecordId, row.loginName)">
                            {{ row.payLumenRecordId }}
                        </span>
                    </template>
                </el-table-column> -->

        <el-table-column label="lumen原有数量" prop="lumenBefore" />
        <el-table-column label="Lumen消费" prop="lumenCost" />
        <el-table-column label="Lumen剩余" prop="lumenLeft" />
        <el-table-column label="创建时间" prop="createTime" />
      </el-table>

      <!-- 分页控件 -->
      <pagination layout="sizes, prev, next" :total="10000000" :page.sync="lumenCostQueryParams.pageNum"
        :page-sizes="[10, 20, 30, 50]" :limit.sync="lumenCostQueryParams.pageSize" @prev="prevClick" @next="nextClick"
        @pagination="queryLumenList()" />
    </el-dialog>


    <el-dialog :title="title" :visible.sync="handleLumenOpen" width="500px" append-to-body>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="每日免费点数日期" prop="dailyLumensTime">
          <el-input v-model="lumensForm.dailyLumensTime" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="每日免费点数" prop="dailyLumens">
          <el-input v-model="lumensForm.dailyLumens" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="会员点数" prop="vipLumens">
          <el-input v-model="lumensForm.vipLumens" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="充值点数" prop="rechargeLumens">
          <el-input v-model="lumensForm.rechargeLumens" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="剩余每日免费点数" prop="leftDailyLumens">
          <el-input v-model="lumensForm.leftDailyLumens" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="剩余会员点数" prop="leftVipLumens">
          <el-input v-model="lumensForm.leftVipLumens" :disabled="true" />
        </el-form-item>
      </el-form>
      <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
        <el-form-item label="剩余充值点数" prop="leftRechargeLumens">
          <el-input v-model="lumensForm.leftRechargeLumens" :disabled="true" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser } from "@/api/admin/user";
import { lumensMessage } from "@/api/orders/standards"
import { lumenList } from "@/api/orders/lumenRecord"

export default {
  name: "User",
  dicts: ['is_del', 'gpt_user_state', 'sys_user_sex', 'vip_type', 'stripe_schedule_record_interval',],
  data() {
    return {
      lumenCosts: [],
      handleLumenOpen:false,
     dialogVisible: false, // 控制对话框的显示
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户信息表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        googleId: null,
        loginName: null,
        userName: null,
        email: null,
        phoneNumber: null,
        sex: null,
        password: null,
        state: null,
        dailyCount: null,
        avatarName: null,
        thumbnailAvatarName: null,
        thumbnailAvatarUrl: null,
        albumImgNum: null,
        contactBlackListFlag: null,
        registCountry: null,
        delFlag: null
      },
      lumenCostQueryParams: {
                pageSize: 10,
                loginName: null,
                markFileId: null,
                isNext: null
            },
            lumensForm: {},

      black_list_flag: [{ key: "否", value: false }, { key: "是", value: true }],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    
    nextClick() {
            this.lumenCostQueryParams.markFileId = this.lumenCosts[this.lumenCosts.length - 1].id
            this.lumenCostQueryParams.isNext = true;
        },
        prevClick() {
            this.lumenCostQueryParams.markFileId = this.lumenCosts[0].id
            this.lumenCostQueryParams.isNext = false;
        },
        handleClose() {
            this.dialogVisible = false;
        },
    queryLumenList() {
      lumenList(this.lumenCostQueryParams).then(response => {
        if (response.rows.length === 0) {
          this.$modal.msgSuccess("未查询到数据 返回上个列表页面");
          return;
        }
        this.lumenCosts = response.rows;
      });;
    },
    handleLumenList(row) {
      this.lumenRow = row;
      this.dialogVisible = true;
      this.lumenCostQueryParams.markFileId = null;
      this.lumenCostQueryParams.isNext = null;

      this.lumenCostQueryParams.loginName = row.loginName;
      lumenList(this.lumenCostQueryParams).then(response => {
        this.lumenCosts = response.rows;
      });; // 查询用户的Lumen消费日志
    },
    handleLumens(row) {
      const id = row.id
      lumensMessage(id).then(response => {
        this.lumensForm = response.data;
        this.handleLumenOpen = true;
      });
    },
    /** 查询用户信息列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptId: null,
        microsoftId: null,
        appleId: null,
        facebookId: null,
        googleId: null,
        wechatOpenId: null,
        loginName: null,
        userName: null,
        userType: null,
        roleId: null,
        email: null,
        phoneNumber: null,
        sex: null,
        avatar: null,
        password: null,
        salt: null,
        state: null,
        delFlag: null,
        loginIp: null,
        loginDate: null,
        pwdUpdateDate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        permission: null,
        invitedBy: null,
        balance: null,
        gainedCount: null,
        usedCount: null,
        chargeCount: null,
        resetPasswordVcode: null,
        contextCount: null,
        contextMaxToken: null,
        usedWordsCount: null,
        vipValidStartTime: null,
        vipValidEndTime: null,
        gainedTokenCount: null,
        usedTokenCount: null,
        dailyCount: null,
        avatarName: null,
        thumbnailAvatarName: null,
        avatarUrl: null,
        thumbnailAvatarUrl: null,
        albumImgNum: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getUser(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除用户信息编号为"' + ids + '"的数据项？').then(function () {
        return delUser(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 拉黑用户 */
    handleFlag(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认拉黑用户信息编号为"' + ids + '"的数据项？').then(function () {
        const blackListFlagForm = {
          id: row.id, blackListFlag: true
        }
        return updateUser(blackListFlagForm);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("拉黑成功");
      }).catch(() => { });
    },
    /** 反馈黑名单拉黑*/
    handleContactFlag(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认拉黑用户信息编号为"' + ids + '"的数据项？').then(function () {
        const blackListFlagForm = {
          id: row.id, contactBlackListFlag: true
        }
        return updateUser(blackListFlagForm);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("拉黑成功");
      }).catch(() => { });
    },
    /** 反馈黑名单拉黑*/
    cancelhandleContactFlag(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认取消拉黑用户信息编号为"' + ids + '"的数据项？').then(function () {
        const blackListFlagForm = {
          id: row.id, contactBlackListFlag: false
        }
        return updateUser(blackListFlagForm);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("拉黑成功");
      }).catch(() => { });
    },
    cancelHandleFlag(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认取消拉黑用户信息编号为"' + ids + '"的数据项？').then(function () {
        const blackListFlagForm = {
          id: row.id, blackListFlag: false
        }
        return updateUser(blackListFlagForm);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("取消拉黑成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('admin/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
