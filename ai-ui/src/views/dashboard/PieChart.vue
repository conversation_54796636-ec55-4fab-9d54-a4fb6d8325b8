<template>
  <div :class="className" :style="{height:height,width:width}"/>
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { getMixLast } from "@/api/operation/mix";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    }
  },
  data() {
    return {
      chart: null,
      chartData: {
        chartCountOne: 0,
        chartCountTwo: 0,
        chartCountThree: 0,
        chartCountFour: 0
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.fetchData()
    })
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ chartCountOne, chartCountTwo, chartCountThree, chartCountFour }) {
      this.chart.setOption({
       title: {
          text: '昨日生图批次',
           textStyle: {
          color: '#000000' // 设置标题颜色为黑色
        }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '10',
          data: ['1图', '2图', '3图', '4图']
        },
        series: [
          {
            name: '图表生成情况',
            type: 'pie',
            // roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: [
              { value: chartCountOne, name: '1图' },
              { value: chartCountTwo, name: '2图' },
              { value: chartCountThree, name: '3图' },
              { value: chartCountFour, name: '4图' }
            ],
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    },
    fetchData() {
      getMixLast().then(response => {
        const data = response.data
        const chartCountOne = data.chartCountOne || 0
        const chartCountTwo = data.chartCountTwo || 0
        const chartCountThree = data.chartCountThree || 0
        const chartCountFour = data.chartCountFour || 0

        this.chartData = { chartCountOne, chartCountTwo, chartCountThree, chartCountFour }
        this.setOptions(this.chartData)
      }).catch(error => {
        console.error("Error fetching data: ", error)
      })
    }
  }
}
</script>
