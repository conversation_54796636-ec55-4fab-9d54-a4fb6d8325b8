<template>
  <div class="app-container">
    <!-- 表单部分 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模组组标识" prop="modelMark">
        <el-input v-model="queryParams.modelMark" placeholder="请输入模组组标识" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="模型组标识" prop="featureMark">
        <el-input v-model="queryParams.featureMark" placeholder="请输入模型组标识" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="规则状态" prop="ruleStatus">
        <el-select v-model="queryParams.ruleStatus" placeholder="请选择规则状态" clearable>
          <el-option v-for="dict in dict.type.sys_notice_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['task:rules:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['task:rules:export']"
        >导出</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" @click="getGuaranteesRule">设置保底策略</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" @click="openFairProportions">设置公平比例</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini"
          @click="openBlacklistFlux">设置黑名单规则(flux)</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini"
          @click="openBlacklistNoFlux">设置黑名单规则(非flux)</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini"
          @click="openAllFeatureServer">设置全能服务器</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格部分 -->
    <el-table v-loading="loading" :data="rulesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="模型组" align="center" prop="modelGroup" />
      <el-table-column label="模组组标识" align="center" prop="modelMark" />
      <el-table-column label="功能组" align="center" prop="featureGroup" />
      <el-table-column label="模型组标识" align="center" prop="featureMark" />
      <el-table-column label="实例集(多个英文逗号分隔)" align="center" prop="instance" />
      <el-table-column label="规则状态" align="center" prop="ruleStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_notice_status" :value="scope.row.ruleStatus" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['task:rules:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['task:rules:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!--新增编辑对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- 模型组下拉框 -->
        <el-form-item label="模型组" prop="modelGroup">
          <el-select v-model="form.modelGroup" placeholder="请选择模型组">
            <el-option v-for="group in modelGroups" :key="group" :label="group" :value="group" />
          </el-select>
        </el-form-item>

        <!-- 功能组下拉框，仅当模型组不为 m2 时显示 -->
        <el-form-item label="功能组" prop="featureGroup">
          <el-select v-model="form.featureGroup" placeholder="请选择功能组">
            <el-option v-for="group in featureGroups" :key="group" :label="group" :value="group" />
          </el-select>
        </el-form-item>

        <!-- 规则状态 -->
        <el-form-item label="规则状态" prop="ruleStatus">
          <el-radio-group v-model="form.ruleStatus">
            <el-radio v-for="dict in dict.type.sys_notice_status" :key="dict.value" :label="parseInt(dict.value)">{{
              dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
    <!-- 设置保底策略 弹窗-->
    <el-dialog :title="title" :visible.sync="openInstances" width="500px" append-to-body>
      <el-form ref="form" label-width="80px">
        <!-- 实例列表组下拉框 -->
        <el-form-item label="实例列表" prop="modelGroup">
          <el-select v-model="guaranteesRule.guarantees" multiple placeholder="请选择实例">
            <el-option v-for="group in guaranteesRule.instance" :key="group.instance" :label="group.mark"
              :value="group.instance" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setGuaranteesRule">设置保底策略</el-button>
        <el-button @click="cancelInstances">取消</el-button>
      </div>
    </el-dialog>
    <!-- 设置公平比例 弹窗-->
    <el-dialog :title="title" :visible.sync="openFair" width="500px" append-to-body>
      <el-form ref="fairProportions" label-width="150px">
        <el-form-item label="公平队列任务阈值" prop="fairTaskThreshold">
          <el-input v-model="fairProportions.fairTaskThreshold" placeholder="请输入公平队列任务阈值" />
        </el-form-item>
        <el-form-item label="非公平队列任务阈值" prop="unfairTaskThreshold">
          <el-input v-model="fairProportions.unfairTaskThreshold" placeholder="请输入非公平队列任务阈值" />
        </el-form-item>
        <el-form-item label="预载gpu队列任务阈值" prop="preloadingTaskThreshold">
          <el-input v-model="fairProportions.preloadingTaskThreshold" placeholder="请输入预载gpu队列任务阈值" />
        </el-form-item>
        <el-form-item label="阻塞等待时长，毫秒" prop="blobFairWaitTime">
          <el-input v-model="fairProportions.blobFairWaitTime" placeholder="请输入阻塞等待时长，毫秒" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="subFairProportion">设置公平比例</el-button>
        <el-button @click="cancelFair">取消</el-button>
      </div>
    </el-dialog>

    <!-- 设置黑名单规则 弹窗-->
    <el-dialog :title="title" :visible.sync="openBlacklistRule" width="500px" append-to-body>
      <el-form ref="form" label-width="80px">
        <!-- 实例列表组下拉框 -->
        <el-form-item label="实例列表" prop="modelGroup">
          <el-select v-model="blacklistRule.guarantees" multiple placeholder="请选择实例">
            <el-option v-for="group in blacklistRule.instance" :key="group.instance" :label="group.mark"
              :value="group.instance" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setBlacklistRule">设置黑名单规则</el-button>
        <el-button @click="canceBlacklistRule">取消</el-button>
      </div>
    </el-dialog>

    <!-- 设置全能服务器规则 弹窗-->
    <el-dialog :title="title" :visible.sync="openAllFeatureServerIds" width="500px" append-to-body>
      <el-form ref="form" label-width="80px">
        <!-- 实例列表组下拉框 -->
        <el-form-item label="实例列表" prop="modelGroup">
          <el-select v-model="allFeatureServer.guarantees" multiple placeholder="请选择实例">
            <el-option v-for="group in allFeatureServer.instance" :key="group.instance" :label="group.mark"
              :value="group.instance" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="setAllFeatureServer">设置全能服务器</el-button> 
        <el-button @click="canceAllFeatureServerIds">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRules, getRules, delRules, addRules,
  updateRules, getGuaranteesRule, setGuaranteesRule,
  getFairProportions, setFairProportions, getBlacklistRule,
  setBlacklistRule, getAllFeatureServerIds,setAllFeatureServerIds
} from "@/api/task/rules";
import { selectDistinctModelGroups } from "@/api/task/model";
import { getDistinctFeatureGroups } from "@/api/task/features";

export default {
  name: "Rules",
  dicts: ['sys_notice_status'],
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      isFlux: false,
      rulesList: [],
      title: "",
      open: false,
      openInstances: false,
      openFair: false,
      openBlacklistRule: false,
      openAllFeatureServerIds: false,
      form: {
        id: '',
        modelGroup: 'm2',
        featureGroup: '',
        ruleStatus: null,
      },
      guaranteesRule: {
        instance: [],
        guarantees: [],
      },
      blacklistRule: {
        instance: [],
        guarantees: [],
      },
      allFeatureServer: {
        instance: [],
        guarantees: [],
      },
      fairProportions: {},
      modelGroups: [],
      featureGroups: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        modelGroup: null,
        modelMark: null,
        featureGroup: null,
        featureMark: null,
        instance: null,
        ruleStatus: null
      },
      rules: {
        modelGroup: [
          { required: true, message: "模型组不能为空", trigger: "change" }
        ],
        // instance: [
        //   { required: true, message: "实例集(多个英文逗号分隔)不能为空", trigger: "blur" }
        // ],
      }
    };
  },
  created() {
    this.fetchModelGroups();
    this.fetchFeatureGroups();
    this.getList();
  },
  methods: {
    canceAllFeatureServerIds(){
      this.openAllFeatureServerIds = false;
      this.reset();
    },
    setAllFeatureServer(){
      const instance = this.allFeatureServer.guarantees;
      // console.log("选择："+instance)
      setAllFeatureServerIds(instance).then(response => {
        this.openAllFeatureServerIds = false;
        this.getList();
      })
    }, 
    async openAllFeatureServer() {
      try {
        this.openAllFeatureServerIds = true;
        const response = await getAllFeatureServerIds();
        this.isFlux = true;
        if (response.code === 200 && response.data) {
          this.allFeatureServer.instance = response.data.map(item => ({
            instance: item.instance,
            mark: item.mark,
          }));

          this.allFeatureServer.guarantees = response.data
            .filter(item => item.isGuarantees === 1)
            .map(item => item.instance);
        } else {
          console.error('获取模型组失败', response.msg);
        }
      } catch (error) {
        console.error('获取模型组失败', error);
      }
    },
    async getGuaranteesRule() {
      try {
        this.openInstances = true;
        const response = await getGuaranteesRule();

        if (response.code === 200 && response.data) {
          this.guaranteesRule.instance = response.data.map(item => ({
            instance: item.instance,
            mark: item.mark,
          }));

          this.guaranteesRule.guarantees = response.data
            .filter(item => item.isGuarantees === 1)
            .map(item => item.instance);
        } else {
          console.error('获取模型组失败', response.msg);
        }
      } catch (error) {
        console.error('获取模型组失败', error);
      }
    },
    async openBlacklistFlux() {
      try {
        this.openBlacklistRule = true;
        const response = await getBlacklistRule("flux");
        this.isFlux = true;
        if (response.code === 200 && response.data) {
          this.blacklistRule.instance = response.data.map(item => ({
            instance: item.instance,
            mark: item.mark,
          }));

          this.blacklistRule.guarantees = response.data
            .filter(item => item.isGuarantees === 1)
            .map(item => item.instance);
        } else {
          console.error('获取模型组失败', response.msg);
        }
      } catch (error) {
        console.error('获取模型组失败', error);
      }
    },
    async openBlacklistNoFlux() {
      try {
        this.openBlacklistRule = true;
        const response = await getBlacklistRule("c");
        this.isFlux = false;
        if (response.code === 200 && response.data) {
          this.blacklistRule.instance = response.data.map(item => ({
            instance: item.instance,
            mark: item.mark,
          }));

          this.blacklistRule.guarantees = response.data
            .filter(item => item.isGuarantees === 1)
            .map(item => item.instance);
        } else {
          console.error('获取模型组失败', response.msg);
        }
      } catch (error) {
        console.error('获取模型组失败', error);
      }
    },
    setBlacklistRule() {
      const instance = this.blacklistRule.guarantees.length >= 1 ? this.blacklistRule.guarantees : "  ";
      setBlacklistRule(instance, this.isFlux).then(response => {
        this.isFlux = false;
        this.openBlacklistRule = false;
        this.getList();
      })
    },
    async fetchModelGroups() {
      try {
        const response = await selectDistinctModelGroups();
        this.modelGroups = response.data;
      } catch (error) {
        console.error('获取模型组失败', error);
      }
    },
    async fetchFeatureGroups() {
      try {
        const response = await getDistinctFeatureGroups();
        this.featureGroups = response.data;
      } catch (error) {
        console.error('获取功能组失败', error);
      }
    },
    subFairProportion() {
      const fairProportionsDto = this.fairProportions;
      // console.log(fairProportionsDto);
      setFairProportions(fairProportionsDto).then(() => {
        this.openFair = false;
        this.getList();
      });
    },
    openFairProportions() {
      this.openFair = true;
      getFairProportions().then(response => {
        this.fairProportions = response;
      })
    },
    setGuaranteesRule() {
      const instance = this.guaranteesRule.guarantees;
      // console.log("选择："+instance)
      setGuaranteesRule(instance).then(response => {
        this.openInstances = false;
        this.getList();
      })
    },
    getList() {
      this.loading = true;
      listRules(this.queryParams).then(response => {
        this.rulesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelFair() {
      this.openFair = false;
      this.fairProportions = {};
    },
    cancelInstances() {
      this.openInstances = false;
      this.guaranteesRule = {
        instance: [],
        guarantees: [],
      }
    },
    canceBlacklistRule() {
      this.openBlacklistRule = false;
      this.isFlux = false;
      this.blacklistRule = {
        instance: [],
        guarantees: [],
      }
    },
    reset() {
      this.form = {
        id: null,
        modelGroup: null,
        modelMark: null,
        featureGroup: null,
        featureMark: null,
        instance: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        ruleStatus: null
      };
      this.resetForm("form");
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleAdd() {
      this.title = "新增规则";
      this.open = true;
      this.reset();
    },
    handleUpdate(row) {
      this.title = "修改规则";
      this.open = true;
      this.form = Object.assign({}, row);
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id) {
            updateRules(this.form).then(() => {
              this.open = false;
              this.getList();
            });
          } else {
            addRules(this.form).then(() => {
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        delRules(row.id).then(() => {
          this.getList();
        });
      });
    }
  }
};
</script>
