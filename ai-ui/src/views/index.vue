<template>
  <div class="app-container home">
  <el-row :gutter="40" class="panel-group">
          <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="navigateToUser">
          <div class="card-panel-icon-wrapper icon-peoples">
            <svg-icon icon-class="peoples" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              注册用户总数
            </div>
            <count-to :start-val="0" :end-val="Number(userCount)" :duration="3600" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="navigateToFile">
          <div class="card-panel-icon-wrapper icon-color">
            <svg-icon icon-class="color" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              生图总数
            </div>
            <count-to :start-val="0" :end-val="Number(fileCount)" :duration="3600" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="navigateToRecord">
          <div class="card-panel-icon-wrapper icon-component">
            <svg-icon icon-class="component" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
               生图总任务量
            </div>
            <count-to :start-val="0" :end-val="Number(recordCount)" :duration="3600" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
            <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="navigateToContacts">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="message" class-name="card-panel-icon"/>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
               用户总反馈量
            </div>
            <count-to :start-val="0" :end-val="Number(contactsCount)" :duration="3600" class="card-panel-num"/>
          </div>
        </div>
      </el-col>
    </el-row>
        <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <el-col :xs="24" :sm="24" :lg="12">
            <panel-group @handleSetLineChartData="handleSetLineChartData" style="height: 400px;" />

      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
                <div ref="barChart" class="chart-wrapper" style="height: 400px;"></div>
      </el-col>
    </el-row>
    <el-divider/>

    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper">
          <raddar-chart />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper">
          <pie-chart />
        </div>
      </el-col>
    </el-row>
    
    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <!-- Line Chart and Bar Chart in the same row -->
      <el-col :xs="24" :sm="24" :lg="12">
        <line-chart :chart-data="lineChartData"  />
      </el-col>
      <el-col :xs="24" :sm="24" :lg="12">
         <div class="chart-wrapper ">
          <bar-chart />
        </div>
        <!-- <div ref="barChart" class="chart-wrapper" style="height: 400px;"></div> -->
      </el-col>
    </el-row>
  </div>
</template>

<script>
import CountTo from 'vue-count-to'
import PanelGroup from './dashboard/PanelGroup'
import LineChart from './dashboard/LineChart'
import RaddarChart from './dashboard/RaddarChart'
import PieChart from './dashboard/PieChart'
import BarChart from './dashboard/BarChart'
import * as echarts from 'echarts'
import { getPromptFileRanking,getFileCount} from "@/api/admin/file";
import { getUserCount } from "@/api/admin/user";
import { getMixLast } from "@/api/operation/mix";
import {getContactsCount} from "@/api/admin/contacts";
import { getRecordCount } from "@/api/admin/record";

require('echarts/theme/macarons')

const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }

}

export default {
  name: "Index",
  components: {
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart,
    CountTo
  },
  data() {
    return {
      barChartOption1:[{"num":291,"loginName":"<EMAIL>"},
{"num":284,"loginName":"<EMAIL>"},
{"num":279,"loginName":"<EMAIL>"},
{"num":233,"loginName":"<EMAIL>"},
{"num":228,"loginName":"<EMAIL>"},
{"num":209,"loginName":"<EMAIL>"},
{"num":191,"loginName":"<EMAIL>"},
{"num":178,"loginName":"<EMAIL>"},
{"num":173,"loginName":"<EMAIL>"},
{"num":155,"loginName":"<EMAIL>"},
{"num":154,"loginName":"<EMAIL>"},
{"num":150,"loginName":"<EMAIL>"},
{"num":148,"loginName":"<EMAIL>"},
{"num":144,"loginName":"<EMAIL>"},
{"num":141,"loginName":"<EMAIL>"},
{"num":141,"loginName":"<EMAIL>"},
{"num":132,"loginName":"<EMAIL>"},
{"num":132,"loginName":"<EMAIL>"},
{"num":129,"loginName":"<EMAIL>"},
{"num":128,"loginName":"<EMAIL>"},
{"num":117,"loginName":"<EMAIL>"},
{"num":115,"loginName":"<EMAIL>"},
{"num":112,"loginName":"<EMAIL>"},
{"num":109,"loginName":"<EMAIL>"},
{"num":108,"loginName":"<EMAIL>"},
{"num":105,"loginName":"<EMAIL>"},
{"num":102,"loginName":"<EMAIL>"},
{"num":98,"loginName":"<EMAIL>"},
{"num":97,"loginName":"<EMAIL>"},
{"num":95,"loginName":"<EMAIL>"}],
      lineChartData: lineChartData.newVisitis,
      barChartOption: {
        title: {
          text: '昨日生图数量排名'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: []
        },
        series: [
          {
            name: '数量',
            type: 'bar',
            data: []
          }
        ]
      },
      contactsCount:0,
      userCount:0,
      fileCount:0,
      recordCount:0,
       mix: {
      },
    };
  },
  methods: {
        navigateToRecord() {
      this.$router.push('/data/record');
    },
       navigateToContacts() {
      this.$router.push('/data/contacts');
    },
   navigateToUser() {
      this.$router.push('/data/user');
    },
    getMixLast() {
      getMixLast(this.queryParams).then(response => {
        this.mix = response.data;
      });
    },
      navigateToFile() {
      this.$router.push('/data/file');
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    handleSetLineChartData(type) {
      this.lineChartData = lineChartData[type]
    },
    async fetchBarChartData() {
      try {
        const response = await getPromptFileRanking();
        const data = response.data;
      // const data = this.barChartOption1;
        // Update bar chart option with data from the API
        this.barChartOption.yAxis.data = data.map(item => item.loginName);
        this.barChartOption.series[0].data = data.map(item => item.num);

        this.initBarChart();
      } catch (error) {
        console.error('Error fetching bar chart data:', error);
      }
    },
    initBarChart() {
      const barChart = echarts.init(this.$refs.barChart)
      barChart.setOption(this.barChartOption)
    },
    async fetchUserCount(){
      try {
        const response = await getUserCount();
        this.userCount=response.data;
      } catch (error) {
        this.userCount=0;
        console.error('Error statistics  data:', error);
      }

    },
    async fetchContactsCount(){
      try {
        const response = await getContactsCount();
        this.contactsCount=response.data;
      } catch (error) {
        this.contactsCount=0;
        console.error('Error statistics  data:', error);
      }

    },
    async fetchFileCount(){
      try {
        const response = await getFileCount();
        this.fileCount=response.data;
      } catch (error) {
        this.fileCount=0;
        console.error('Error statistics  data:', error);
      }
    },
    async fetchRecordCount(){
      try {
        const response = await getRecordCount();
        this.recordCount=response.data;
      } catch (error) {
        this.recordCount=0;
        console.error('Error statistics  data:', error);
      }
    }
  },
  mounted() {
    this.fetchRecordCount();
    this.fetchContactsCount();
    this.getMixLast();
    this.fetchUserCount();
    this.fetchFileCount();
    this.fetchBarChartData();
  }
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .dashboard-editor-container {
    padding: 32px;
    background-color: rgb(240, 242, 245);
    position: relative;

    .chart-wrapper {
      background: #fff;
      padding: 16px 16px 0;
      margin-bottom: 32px;
    }
  }

.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-time {
        background: #40c9c6;
      }

      .icon-chart {
        background: #36a3f7;
      }

      .icon-color {
        background: #f4516c;
      }

      .icon-peoples {
        background: #34bfa3
      }
      .icon-component {
        background: #ee60c8;
      }
             .icon-message {
      background: #34bfa3;
    }
    }

    .icon-time {
      color: #40c9c6;
    }
        .icon-message {
      color: #34bfa3;
    }

    .icon-chart {
      color: #36a3f7;
    }

    .icon-color {
      color: #f4516c;
    }

    .icon-peoples {
      color: #34bfa3;
    }
       .icon-component {
        color: #ee60c8;
      }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

  @media (max-width:1024px) {
    .chart-wrapper {
      padding: 8px;
    }
  }
}
</style>