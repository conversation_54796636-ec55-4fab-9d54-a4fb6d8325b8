<template>
  <div class="app-container">
    <el-button class="fixed-toolbar1" type="primary" icon="el-icon-edit" circle
      @click="dialog = true, showSearch = true">
    </el-button>

    <div class="fixed-toolbar">

      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="用户账号" prop="loginName">
          <el-input v-model="queryParams.loginName" placeholder="请输入用户账号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="promptId" prop="promptId">
          <el-input v-model="queryParams.promptId" placeholder="请输入promptId" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="reviewStatus">
          <el-select v-model="queryParams.reviewStatus" placeholder="请选择状态" clearable>
            <el-option v-for="dict in dict.type.public_file_review_review_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="拒绝类型" prop="rejectionType">
          <el-select v-model="queryParams.rejectionType" placeholder="请选择拒绝类型" clearable>
            <el-option v-for="dict in dict.type.public_file_review_rejection_content" :key="dict.value"
              :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="公开类型(提示词)" prop="publicType">
          <el-select v-model="queryParams.publicType" placeholder="请选择公开类型" clearable>
            <el-option v-for="dict in dict.type.public_type" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="是否参与活动" prop="activityPosts">
          <el-select v-model="queryParams.activityPosts" placeholder="请选择是否参与" clearable>
            <el-option :label="'全部'" :value="null" />
            <el-option :label="'是'" :value="true" />
            <el-option :label="'否'" :value="false" />
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <!-- <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="ids.length == 0"
            @click="handleDelete" v-hasPermi="['operation:review:remove']">删除</el-button> -->
        </el-col>
        <el-col v-if="displayMode === 'grid'" :span="1.5">
          <el-checkbox size="mini" v-hasPermi="['operation:review:edit']" v-model="isCheckedAll" label="全选当页" border
            @change="handleSelectAll" />
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="ids.length == 0"
            @click="handlePassInBatches" v-hasPermi="['operation:review:edit']">批量通过</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="ids.length == 0"
            @click="handlePassInBatchesAndFeatured" v-hasPermi="['operation:review:edit']">通过并精选</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-ban" size="mini" :disabled="ids.length == 0"
            @click="handleRejectInBatches" v-hasPermi="['operation:review:edit']">批量拒绝</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        <el-tooltip class="item" effect="dark" :content="showSearchImg ? '图片展示' : '列表展示'" placement="top">
          <el-button size="mini" circle :icon=iconImg @click="toggleSearchImg()" style="float: right;" />
        </el-tooltip>
      </el-row>
    </div>
    <el-drawer :visible.sync="dialog" direction="ttb" custom-class="demo-drawer" ref="drawer">

      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="用户账号" prop="loginName">
          <el-input v-model="queryParams.loginName" placeholder="请输入用户账号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="promptId" prop="promptId">
          <el-input v-model="queryParams.promptId" placeholder="请输入promptId" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="reviewStatus">
          <el-select v-model="queryParams.reviewStatus" placeholder="请选择状态" clearable>
            <el-option v-for="dict in dict.type.public_file_review_review_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="拒绝类型" prop="rejectionType">
          <el-select v-model="queryParams.rejectionType" placeholder="请选择拒绝类型" clearable>
            <el-option v-for="dict in dict.type.public_file_review_rejection_content" :key="dict.value"
              :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="公开类型(提示词)" prop="publicType">
          <el-select v-model="queryParams.publicType" placeholder="请选择公开类型" clearable>
            <el-option v-for="dict in dict.type.public_type" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-select v-model="displayMode" placeholder="选择展示方式" style="margin-bottom: 10px; width: 200px;">
          <el-option label="图片展示" value="grid" />
          <el-option label="列表展示" value="table" />
        </el-select>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <!-- <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="ids.length == 0"
            @click="handleDelete" v-hasPermi="['operation:review:remove']">删除</el-button> -->
        </el-col>
        <el-col v-if="displayMode === 'grid'" :span="1.5">
          <el-checkbox size="mini" v-hasPermi="['operation:review:edit']" v-model="isCheckedAll" label="全选当页" border
            @change="handleSelectAll" />
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="ids.length == 0"
            @click="handlePassInBatches" v-hasPermi="['operation:review:edit']">批量通过</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="ids.length == 0"
            @click="handlePassInBatchesAndFeatured" v-hasPermi="['operation:review:edit']">通过并精选</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-ban" size="mini" :disabled="ids.length == 0"
            @click="handleRejectInBatches" v-hasPermi="['operation:review:edit']">批量拒绝</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>


    </el-drawer>
    <div class="content">

      <div v-if="displayMode === 'grid'" v-loading="loading" class="grid">
        <!-- 判断 reviewList 是否为空 -->
        <div v-if="reviewList.length === 0" class="no-data">暂无数据</div>

        <!-- 如果有数据，显示图片列表 -->
        <div v-else class="grid-item" v-for="(item) in reviewList" :key="item.id">
          <el-image class="grid-image" style="width: 100%; height: 100%" :src="item.highMiniUrl || item.thumbnailUrl"
            :preview-src-list="[item.highThumbnailUrl || item.thumbnailUrl || item.fileUrl]" fit="contain" lazy />

          <!-- 包裹选中框和模型名 -->
          <div class="grid-item-control">
            <el-checkbox class="grid-item-checked" v-model="item.reviewChecked" label="选中"
              @change="changeChecked"></el-checkbox>

            <div v-if="item.modelDisplay" class="model-display">
              {{ item.modelDisplay }}
            </div>
          </div>
        </div>
      </div>
      <el-table v-if="displayMode === 'table'" v-loading="loading" :data="reviewList"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="缩略图路径" align="center" prop="thumbnailUrl" width="100">
          <template slot-scope="scope">
            <image-preview :src="scope.row.thumbnailUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="用户账号" align="center" prop="loginName" />
        <el-table-column label="promptId" align="center" prop="promptId" />
        <el-table-column label="文件名称" align="center" prop="fileName" />
        <el-table-column label="状态" align="center" prop="reviewStatus">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.public_file_review_review_status" :value="scope.row.reviewStatus" />
          </template>
        </el-table-column>
        <el-table-column label="简要" align="center" prop="brief" />
        <el-table-column label="拒绝类型" align="center" prop="rejectionType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.public_file_review_rejection_content" :value="scope.row.rejectionType" />
          </template>
        </el-table-column>
        <el-table-column label="公开类型(提示词)" align="center" prop="publicType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.public_type" :value="scope.row.publicType" />
          </template>
        </el-table-column>
        <el-table-column label="拒绝内容描述" align="center" prop="rejectionContent" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核时间" align="center" prop="updateTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button v-if="scope.row.reviewStatus === 'review'" size="mini" type="text" icon="el-icon-edit"
              @click="handleUpdate(scope.row)" v-hasPermi="['operation:review:edit']">审核</el-button>
            <!-- <el-button v-if="scope.row.reviewStatus != 'pass'" size="mini" type="text" icon="el-icon-delete"
              @click="handleDelete(scope.row)" v-hasPermi="['operation:review:remove']">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
        :page-sizes="[10, 20, 30, 50, 100, 200]" :limit.sync="queryParams.pageSize" @pagination="getList" />

      <!-- 审核对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="用户账号" prop="loginName">
            <el-input v-model="form.loginName" :disabled=true />
          </el-form-item>

          <el-form-item label="缩略图(点击放大)" align="center" prop="thumbnailUrl" width="100">
            <template>
              <image-preview :src="form.thumbnailUrl" :width="350" :height="350" />
            </template>
          </el-form-item>

          <el-form-item label="状态" prop="reviewStatus">
            <el-select v-model="form.reviewStatus" placeholder="请选择状态">
              <el-option v-for="dict in dict.type.public_file_review_review_status" :key="dict.value"
                :label="dict.label" :value="dict.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.reviewStatus === 'rejection'" label="拒绝类型" prop="rejectionType">
            <el-select v-model="form.rejectionType" placeholder="请选择拒绝类型" @change="handleRejectionTypeChange">
              <el-option v-for="dict in dict.type.public_file_review_rejection_content" :key="dict.value"
                :label="dict.label" :value="dict.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.reviewStatus === 'rejection'" label="拒绝内容描述">
            <el-input v-model="form.rejectionContent" type="textarea" rows="4" placeholder="请输入拒绝内容"></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>


      <el-dialog :title="'批量拒绝'" :visible.sync="batchRejectDialogVisible" width="600px" append-to-body>
        <div v-if="selectedImages.length > 0">
          <div class="el-upload__tip" style="color:red" slot="tip">提示：只会处理待审核的数据,若你选择的数据中有已审核的数据，后台则会剔出来，只处理待审核的数据</div>
          <div class="image-gallery">
            <el-row :gutter="10">
              <el-col v-for="(image, index) in selectedImages" :key="index" :span="4">
                <el-card :body-style="{ padding: '20px' }">
                  <img :src="image.thumbnailUrl" alt="缩略图" width="100%" height="100" style="object-fit: cover;" />
                </el-card>
              </el-col>
            </el-row>
          </div>
          <el-form :model="batchRejectForm" ref="batchRejectForm" label-width="100px" size="small">
            <el-form-item label="拒绝类型" prop="rejectionType">
              <el-radio-group v-model="batchRejectForm.rejectionType" @change="handleRejectTypeChange">
                <el-radio v-for="dict in dict.type.public_file_review_rejection_content" :key="dict.value"
                  :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="拒绝内容">
              <el-input v-model="batchRejectForm.rejectionContent" type="textarea" rows="4"
                placeholder="请输入拒绝内容"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelBatchReject">取消</el-button>
          <el-button type="primary" @click="submitBatchReject">批量拒绝</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { listReview, getReview, delReview, addReview, updateReview, passInBatches, rejectInBatches } from "@/api/operation/review";

export default {
  name: "Review",
  dicts: ['public_file_review_rejection_content', 'public_file_review_display', 'public_file_review_review_status', 'public_type'],
  data() {
    return {
      displayMode: 'grid',
      dialog: false,
      showSearchImg: true,
      iconImg: 'el-icon-picture',
      isCheckedAll: false, //是否全选
      batchRejectDialogVisible: false,
      // 选中的图片列表
      selectedImages: [],
      // 批量拒绝表单数据
      batchRejectForm: {
        rejectionType: null, // 拒绝类型
        rejectionContent: '', // 拒绝内容
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 用户图片公开审核表格数据
      reviewList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 200,
        activityPosts: false, // 默认选中“全部”
        loginName: null,
        promptId: null,
        reviewStatus: null,
        isDisplay: null,
        rejectionType: null,
        publicType: null,
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    toggleSearchImg() {
      if (this.displayMode === 'grid') {
        this.displayMode = 'table'
        this.showSearchImg = false
        this.iconImg = 'el-icon-s-order'

      } else {
        this.displayMode = 'grid'
        this.showSearchImg = true
        this.iconImg = 'el-icon-picture'
      }
    },
    getLabelStyle(item) {
      // 根据 item.publicType 的值来动态设置背景色
      return {
        backgroundColor: item.publicType === 'everyone' ? 'rgba(44, 229, 12, 0.8)' : '#FFDD00'
      };
    },
    handleRejectionTypeChange(value) {
      // if (!this.form.rejectionContent) {
      // 如果拒绝内容为空，直接设置为当前选择的值
      this.form.rejectionContent = `${value}`;
      // } else if (!this.form.rejectionContent.includes(value)) {
      //   // 如果拒绝内容中不包含当前选择的值，则追加
      //   this.form.rejectionContent += `；${value}`;
      // }
    },
    handleRejectTypeChange(value) {
      this.batchRejectForm.rejectionContent = `${value}`;
    },
    // 选择批量拒绝的图片
    handleRejectInBatches() {
      if (this.ids.length > 100) {
        this.$modal.msgWarning("最多只允许100条数据批量拒绝");
        return;
      }

      // 获取选中的数据
      this.selectedImages = this.reviewList.filter(item => this.ids.includes(item.id));

      // 打开批量拒绝对话框
      this.batchRejectDialogVisible = true;
      this.batchRejectForm = {
        rejectionType: null, // 拒绝类型
        rejectionContent: '', // 拒绝内容
      }
    },
    //单个状态变更
    changeChecked() {
      this.ids = this.reviewList.filter(item => item.reviewChecked).map(item => item.id);
      this.isCheckedAll = this.ids.length === this.reviewList.length;
    },
    //批量选中
    handleSelectAll(reviewChecked) {
      this.reviewList = this.reviewList.map(item => ({ ...item, reviewChecked }));
      if (reviewChecked) {
        this.ids = this.reviewList.map(item => item.id);
      } else {
        this.ids = [];
      }
    },

    // 取消批量拒绝
    cancelBatchReject() {
      this.batchRejectDialogVisible = false;
      this.batchRejectForm = {
        rejectionType: null,
        rejectionContent: '',
      };
    },

    // // 提交批量拒绝
    // submitBatchReject() {
    //   const { rejectionType, rejectionContent } = this.batchRejectForm;
    //   if (!rejectionType || !rejectionContent) {
    //     this.$modal.msgWarning("拒绝类型和拒绝内容不能为空");
    //     return;
    //   }

    //   // 将拒绝内容和类型应用到选中的图片
    //   const rejectData = {
    //     ids: this.ids,
    //     rejectionType,
    //     rejectionContent,
    //   };

    //   console.log(rejectData);

    //   // 执行批量拒绝请求
    //   rejectInBatches(rejectData).then(() => {
    //     this.$modal.msgSuccess("批量拒绝成功");
    //     this.batchRejectDialogVisible = false;
    //     this.getList(); // 重新加载列表
    //   }).catch(() => {
    //     this.$modal.msgError("批量拒绝失败");
    //   });
    // },
    // 提交批量拒绝
    submitBatchReject() {
      const { rejectionType, rejectionContent } = this.batchRejectForm;
      if (!rejectionType || !rejectionContent) {
        this.$modal.msgWarning("拒绝类型和拒绝内容不能为空");
        return;
      }

      if (!this.ids || this.ids.length === 0) {
        this.$modal.msgWarning("请选择要拒绝的记录");
        return;
      }

      this.loading = true; // 开始加载

      // 拆分成每组最多20条
      const batchSize = 20;
      const batches = [];
      for (let i = 0; i < this.ids.length; i += batchSize) {
        batches.push(this.ids.slice(i, i + batchSize));
      }

      // 定义一个处理函数来按顺序执行
      const processBatches = async () => {
        try {
          for (const batch of batches) {
            const rejectData = {
              ids: batch,
              rejectionType,
              rejectionContent,
            };
            await rejectInBatches(rejectData);
          }
          this.$modal.msgSuccess("批量拒绝成功");
          this.batchRejectDialogVisible = false;
          this.getList();
        } catch (error) {
          this.$modal.msgError("批量拒绝失败");
        } finally {
          this.loading = false; // 所有请求完成或失败
        }
      };

      processBatches();
    },

    parseTime(time, format) {
      if (!time) return '';
      const date = new Date(time);
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      };
      const formatter = new Intl.DateTimeFormat('zh-CN', options);
      const parts = formatter.formatToParts(date);
      const formatted = parts.map(part => part.value).join('');
      return formatted;
    },

    /** 查询用户图片公开审核列表 */
    getList() {
      this.loading = true;
      listReview(this.queryParams).then(response => {
        this.reviewList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.ids = [];
        this.isCheckedAll = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        loginName: null,
        promptId: null,
        fileName: null,
        reviewStatus: null,
        isDisplay: null,
        rejectionType: null,
        rejectionContent: '',
        createTime: null,
        updateTime: null,
        createBy: null,
        updateBy: null,
        del: null,
        thumbnailUrl: null,
        publicType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户图片公开审核";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getReview(id).then(response => {
        this.form = response.data;
        this.form.thumbnailUrl = row.thumbnailUrl;
        this.open = true;
        this.title = "修改用户图片公开审核";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            console.log(this.form.rejectionContent)
            if (this.form.reviewStatus === 'rejection') {
              if (!this.form.rejectionContent || this.form.rejectionContent.length <= 0) {
                this.$modal.msgSuccess("拒绝描述不能为空");
                return;
              }
            }
            updateReview(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReview(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除用户图片公开审核编号为"' + ids + '"的数据项？').then(function () {
        return delReview(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    handlePassInBatches() {
      if (this.ids.length > 200) {
        this.$modal.msgSuccess("最多只允许200条数据批量通过");
        return;
      }

      const ids = this.ids;
      const featured = false;
      this.$modal.confirm('是否确认批量通过您所选的数据项？').then(() => {
        this.loading = true;  // 开始加载时显示 loading 状态

        const batchSize = 20; // 每次处理 20 条数据
        const totalBatches = Math.ceil(ids.length / batchSize); // 计算总共需要多少批次

        // 使用一个递归函数来逐批处理
        const processBatch = (batchIndex = 0) => {
          if (batchIndex >= totalBatches) {
            this.loading = false;  // 所有批次处理完成后，隐藏 loading
            this.isCheckedAll = false; // 循环结束后将全选状态设置为 false
            this.$modal.msgSuccess("通过成功");
            this.getList();  // 更新列表
            return;
          }

          // 获取当前批次的 ids
          const batchIds = ids.slice(batchIndex * batchSize, (batchIndex + 1) * batchSize);
          const activity = false;
          // 调用批量通过接口
          passInBatches(batchIds, featured, activity).then(() => {
            // 递归调用，继续处理下一批次
            processBatch(batchIndex + 1);
          }).catch(() => {
            this.loading = false;  // 如果失败也需要停止 loading
            this.isCheckedAll = false;
          });
        };

        // 开始批量处理
        processBatch();
      }).catch(() => {
        // 用户取消操作时的逻辑
      });


    },
    handlePassInBatchesAndFeatured() {
      if (this.ids.length > 20) {

        this.$modal.msgSuccess("最多只允许20条数据批量通过并精选");
        return;
      }
      const ids = this.ids;
      const featured = true;
      const activity = false;
      this.$modal.confirm('是否确认批量通过并精选您所选的数据项？').then(function () {
        return passInBatches(ids, featured, activity);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("通过并精选成功");
      }).catch(() => { });

    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/review/export', {
        ...this.queryParams
      }, `review_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style scoped>
.grid {
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

/* 为页面内容添加上边距，以避免与固定的按钮重叠 */
.content {
  margin-top: 50px;
  /* 可以根据固定工具栏的高度调整 */
}

.fixed-toolbar {
  position: fixed;
  left: 250px;
  right: 0;
  top: 84px;
  background-color: #fff;
  padding: 10px;
  z-index: 101;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 防止内容溢出 */
}

.image-prompt {
  position: absolute;
  bottom: 5px;
  /* 提示词距离图片顶部的距离 */
  left: 5px;
  /* 提示词距离图片左边的距离 */
  background-color: rgba(0, 0, 0, 0.6);
  /* 半透明背景 */
  color: white;
  /* 白色字体 */
  padding: 5px;
  font-size: 12px;
  /* 字号偏小 */
  border-radius: 5px;
  /* 边角圆润 */
  display: none;
  /* 默认不显示 */
}

.image-container:hover .image-prompt {
  display: block;
  /* 鼠标悬浮时显示提示词 */
}

.fixed-toolbar1 {
  display: none;
}

@media screen and (min-width:1024px) and (max-width:1800px) {
  .content {
    margin-top: 70px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar1 {
    display: none;
  }
}

@media screen and (min-width:767px) and (max-width:1024px) {
  .grid {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .content {
    margin-top: 80px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar {
    left: 180px;
  }

  .fixed-toolbar1 {
    display: none;
  }
}

@media screen and (max-width:767px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .content {
    margin-top: 20px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar {
    display: none;

    left: 120px;
  }

  .fixed-toolbar1 {
    display: block;
  }
}

@media screen and (max-width:480px) {
  .grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .content {
    margin-top: 20px;
    /* 可以根据固定工具栏的高度调整 */
  }

  .fixed-toolbar {
    display: none;
    left: 80px;


  }

  .fixed-toolbar1 {
    display: block;
  }
}

.grid-item {
  aspect-ratio: 1;
  font-size: 0;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

/* 控制整个悬浮区域的位置 */
.grid-item-control {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 选中框样式 */
.grid-item-checked {
  width: 75px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  background-color: #000;
  color: #fff;
}

/* 模型名称样式 */
.model-display {
  margin-top: 4px;
  font-size: 12px;
  color: #eee;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  border-radius: 4px;
  max-width: 80px;
  text-align: center;
  word-break: break-word;
}

.el-checkbox {
  display: flex;
  align-items: center;
}

.no-data {
  text-align: center;
  font-size: 16px;
  color: #888;
  padding: 20px;
}

.item-img {
  right: 30px;

}

.publicType-label {
  position: absolute;
  top: 60px;
  right: 10px;
  color: white;
  font-size: 14px;
  padding: 2px 6px;
  border-radius: 4px;
  z-index: 100;
  padding: 5px;
}
</style>
