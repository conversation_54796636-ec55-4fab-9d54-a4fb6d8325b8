<template>
  <div class="personal-analysis">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>piclumen用户行为分析BI</h2>
      </el-col>
    </el-row>
    <el-divider />
    <el-row :gutter="40" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
        <div class="card-panel" @click="navigateToMix">
          <div class="card-panel-icon-wrapper icon-rate">
            <svg-icon icon-class="rate" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              昨日最喜爱单批次数量
            </div>
            <div class="card-panel-num">
              <count-to :start-val="0" :end-val="Number(maxChartCount)" :duration="2600" />
              <span class="batch-info">
                ({{ batchLabel }})
              </span>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
        <div class="card-panel" @click="navigateToMix">
          <div class="card-panel-icon-wrapper icon-code">
            <svg-icon icon-class="code" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              昨日最喜爱生图比例
            </div>
            <h2>{{ mix.favoriteAspectRatio }}</h2>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
        <div class="card-panel" @click="navigateToMix">
          <div class="card-panel-icon-wrapper icon-color">
            <svg-icon icon-class="color" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              昨日色情提示词过滤次数
            </div>
            <count-to :start-val="0" :end-val="Number(chirdChildPornCount) || 0" :duration="3200" class="card-panel-num" />
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
        <div class="card-panel" @click="navigateToMix">
          <div class="card-panel-icon-wrapper icon-message">
            <svg-icon icon-class="message" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              昨日质量生图任务数
            </div>
            <count-to :start-val="0" :end-val="Number(mix.text2picTasks) - Number(mix.fastTasks)" :duration="3600"
              class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
        <div class="card-panel" @click="navigateToMix">
          <div class="card-panel-icon-wrapper icon-rate">
            <svg-icon icon-class="rate" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              昨日快速生图任务数
            </div>
            <count-to :start-val="0" :end-val="Number(mix.fastTasks)" :duration="3600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col">
        <div class="card-panel" @click="navigateToMix">
          <div class="card-panel-icon-wrapper icon-color">
            <svg-icon icon-class="color" class-name="card-panel-icon" />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">
              昨日图生图任务数
            </div>
            <count-to :start-val="0" :end-val="Number(mix.text2picTasks)" :duration="3600" class="card-panel-num" />
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="chart-container">
      <el-select v-model="selectedType1" @change="fetchData1" placeholder="请选择维度">
        <el-option label="最近7天" value="1"></el-option>
        <el-option label="最近14天" value="4"></el-option>
        <el-option label="最近30天" value="2"></el-option>
      </el-select>
      <div class="echart-header">
        <h3>文生图用户行为分析</h3>
      </div>
      <div ref="echartContainer1" class="echart-container"></div>
    </div>
    <div class="chart-container">
      <el-select v-model="selectedType2" @change="fetchData2" placeholder="请选择维度">
        <el-option label="最近7天" value="1"></el-option>
        <el-option label="最近14天" value="4"></el-option>
        <el-option label="最近30天" value="2"></el-option>
      </el-select>
      <div class="echart-header">
        <h3>图生图用户行为分析</h3>
      </div>
      <div ref="echartContainer2" class="echart-container"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // 引入主题
import { getKpiMixWeek } from "@/api/operation/mix"; // 修改为你的API路径
import { getMixLast } from "@/api/operation/mix";
import { getChildPornCountByDate } from "@/api/task/cache";

import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      chirdChildPornCount: 0, 
      maxdata1: 0,
      maxdata2: 0,
      batchLabel: '',
      selectedType1: "1",
      selectedType2: "1",
      maxChartCount: '1000',
      chart1: null,
      chart2: null,
      mix: {},
      data1: [], // Store data for chart1
      data2: [], // Store data for chart2
      dates1: [], // Store dates for x-axis
      dates2: [], // Store dates for x-axis
    };
  },
  mounted() {
    this.initChart1();
    this.initChart2();
    window.addEventListener("resize", this.handleResize);
    this.fetchData1(); // Fetch data on initial load
    this.fetchData2(); // Fetch data on initial load
    this.getMixLast();
    this.yesterdayChirdChildPornCount();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.chart1) {
      this.chart1.dispose();
    }
    if (this.chart2) {
      this.chart2.dispose();
    }
  },
  methods: {
    yesterdayChirdChildPornCount() {
      // 获取昨天的日期
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      // 格式化为 YYYY-MM-DD 的字符串（可以根据需要调整格式）
      const formattedDate = yesterday.toISOString().split('T')[0];
      getChildPornCountByDate(formattedDate).then((response) => {
        this.chirdChildPornCount = response.data;
      });
    },
    navigateToMix() {
      this.$router.push('/operation/mix');
    },
    getMixLast() {
      getMixLast(this.queryParams).then(response => {
        this.mix = response.data;
        this.maxChartCount = Math.max(this.mix.chartCountOne, this.mix.chartCountTwo, this.mix.chartCountThree, this.mix.chartCountFour)
        this.setBatchLabel(); // 更新批次标签

      });
    },
    setBatchLabel() {
      if (this.mix.chartCountOne == this.maxChartCount) {
        this.batchLabel = '1张批次';
      } else if (this.mix.chartCountTwo == this.maxChartCount) {
        this.batchLabel = '2张批次';
      } else if (this.mix.chartCountThree == this.maxChartCount) {
        this.batchLabel = '3张批次';
      } else if (this.mix.chartCountFour == this.maxChartCount) {
        this.batchLabel = '4张批次';
      } else {
        this.batchLabel = '未知批次';
      }
      console.log('批次名：' + this.batchLabel);
    },
    initChart1() {
      this.chart1 = echarts.init(this.$refs.echartContainer1, "macarons");
      // this.updateChart1();
    },
    initChart2() {
      this.chart2 = echarts.init(this.$refs.echartContainer2, "macarons");
      // this.updateChart2();
    },
    updateChart1() {
      this.initChart1();

      const option1 = {
        legend: {},
        tooltip: {
          trigger: "axis",
          showContent: false,
        },
        dataset: {
          source: [
            ["product", ...this.dates1],
            [
              "Realistic生图数量",
              ...this.data1.map((item) => item.realisticCount),
            ],
            [
              "Anime生图数量",
              ...this.data1.map((item) => item.animeCount),
            ],
            [
              "Lineart生图数量",
              ...this.data1.map((item) => item.lineartCount),
            ],
            [
              "flux 任务数",
              ...this.data1.map((item) => item.fluxCount),
            ],
            [
              "pony 任务数",
              ...this.data1.map((item) => item.ponyCount),
            ],
            [
              "art 任务数",
              ...this.data1.map((item) => item.artCount),
            ],
          ],
        },
        xAxis: { type: "category" },
        yAxis: { gridIndex: 0 },
        grid: { top: "55%" },
        series: [
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "pie",
            id: "pie",
            radius: "30%",
            center: ["50%", "25%"],
            emphasis: { focus: "self" },
            label: { formatter: `{b}: {@${this.dates1[this.maxdata1]}} ({d}%)` },
            encode: { itemName: "product", value: this.dates1[this.maxdata1], tooltip: this.dates1[this.maxdata1] },
          },
        ],
      };

      this.chart1.setOption(option1);

      this.chart1.on("updateAxisPointer", (event) => {
        const xAxisInfo = event.axesInfo[0];
        if (xAxisInfo) {
          const dimension = xAxisInfo.value + 1;
          this.chart1.setOption({
            series: {
              id: "pie",
              label: { formatter: `{b}: {@[${dimension}]} ({d}%)` },
              encode: { value: dimension, tooltip: dimension },
            },
          });
        }
      });
    },
    updateChart2() {
      this.initChart2();
      const option2 = {
        legend: {},
        tooltip: {
          trigger: "axis",
          showContent: false,
        },
        dataset: {
          source: [
            ["product", ...this.dates2],
            [
              "Content Refer生图数量",
              ...this.data2.map((item) => item.pic2picContentRef),
            ],
            [
              "Style Refer生图数量",
              ...this.data2.map((item) => item.pic2picStyleRef),
            ],
            [
              "Character Refer生图数量",
              ...this.data2.map((item) => item.pic2picCharacterRef),
            ],
          ],
        },
        xAxis: { type: "category" },
        yAxis: { gridIndex: 0 },
        grid: { top: "55%" },
        series: [
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "line",
            smooth: true,
            seriesLayoutBy: "row",
            emphasis: { focus: "series" },
          },
          {
            type: "pie",
            id: "pie",
            radius: "30%",
            center: ["50%", "25%"],
            emphasis: { focus: "self" },
            label: { formatter: `{b}: {@${this.dates2[this.maxdata2]}} ({d}%)` },
            encode: { itemName: "product", value: this.dates2[this.maxdata2], tooltip: this.dates2[this.maxdata2] },
          },
        ],
      };

      this.chart2.setOption(option2);

      this.chart2.on("updateAxisPointer", (event) => {
        const xAxisInfo = event.axesInfo[0];
        if (xAxisInfo) {
          const dimension = xAxisInfo.value + 1;
          this.chart2.setOption({
            series: {
              id: "pie",
              label: { formatter: `{b}: {@[${dimension}]} ({d}%)` },
              encode: { value: dimension, tooltip: dimension },
            },
          });
        }
      });
    },
    fetchData1() {
      getKpiMixWeek(this.selectedType1).then((response) => {
        const data = response.data; // Adjust this according to your API response structure
        this.data1 = data;
        this.dates1 = data.map((item) => item.recordDate); // Set x-axis labels
        this.maxdata1 = this.dates1.length - 1
        console.log("dates1:" + this.dates1)


        this.updateChart1();
      });
    },
    fetchData2() {
      getKpiMixWeek(this.selectedType2).then((response) => {
        const data = response.data; // Adjust this according to your API response structure
        this.data2 = data;
        this.dates2 = data.map((item) => item.recordDate); // Set x-axis labels
        this.maxdata2 = this.dates2.length - 1
        this.updateChart2();
      });
    },
    handleResize() {
      if (this.chart1) {
        this.chart1.resize();
      }
      if (this.chart2) {
        this.chart2.resize();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.personal-analysis {
  width: 100%;
  height: 140vh;
  color: #000;
}

.chart-container {
  margin-bottom: 40px;
}

.echart-container {
  width: 100%;
  height: 400px;
  /* 根据需要调整高度 */
}

.echart-header {
  text-align: center;
  margin-bottom: 10px;
  /* 适当调整标题与图表之间的间距 */
}

h3 {
  margin: 0;
  /* 移除标题的默认外边距 */
}

.card-panel-col {
  margin-bottom: 32px;
}

.card-panel {
  height: 108px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
  border-color: rgba(0, 0, 0, .05);

  &:hover {
    .card-panel-icon-wrapper {
      color: #fff;
    }

    .icon-rate {
      background: #40c9c6;
    }

    .icon-color {
      background: #36a3f7;
    }

    .icon-code {
      background: #f4516c;
    }

    .icon-message {
      background: #34bfa3
    }

    .icon-email {
      background: #6586f5;
    }
  }

  .icon-rate {
    color: #40c9c6;
  }

  .icon-color {
    color: #36a3f7;
  }

  .icon-code {
    color: #f4516c;
  }

  .icon-message {
    color: #34bfa3;
  }



  .card-panel-icon-wrapper {
    float: left;
    margin: 14px 0 0 14px;
    padding: 16px;
    transition: all 0.38s ease-out;
    border-radius: 6px;
  }

  .card-panel-icon {
    float: left;
    font-size: 48px;
  }

  .card-panel-description {
    float: right;
    font-weight: bold;
    margin: 26px;
    margin-left: 0px;

    .card-panel-text {
      line-height: 18px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 10px;
      margin-bottom: 12px;
    }

    .card-panel-num {
      font-size: 20px;
    }

    .batch-info {
      color: #666;
      font-size: 14px;
    }
  }
}
</style>
