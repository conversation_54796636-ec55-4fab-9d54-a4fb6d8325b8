<template>
  <div class="personal-analysis">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>piclumen性能数据统计BI</h2>
      </el-col>
    </el-row>
    <el-divider />
    <el-select v-model="selectedType1" @change="fetchData1" placeholder="请选择维度">
      <el-option label="最近7天" value="1"></el-option>
      <el-option label="最近30天" value="2"></el-option>
    </el-select>
    <div :class="className" :style="{ height: height, width: width }" ref="chartContainer"></div>
    <el-divider />
    <el-row :gutter="20">
      <el-col :span="12">
        <el-select v-model="selectedMetric1" placeholder="选择指标" @change="updateChart1">
          <el-option label="昨日用户平均等待时间" value="avgWait"></el-option>
          <el-option label="昨日用户任务平均完成时间" value="avgChartTime"></el-option>
          <el-option label="昨日用户单图(Fast)平均完成时间" value="fastOnePicTime"></el-option>
          <el-option label="昨日用户单图(Relax)平均完成时间" value="relaxOnePicTime"></el-option>
        </el-select>
        <el-select v-model="selectedType2" @change="fetchData2" placeholder="请选择维度">
          <el-option label="最近7天" value="1"></el-option>
          <el-option label="最近30天" value="2"></el-option>
          <el-option label="最近三个月" value="3"></el-option>
        </el-select>
        <div :class="className" :style="{ height: height, width: width }" ref="chartContainer1"></div>
      </el-col>
      <el-col :span="12">
        <el-select v-model="selectedMetric2" placeholder="选择指标" @change="updateChart2">
          <el-option label="昨日用户平均生图数量" value="avgChartsPerUser"></el-option>
          <el-option label="昨日最大并发数量" value="maxConcurrentChartTasks"></el-option>
          <el-option label="昨日用户最大生图数量" value="maxChartsPerUser"></el-option>
        </el-select>
        <el-select v-model="selectedType3" @change="fetchData3" placeholder="请选择维度">
          <el-option label="最近7天" value="1"></el-option>
          <el-option label="最近30天" value="2"></el-option>
          <el-option label="最近三个月" value="3"></el-option>
        </el-select>
        <div :class="className" :style="{ height: height, width: width }" ref="chartContainer2"></div>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-select v-model="selectedType4" @change="fetchData4" placeholder="请选择维度">
          <el-option label="最近7天" value="1"></el-option>
          <el-option label="最近30天" value="2"></el-option>
          <el-option label="最近三个月" value="3"></el-option>
        </el-select>
        <div :class="className" :style="{ height: height, width: width }" ref="chartContainer3"></div>
      </el-col>
      <el-col :span="12">
        <el-select v-model="selectedType5" @change="fetchData5" placeholder="请选择维度">
          <el-option label="最近7天" value="1"></el-option>
          <el-option label="最近30天" value="2"></el-option>
          <el-option label="最近三个月" value="3"></el-option>
        </el-select>
        <div :class="className" :style="{ height: height, width: width }" ref="chartContainer4"></div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // 引入主题
import { getKpiMixWeek } from "@/api/operation/mix"; // 修改为你的API路径
import { getKpiDayMaxTaskSizeLast } from "@/api/operation/size"
export default {
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "350px",
    }
  },
  data() {
    return {
      chart: null,
      chart1: null,
      chart2: null,
      chart3: null,
      selectedMetric1: 'avgWait',
      selectedMetric2: 'avgChartsPerUser',
      selectedType1: '1',
      selectedType2: '1',
      selectedType3: '1',
      selectedType4: '1',
      selectedType5: '1',
      data1: {},
      data2: {},
      data3: {},
      data4: {},
      xAxisData1: [],
      xAxisData2: [],
      xAxisData3: [],
      xAxisData4: [],
      xAxisData5: [], 
      seriesDate4:[
      ],
      sizeList: [],
    };
  },
  mounted() {
    this.fetchData1();
    this.fetchData2();
    this.fetchData3();
    this.fetchData4();
    this.fetchData5();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    if (this.chart1) {
      this.chart1.dispose();
      this.chart1 = null;
    }
    if (this.chart2) {
      this.chart2.dispose();
      this.chart2 = null;
    }
    if (this.chart3){
      this.chart3.dispose();
      this.chart3 = null;

    }
  },
  methods: {
    fetchData1() {
      getKpiMixWeek(this.selectedType1).then(response => {
        const result = response.data;
        this.xAxisData1 = result.map(item => item.recordDate);
        this.data1 = {
          avgChartOneTime: result.map(item => item.avgChartOneTime),
          avgChartTwoTime: result.map(item => item.avgChartTwoTime),
          avgChartThreeTime: result.map(item => item.avgChartThreeTime),
          avgChartFourTime: result.map(item => item.avgChartFourTime)
        };
        this.initChart();
      });
    },
    fetchData2() {
      getKpiMixWeek(this.selectedType2).then(response => {
        const result = response.data;
        this.xAxisData2 = result.map(item => item.recordDate);
        this.data2 = {
          avgWait: result.map(item => item.avgWait),
          avgChartTime: result.map(item => item.avgChartTime),
          // avgPerChartTime: result.map(item => item.avgPerChartTime),
          fastOnePicTime: result.map(item => item.fastOnePicTime),
          relaxOnePicTime: result.map(item => item.relaxOnePicTime)
        };
        this.initChart1();
      });
    },
    fetchData3() {
      getKpiMixWeek(this.selectedType3).then(response => {
        const result = response.data;
        this.xAxisData3 = result.map(item => item.recordDate);
        this.data3 = {
          avgChartsPerUser: result.map(item => item.avgChartsPerUser),
          maxConcurrentChartTasks: result.map(item => item.maxConcurrentChartTasks),
          maxChartsPerUser: result.map(item => item.maxChartsPerUser)
        };
        this.initChart2();
      });
    },
    fetchData4(){
      getKpiDayMaxTaskSizeLast(this.selectedType4,true).then(response =>{
        const result = response.data;
        this.sizeList= result;
        this.xAxisData4 = result.map(item => item.date);
        this.initChart3();

      })

    },
    fetchData5(){
      getKpiDayMaxTaskSizeLast(this.selectedType5,false).then(response =>{
        const result = response.data;
        this.sizeList1= result;
        this.xAxisData5 = result.map(item => item.date);
        this.initChart4();

      })
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer, "macarons");
      this.setBarChartOptions();
    },
    initChart1() {
      this.chart1 = echarts.init(this.$refs.chartContainer1, "macarons");
      this.updateChart1();
    },
    initChart2() {
      this.chart2 = echarts.init(this.$refs.chartContainer2, "macarons");
      this.updateChart2();
    },
    initChart3() {
      this.chart3 = echarts.init(this.$refs.chartContainer3, "macarons");
      this.updateChart3();
    },
    initChart4() {
      this.chart4 = echarts.init(this.$refs.chartContainer4, "macarons");
      this.updateChart4();
    },
    setBarChartOptions() {
      const labelOption = {
        show: false,
        position: 'insideBottom',
        distance: 15,
        align: 'left',
        verticalAlign: 'middle',
        rotate: 90,
        formatter: '{c}  {name|{a}}',
        fontSize: 16,
        rich: {
          name: {}
        }
      };
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['平均生图时间(每批1张)', '平均生图时间(每批2张)', '平均生图时间(每批3张)', '平均生图时间(每批4张)']
        },
        toolbox: {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'center',
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar', 'stack'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            data: this.xAxisData1
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位：秒',
            nameTextStyle: {
              fontSize: 12,
              padding: [0, 0, 10, 0] // Adjust padding as needed
            }
          }
        ],
        series: [
          {
            name: '平均生图时间(每批1张)',
            type: 'bar',
            barGap: 0,
            label: labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.data1.avgChartOneTime
          },
          {
            name: '平均生图时间(每批2张)',
            type: 'bar',
            label: labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.data1.avgChartTwoTime
          },
          {
            name: '平均生图时间(每批3张)',
            type: 'bar',
            label: labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.data1.avgChartThreeTime
          },
          {
            name: '平均生图时间(每批4张)',
            type: 'bar',
            label: labelOption,
            emphasis: {
              focus: 'series'
            },
            data: this.data1.avgChartFourTime
          }
        ]
      };
      this.chart.setOption(option);
    },
    updateChart1() {
      const option1 = {
        trigger: 'axis',
        xAxis: {
          type: 'category',
          data: this.xAxisData2
        },
          tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          type: 'value',
          name: '单位：秒',
          nameTextStyle: {
            fontSize: 12,
            padding: [0, 0, 10, 0] // Adjust padding as needed
          }
        },
        series: [
          {
            data: this.data2[this.selectedMetric1],
            type: 'line',
            smooth: true
          }
        ]
      };
      this.chart1.setOption(option1);
    },
    updateChart2() {
      const option2 = {
        xAxis: {
          type: 'category',
          data: this.xAxisData3
        },
        yAxis: {
          type: 'value',
          name: '单位：次',
          nameTextStyle: {
            fontSize: 12,
            padding: [0, 0, 10, 0] // Adjust padding as needed
          }
        },
               tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        series: [
          {
            data: this.data3[this.selectedMetric2],
            type: 'line',
            smooth: true
          }
        ]
      };
      this.chart2.setOption(option2);
    },
    updateChart3() {
      const groupedData = {};
      this.sizeList.forEach(item => {
      if (!groupedData[item.keyName]) {
      groupedData[item.keyName] = {
        name: item.keyName,
        dates: [],
        scores: []
      };
    }
    groupedData[item.keyName].dates.push(item.date);
    groupedData[item.keyName].scores.push(item.score);
  });
  // Step 2: 准备 series 数据
  const seriesData = Object.values(groupedData).map(group => ({
    name: group.name,
    data: group.scores,
    type: 'line',
    smooth: true
  }));

  // Step 3: 提取 xAxis 数据（所有日期）
  const allDates = [...new Set(this.sizeList.map(item => item.date))]; // 去重日期

  // Step 4: ECharts 配置
  const option3 = {
    title: {
      text: '公平队列'
    },
    xAxis: {
      type: 'category',
      data: allDates
    },
    yAxis: {
      type: 'value',
      nameTextStyle: {
        fontSize: 12,
        padding: [0, 0, 10, 0] // Adjust padding as needed
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10]
    },
    series: seriesData
  };

  this.chart3.setOption(option3);
},
updateChart4(){
  const groupedData = {};
      this.sizeList1.forEach(item => {
      if (!groupedData[item.keyName]) {
      groupedData[item.keyName] = {
        name: item.keyName,
        dates: [],
        scores: []
      };
    }
    groupedData[item.keyName].dates.push(item.date);
    groupedData[item.keyName].scores.push(item.score);
  });
  // Step 2: 准备 series 数据
  const seriesData = Object.values(groupedData).map(group => ({
    name: group.name,
    data: group.scores,
    type: 'line',
    smooth: true
  }));
    console.log(seriesData);
  // Step 3: 提取 xAxis 数据（所有日期）
  const allDates = [...new Set(this.sizeList.map(item => item.date))]; // 去重日期

  // Step 4: ECharts 配置
  const option4 = {
    title: {
      text: '非公平队列'
    },
    xAxis: {
      type: 'category',
      data: allDates
    },
    yAxis: {
      type: 'value',
      nameTextStyle: {
        fontSize: 12,
        padding: [0, 0, 10, 0] // Adjust padding as needed
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10]
    },
    series: seriesData
  };

  this.chart4.setOption(option4);
}
    
  }
};
</script>

<style lang="scss" scoped>
.personal-analysis {
  width: 100%;
  height: 100vh;
  color: #000;
}
.chart {
  width: 100%;
  height: 100%;
}

</style>
