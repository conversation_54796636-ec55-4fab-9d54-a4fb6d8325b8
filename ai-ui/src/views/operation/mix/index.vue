<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期" prop="recordDate">
        <el-date-picker clearable
          v-model="queryParams.recordDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['operation:mix:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['operation:mix:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['operation:mix:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['operation:mix:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

   <el-table v-loading="loading" :data="mixList" @selection-change="handleSelectionChange">
  <el-table-column type="selection" width="55" align="center" />
  <!-- <el-table-column label="" align="center" prop="id" /> -->
  <el-table-column label="日期" align="center" prop="recordDate" width="180">
    <template slot-scope="scope">
      <span>{{ parseTime(scope.row.recordDate, '{y}-{m}-{d}') }}</span>
    </template>
  </el-table-column>
  <el-table-column label="平均任务生图时间" align="center" prop="avgChartTime">
    <template slot-scope="scope">
      <span>{{ scope.row.avgChartTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="平均等待时长" align="center" prop="avgWait">
    <template slot-scope="scope">
      <span>{{ scope.row.avgWait || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="1图平均生图时间" align="center" prop="avgChartOneTime">
    <template slot-scope="scope">
      <span>{{ scope.row.avgChartOneTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="2图平均生图时间" align="center" prop="avgChartTwoTime">
    <template slot-scope="scope">
      <span>{{ scope.row.avgChartTwoTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="3图平均生图时间" align="center" prop="avgChartThreeTime">
    <template slot-scope="scope">
      <span>{{ scope.row.avgChartThreeTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="4图平均生图时间" align="center" prop="avgChartFourTime">
    <template slot-scope="scope">
      <span>{{ scope.row.avgChartFourTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="平均单图生成时间" align="center" prop="avgPerChartTime">
    <template slot-scope="scope">
      <span>{{ scope.row.avgPerChartTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="生图数量" align="center" prop="chartCount">
    <template slot-scope="scope">
      <span>{{ scope.row.chartCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="日活" align="center" prop="dau">
    <template slot-scope="scope">
      <span>{{ scope.row.dau || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="周活" align="center" prop="wau">
    <template slot-scope="scope">
      <span>{{ scope.row.wau || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="月活" align="center" prop="mau">
    <template slot-scope="scope">
      <span>{{ scope.row.mau || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="新注册用户量" align="center" prop="newRegisters">
    <template slot-scope="scope">
      <span>{{ scope.row.newRegisters || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="总用户量" align="center" prop="totalRegisters">
    <template slot-scope="scope">
      <span>{{ scope.row.totalRegisters || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="单用户平均生图数量" align="center" prop="avgChartsPerUser">
    <template slot-scope="scope">
      <span>{{ scope.row.avgChartsPerUser || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="当日最大并发生图任务数" align="center" prop="maxConcurrentChartTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.maxConcurrentChartTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="fast当日最大并发生图任务数" align="center" prop="fairQueueMaxConcurrentChartTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.fairQueueMaxConcurrentChartTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="非fast当日最大并发生图任务数" align="center" prop="unfairQueueMaxConcurrentChartTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.unfairQueueMaxConcurrentChartTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="单用户最大生图数量" align="center" prop="maxChartsPerUser">
    <template slot-scope="scope">
      <span>{{ scope.row.maxChartsPerUser || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="生成单图任务数" align="center" prop="chartCountOne">
    <template slot-scope="scope">
      <span>{{ scope.row.chartCountOne || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="生成2图任务数" align="center" prop="chartCountTwo">
    <template slot-scope="scope">
      <span>{{ scope.row.chartCountTwo || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="生成3图任务数" align="center" prop="chartCountThree">
    <template slot-scope="scope">
      <span>{{ scope.row.chartCountThree || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="生成4图任务数" align="center" prop="chartCountFour">
    <template slot-scope="scope">
      <span>{{ scope.row.chartCountFour || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="文生图任务数" align="center" prop="text2picTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.text2picTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="高清修复任务数" align="center" prop="hiresfixTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.hiresfixTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="去背景任务数" align="center" prop="removebgTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.removebgTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="图生图任务数" align="center" prop="pic2picTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.pic2picTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="快速生图任务数" align="center" prop="fastTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.fastTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="单用户remix点击次数" align="center" prop="remixPerUser">
    <template slot-scope="scope">
      <span>{{ scope.row.remixPerUser || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="生图任务数量" align="center" prop="chartTaskCount">
    <template slot-scope="scope">
      <span>{{ scope.row.chartTaskCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="图生图character_ref次数" align="center" prop="pic2picCharacterRef">
    <template slot-scope="scope">
      <span>{{ scope.row.pic2picCharacterRef || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="图生图content_ref次数" align="center" prop="pic2picContentRef">
    <template slot-scope="scope">
      <span>{{ scope.row.pic2picContentRef || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="图生图style_ref次数" align="center" prop="pic2picStyleRef">
    <template slot-scope="scope">
      <span>{{ scope.row.pic2picStyleRef || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="最喜爱生图比例" align="center" prop="favoriteAspectRatio">
    <template slot-scope="scope">
      <span>{{ scope.row.favoriteAspectRatio || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="realistic生图数量" align="center" prop="realisticCount">
    <template slot-scope="scope">
      <span>{{ scope.row.realisticCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="anime生图数量" align="center" prop="animeCount">
    <template slot-scope="scope">
      <span>{{ scope.row.animeCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="lineart生图数量" align="center" prop="lineartCount">
    <template slot-scope="scope">
      <span>{{ scope.row.lineartCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="flux 任务数" align="center" prop="fluxCount">
    <template slot-scope="scope">
      <span>{{ scope.row.fluxCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="pony 任务数" align="center" prop="ponyCount">
    <template slot-scope="scope">
      <span>{{ scope.row.ponyCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="flux 任务数" align="center" prop="fluxCount">
    <template slot-scope="scope">
      <span>{{ scope.row.fluxCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="art 任务数" align="center" prop="artCount">
    <template slot-scope="scope">
      <span>{{ scope.row.artCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="Fast任务，单图生成耗时" align="center" prop="fastOnePicTime">
    <template slot-scope="scope">
      <span>{{ scope.row.fastOnePicTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="Relax任务，任务平均时间" align="center" prop="relaxTaskAvgTime">
    <template slot-scope="scope">
      <span>{{ scope.row.relaxTaskAvgTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="Relax任务，单图生成耗时" align="center" prop="relaxOnePicTime">
    <template slot-scope="scope">
      <span>{{ scope.row.relaxOnePicTime || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="当日成功完成任务数量" align="center" prop="chartSuccessTaskCount">
    <template slot-scope="scope">
      <span>{{ scope.row.chartSuccessTaskCount || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="任务执行成功率" align="center" prop="chartSuccessTaskRate">
    <template slot-scope="scope">
      <span>{{ scope.row.chartSuccessTaskRate || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="当日Fast任务数量" align="center" prop="fastChartTaskRate">
    <template slot-scope="scope">
      <span>{{ scope.row.fastChartTaskRate || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="当日Relax任务数量" align="center" prop="relaxChartTaskRate">
    <template slot-scope="scope">
      <span>{{ scope.row.relaxChartTaskRate || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="用户上传任务数" align="center" prop="customUploadTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.customUploadTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="阔图任务数" align="center" prop="enlargeImageTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.enlargeImageTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="线稿上色任务数" align="center" prop="lineRecolorTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.lineRecolorTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="局部重绘任务数" align="center" prop="localRedrawTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.localRedrawTasks || 0 }}</span>
    </template>
  </el-table-column>
  <el-table-column label="图片渐变任务数" align="center" prop="varyTasks">
    <template slot-scope="scope">
      <span>{{ scope.row.varyTasks || 0 }}</span>
    </template>
  </el-table-column>

  <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
    <template slot-scope="scope">
      <el-button
        size="mini"
        type="text"
        icon="el-icon-edit"
        @click="handleUpdate(scope.row)"
        v-hasPermi="['operation:mix:edit']"
      >修改</el-button>
      <el-button
        size="mini"
        type="text"
        icon="el-icon-delete"
        @click="handleDelete(scope.row)"
        v-hasPermi="['operation:mix:remove']"
      >删除</el-button>
    </template>
  </el-table-column>
</el-table>

    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改Kpi汇总对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="recordDate">
          <el-date-picker clearable
            v-model="form.recordDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="平均任务生图时间" prop="avgChartTime">
          <el-input v-model="form.avgChartTime" placeholder="请输入平均任务生图时间" />
        </el-form-item>
        <el-form-item label="平均等待时长" prop="avgWait">
          <el-input v-model="form.avgWait" placeholder="请输入平均等待时长" />
        </el-form-item>
        <el-form-item label="1图平均生图时间" prop="avgChartOneTime">
          <el-input v-model="form.avgChartOneTime" placeholder="请输入1图平均生图时间" />
        </el-form-item>
        <el-form-item label="2图平均生图时间" prop="avgChartTwoTime">
          <el-input v-model="form.avgChartTwoTime" placeholder="请输入2图平均生图时间" />
        </el-form-item>
        <el-form-item label="3图平均生图时间" prop="avgChartThreeTime">
          <el-input v-model="form.avgChartThreeTime" placeholder="请输入3图平均生图时间" />
        </el-form-item>
        <el-form-item label="4图平均生图时间" prop="avgChartFourTime">
          <el-input v-model="form.avgChartFourTime" placeholder="请输入4图平均生图时间" />
        </el-form-item>
        <el-form-item label="平均单图生成时间" prop="avgPerChartTime">
          <el-input v-model="form.avgPerChartTime" placeholder="请输入平均单图生成时间" />
        </el-form-item>
        <el-form-item label="生图数量" prop="chartCount">
          <el-input v-model="form.chartCount" placeholder="请输入生图数量" />
        </el-form-item>
        <el-form-item label="日活" prop="dau">
          <el-input v-model="form.dau" placeholder="请输入日活" />
        </el-form-item>
        <el-form-item label="周活" prop="wau">
          <el-input v-model="form.wau" placeholder="请输入周活" />
        </el-form-item>
        <el-form-item label="月活" prop="mau">
          <el-input v-model="form.mau" placeholder="请输入月活" />
        </el-form-item>
        <el-form-item label="新注册用户量" prop="newRegisters">
          <el-input v-model="form.newRegisters" placeholder="请输入新注册用户量" />
        </el-form-item>
        <el-form-item label="总用户量" prop="totalRegisters">
          <el-input v-model="form.totalRegisters" placeholder="请输入总用户量" />
        </el-form-item>
        <el-form-item label="单用户平均生图数量" prop="avgChartsPerUser">
          <el-input v-model="form.avgChartsPerUser" placeholder="请输入单用户平均生图数量" />
        </el-form-item>
        <el-form-item label="当日最大并发生图任务数" prop="maxConcurrentChartTasks">
          <el-input v-model="form.maxConcurrentChartTasks" placeholder="请输入当日最大并发生图任务数" />
        </el-form-item>
        <el-form-item label="单用户最大生图数量" prop="maxChartsPerUser">
          <el-input v-model="form.maxChartsPerUser" placeholder="请输入单用户最大生图数量" />
        </el-form-item>
        <el-form-item label="生成单图任务数" prop="chartCountOne">
          <el-input v-model="form.chartCountOne" placeholder="请输入生成单图任务数" />
        </el-form-item>
        <el-form-item label="生成2图任务数" prop="chartCountTwo">
          <el-input v-model="form.chartCountTwo" placeholder="请输入生成2图任务数" />
        </el-form-item>
        <el-form-item label="生成3图任务数" prop="chartCountThree">
          <el-input v-model="form.chartCountThree" placeholder="请输入生成3图任务数" />
        </el-form-item>
        <el-form-item label="生成4图任务数" prop="chartCountFour">
          <el-input v-model="form.chartCountFour" placeholder="请输入生成4图任务数" />
        </el-form-item>
        <el-form-item label="文生图任务数" prop="text2picTasks">
          <el-input v-model="form.text2picTasks" placeholder="请输入文生图任务数" />
        </el-form-item>
        <el-form-item label="高清修复任务数" prop="hiresfixTasks">
          <el-input v-model="form.hiresfixTasks" placeholder="请输入高清修复任务数" />
        </el-form-item>
        <el-form-item label="去背景任务数" prop="removebgTasks">
          <el-input v-model="form.removebgTasks" placeholder="请输入去背景任务数" />
        </el-form-item>
        <el-form-item label="图生图任务数" prop="pic2picTasks">
          <el-input v-model="form.pic2picTasks" placeholder="请输入图生图任务数" />
        </el-form-item>
        <el-form-item label="快速生图任务数" prop="fastTasks">
          <el-input v-model="form.fastTasks" placeholder="请输入快速生图任务数" />
        </el-form-item>
        <el-form-item label="单用户remix点击次数" prop="remixPerUser">
          <el-input v-model="form.remixPerUser" placeholder="请输入单用户remix点击次数" />
        </el-form-item>
        <el-form-item label="生图任务数量" prop="chartTaskCount">
          <el-input v-model="form.chartTaskCount" placeholder="请输入生图任务数量" />
        </el-form-item>
        <el-form-item label="图生图character_ref次数" prop="pic2picCharacterRef">
          <el-input v-model="form.pic2picCharacterRef" placeholder="请输入图生图character_ref次数" />
        </el-form-item>
        <el-form-item label="图生图content_ref次数" prop="pic2picContentRef">
          <el-input v-model="form.pic2picContentRef" placeholder="请输入图生图content_ref次数" />
        </el-form-item>
        <el-form-item label="图生图style_ref次数" prop="pic2picStyleRef">
          <el-input v-model="form.pic2picStyleRef" placeholder="请输入图生图style_ref次数" />
        </el-form-item>
        <el-form-item label="最喜爱生图比例" prop="favoriteAspectRatio">
          <el-input v-model="form.favoriteAspectRatio" placeholder="请输入最喜爱生图比例" />
        </el-form-item>
        <el-form-item label="realistic生图数量" prop="realisticCount">
          <el-input v-model="form.realisticCount" placeholder="请输入realistic生图数量" />
        </el-form-item>
        <el-form-item label="anime生图数量" prop="animeCount">
          <el-input v-model="form.animeCount" placeholder="请输入anime生图数量" />
        </el-form-item>
        <el-form-item label="lineart生图数量" prop="lineartCount">
          <el-input v-model="form.lineartCount" placeholder="请输入lineart生图数量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMix, getMix, delMix, addMix, updateMix } from "@/api/operation/mix";

export default {
  name: "Mix",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // Kpi汇总表格数据
      mixList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        recordDate: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询Kpi汇总列表 */
    getList() {
      this.loading = true;
      listMix(this.queryParams).then(response => {
        this.mixList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        recordDate: null,
        avgChartTime: null,
        avgWait: null,
        avgChartOneTime: null,
        avgChartTwoTime: null,
        avgChartThreeTime: null,
        avgChartFourTime: null,
        avgPerChartTime: null,
        chartCount: null,
        dau: null,
        wau: null,
        mau: null,
        newRegisters: null,
        totalRegisters: null,
        avgChartsPerUser: null,
        maxConcurrentChartTasks: null,
        maxChartsPerUser: null,
        chartCountOne: null,
        chartCountTwo: null,
        chartCountThree: null,
        chartCountFour: null,
        text2picTasks: null,
        hiresfixTasks: null,
        removebgTasks: null,
        pic2picTasks: null,
        fastTasks: null,
        remixPerUser: null,
        chartTaskCount: null,
        pic2picCharacterRef: null,
        pic2picContentRef: null,
        pic2picStyleRef: null,
        favoriteAspectRatio: null,
        realisticCount: null,
        animeCount: null,
        lineartCount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加Kpi汇总";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMix(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改Kpi汇总";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMix(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMix(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除Kpi汇总编号为"' + ids + '"的数据项？').then(function() {
        return delMix(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('operation/mix/export', {
        ...this.queryParams
      }, `mix_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
