<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="登录账号" prop="loginName">
                <el-input v-model="queryParams.loginName" placeholder="请输入登录账号" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="用户昵称" prop="userName">
                <el-input v-model="queryParams.userName" placeholder="请输入用户昵称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="会员类型" prop="vipType">
                <el-select v-model="queryParams.vipType" placeholder="请选择会员类型" clearable>
                    <el-option v-for="dict in dict.type.vip_type" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="价格间隔" prop="priceInterval">
                <el-select v-model="queryParams.priceInterval" placeholder="请选择价格间隔" clearable>
                    <el-option v-for="dict in dict.type.stripe_schedule_record_interval" :key="dict.value"
                        :label="dict.label" :value="dict.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="lumen 获得时间">
                <el-date-picker v-model="daterangeVipBeginTime" style="width: 240px" value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetimerange" range-separator="-" start-placeholder="开始日期"
                    end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <!-- <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    v-hasPermi="['admin:user:export']">导出</el-button>
            </el-col> -->
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户ID" align="center" prop="id" />
            <el-table-column label="登录账号" align="center" prop="loginName" />
            <el-table-column label="用户昵称" align="center" prop="userName" />
            <el-table-column label="用户邮箱" align="center" prop="email" />
            <el-table-column label="注册国家" align="center" prop="registCountry" />
            <el-table-column label="用户已收藏数量" align="center" prop="usedCollectNum" />

            <el-table-column label="已使用的存储空间 (MB)" align="center" prop="usedSize"
                :formatter="(row) => (row.usedSize / 1024 / 1024).toFixed(2)" />
            <!-- <el-table-column label="总存储空间 (MB)" align="center" prop="totalSize"
                :formatter="(row) => (row.totalSize / 1024 / 1024).toFixed(2)" /> -->
            <el-table-column label="会员类型" align="center" prop="vipType">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.vip_type" :value="scope.row.vipType" />
                </template>
            </el-table-column> <el-table-column label="普通会员生效时间" align="center" prop="vipBeginTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.vipBeginTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="普通会员过期时间" align="center" prop="vipEndTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.vipEndTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="每日免费点数日期(只记录年月日)" align="center" prop="dailyLumensTime" width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.dailyLumensTime, 'yyyy-MM-dd HH:mm:ss') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="每日免费点数" align="center" prop="dailyLumens" />
            <el-table-column label="每日已使用免费点数" align="center" prop="useDailyLumens" />
            <el-table-column label="价格间隔" align="center" prop="priceInterval">
                <template slot-scope="scope">
                    <dict-tag :options="dict.type.stripe_schedule_record_interval" :value="scope.row.priceInterval" />
                </template>
            </el-table-column>
            <el-table-column label="头像图片缩略图路径" align="center" prop="thumbnailAvatarUrl" width="100">
                <template slot-scope="scope">
                    <div v-if="scope.row.thumbnailAvatarUrl">
                        <image-preview :src="scope.row.thumbnailAvatarUrl" :width="50" :height="50" />
                    </div>
                    <div v-else>
                        <span>用户暂无上传头像</span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit"
                        @click="handleLumens(scope.row)">lumen情况</el-button>
                    <el-button size="mini" type="text" icon="el-icon-edit"
                        @click="handleLumenList(scope.row)">查看消费日志</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />

        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="每日免费点数日期" prop="dailyLumensTime">
                    <el-input v-model="lumensForm.dailyLumensTime" :disabled="true" />
                </el-form-item>
            </el-form>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="每日免费点数" prop="dailyLumens">
                    <el-input v-model="lumensForm.dailyLumens" :disabled="true" />
                </el-form-item>
            </el-form>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="会员点数" prop="vipLumens">
                    <el-input v-model="lumensForm.vipLumens" :disabled="true" />
                </el-form-item>
            </el-form>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="充值点数" prop="rechargeLumens">
                    <el-input v-model="lumensForm.rechargeLumens" :disabled="true" />
                </el-form-item>
            </el-form>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="剩余每日免费点数" prop="leftDailyLumens">
                    <el-input v-model="lumensForm.leftDailyLumens" :disabled="true" />
                </el-form-item>
            </el-form>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="剩余会员点数" prop="leftVipLumens">
                    <el-input v-model="lumensForm.leftVipLumens" :disabled="true" />
                </el-form-item>
            </el-form>
            <el-form ref="form" :model="lumensForm" :rules="rules" label-width="80px">
                <el-form-item label="剩余充值点数" prop="leftRechargeLumens">
                    <el-input v-model="lumensForm.leftRechargeLumens" :disabled="true" />
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog :visible.sync="dialogVisible" title="查看Lumen消费日志" width="80%" :before-close="handleClose">
            <el-table :data="lumenCosts" stripe style="width: 100%">
                <el-table-column label="用户ID" prop="userId" />
                <el-table-column label="账号" prop="loginName" />
                <el-table-column label="任务id" prop="payLumenRecordId" />
                <!-- 任务ID 列，添加点击事件 -->
                <!-- <el-table-column label="任务id">
                    <template #default="{ row }">
                        <span style="color: blue; cursor: pointer;"
                            @click="viewRecord(row.payLumenRecordId, row.loginName)">
                            {{ row.payLumenRecordId }}
                        </span>
                    </template>
                </el-table-column> -->

                <el-table-column label="lumen原有数量" prop="lumenBefore" />
                <el-table-column label="Lumen消费" prop="lumenCost" />
                <el-table-column label="Lumen剩余" prop="lumenLeft" />
                <el-table-column label="创建时间" prop="createTime" />
            </el-table>

            <!-- 分页控件 -->
            <pagination layout="sizes, prev, next" :total="10000000" :page.sync="lumenCostQueryParams.pageNum"
                :page-sizes="[10, 20, 30, 50]" :limit.sync="lumenCostQueryParams.pageSize" @prev="prevClick"
                @next="nextClick" @pagination="queryLumenList()" />
        </el-dialog>

        <el-dialog :title="title" :visible.sync="openRecord" width="500px" append-to-body>
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>任务详情</span>
                    <el-button style="float: right; padding: 3px 0" type="text"
                        @click="openRecord = false">关闭</el-button>
                </div>
                <!-- 遍历 recordDetails 并显示所有字段 -->
                <div v-for="(value, key) in recordDetails" :key="key" class="text item">
                    <strong>{{ key }}:</strong> {{ value }}
                </div>
            </el-card>

            <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div> -->
        </el-dialog>
    </div>
</template>

<script>
import { vipLst } from "@/api/admin/user";
import { lumensMessage } from "@/api/orders/standards"
import { lumenList } from "@/api/orders/lumenRecord"
import { getRecord } from "@/api/admin/record";


export default {
    name: "User",
    dicts: ['stripe_schedule_record_interval', 'vip_type'],
    data() {
        return {
            dialogVisible: false, // 控制对话框的显示
            lumenRow: null,
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            lumenCosts: [],
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 用户信息表格数据
            userList: [],
            // 控制卡片弹窗显示
            openRecord: false,
            // 存储接口返回的任务详情
            recordDetails: {},
            // 标题
            title: "任务详情",
            // 是否显示弹出层
            open: false,
            // 会员生效时间范围
            daterangeVipBeginTime: [],
            lumenCostQueryParams: {
                pageSize: 10,
                loginName: null,
                markFileId: null,
                isNext: null
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                loginName: null,
                userName: null,
                vipType: null,
                priceInterval: null
            },
            lumensForm: {},
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        // 点击任务ID时调用此方法
        viewRecord(id, loginName) {
            getRecord(id, loginName)
                .then(response => {
                    this.recordDetails = response.data;  // 获取任务详情
                    this.openRecord = true;  // 打开卡片弹窗
                })
                .catch(error => {
                    this.$message.error("获取任务详情失败");
                });
        },

        nextClick() {
            this.lumenCostQueryParams.markFileId = this.lumenCosts[this.lumenCosts.length - 1].id
            this.lumenCostQueryParams.isNext = true;
        },
        prevClick() {
            this.lumenCostQueryParams.markFileId = this.lumenCosts[0].id
            this.lumenCostQueryParams.isNext = false;
        },
        handleClose() {
            this.dialogVisible = false;
        },
        queryLumenList() {
            lumenList(this.lumenCostQueryParams).then(response => {
                if (response.rows.length === 0) {
                    this.$modal.msgSuccess("未查询到数据 返回上个列表页面");
                    return;
                }
                this.lumenCosts = response.rows;
            });;
        },
        handleLumenList(row) {
            this.lumenRow = row;
            this.dialogVisible = true;
            this.lumenCostQueryParams.markFileId = null;
            this.lumenCostQueryParams.isNext = null;

            this.lumenCostQueryParams.loginName = row.loginName;
            lumenList(this.lumenCostQueryParams).then(response => {
                this.lumenCosts = response.rows;
            });; // 查询用户的Lumen消费日志
        },
        handleLumens(row) {
            const id = row.id
            lumensMessage(id).then(response => {
                this.lumensForm = response.data;
                this.open = true;
            });
        },
        parseTime(time, format) {
            if (!time) return ''; // 如果时间无效，直接返回空字符串

            // 确保 time 是字符串，并且是秒级时间戳，转换为毫秒级
            const timestamp = typeof time === 'string' && time.length === 10 ? parseInt(time) * 1000 : time;

            // 创建 Date 对象并检查其有效性
            const date = new Date(timestamp);

            // 如果日期无效，返回空字符串
            if (isNaN(date.getTime())) {
                return ''; // 或者你可以返回 '无效时间' 等提示
            }

            // 格式化日期为 yyyy-MM-dd HH:mm:ss
            if (format === 'yyyy-MM-dd HH:mm:ss') {
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                const seconds = date.getSeconds().toString().padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            }

            // 如果没有匹配的 format，返回默认格式
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false,
            };
            const formatter = new Intl.DateTimeFormat('zh-CN', options);
            const parts = formatter.formatToParts(date);
            const formatted = parts.map(part => part.value).join('');
            return formatted;
        },
        /** 查询用户信息列表 */
        getList() {
            this.loading = true;
            this.queryParams.params = {};

            if (null != this.daterangeVipBeginTime && '' != this.daterangeVipBeginTime) {
                this.queryParams.params["beginVipBeginTime"] = this.daterangeVipBeginTime[0];
                this.queryParams.params["endVipBeginTime"] = this.daterangeVipBeginTime[1];
            }
            vipLst(this.queryParams).then(response => {
                this.userList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 表单重置
        reset() {
            this.form = {
                id: null,
                loginName: null,
                userName: null,
                email: null,
                avatarUrl: null,
                thumbnailAvatarUrl: null,
                usedSize: null,
                totalSize: null,
                vipType: null,
                vipBeginTime: null,
                vipEndTime: null,
                dailyLumensTime: null,
                dailyLumens: null,
                useDailyLumens: null,
                priceInterval: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.daterangeVipBeginTime = [];
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.id)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('admin/user/export', {
                ...this.queryParams
            }, `user_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>