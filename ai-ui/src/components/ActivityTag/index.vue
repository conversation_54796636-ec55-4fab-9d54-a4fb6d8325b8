<template>
  <div class="custom-tag">
    <img :src="tagIcons[type]" />
    <el-input v-model="content" placeholder="请输入" />
  </div>
</template>

<script>
const TYPE_ENUM = {
  type1: require("@/assets/icons/svg/xxxx.svg"),
  type2: require("@/assets/icons/svg/xxxx.svg"),
  type3: require("@/assets/icons/svg/xxxx.svg"),
  type4: require("@/assets/icons/svg/xxxx.svg"),
};
export default {
  props: {
    type: {
      type: String,
      default: "",
    },
    value: {
      type: [String, Number, undefined, null],
      default: "",
    },
  },
  data() {
    return {
      content: "",
      tagIcons: TYPE_ENUM,
    };
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.custom-tag {
  height: 32px;
  padding: 4px 8px;
  border: 1px solid #ffbb0f;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8;
}
</style>
