<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="80%"
    @close="handleClose"
    append-to-body>
    
    <div v-loading="loading">
      <!-- 统计概览 -->
      <el-card class="stats-card" v-if="statsData">
        <div slot="header">
          <span>统计概览</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">${{ statsData.totalLumenAmount || '0.00' }}</div>
              <div class="stat-label">会员总Lumen购买金额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statsData.memberCount || 0 }}</div>
              <div class="stat-label">购买会员人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">${{ statsData.totalMemberAmount || '0.00' }}</div>
              <div class="stat-label">购买会员总金额</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ statsData.lumenPackageCount || 0 }}</div>
              <div class="stat-label">购买Lumen套餐包人数</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 购买详情列表 -->
      <el-card class="purchase-card">
        <div slot="header">
          <span>购买详情</span>
        </div>
        <el-table :data="purchaseList" v-loading="purchaseLoading">
          <el-table-column prop="loginName" label="用户名称" width="150" align="center" />
          <el-table-column prop="productName" label="购买项名称" width="200" align="center" />
          <el-table-column prop="qty" label="购买数量" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.qty || 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="实付金额" width="120" align="center">
            <template slot-scope="scope">
              ${{ scope.row.amount || '0.00' }}
            </template>
          </el-table-column>
          <el-table-column prop="usedTime" label="付款日期" width="180" align="center">
            <template slot-scope="scope">
              {{ formatTimestamp(scope.row.usedTime) }}
            </template>
          </el-table-column>
        </el-table>
        
        <pagination
          v-show="purchaseTotal > 0"
          :total="purchaseTotal"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getPurchaseList"
          style="margin-top: 20px;"
        />
      </el-card>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
      <el-button type="primary" @click="goToStatsPage">查看完整统计</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCouponStats, getCouponPurchaseDetails } from "@/api/admin/couponStats";

export default {
  name: "CouponStatsDialog",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    couponCode: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      purchaseLoading: false,
      statsData: null,
      purchaseList: [],
      purchaseTotal: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  computed: {
    dialogTitle() {
      if (this.couponCode) {
        return `优惠码统计 - ${this.couponCode}`;
      } else if (this.type) {
        return `类型统计 - ${this.type}`;
      }
      return '统计详情';
    }
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val && this.couponCode) {
        this.loadData();
      }
    },
    visible(val) {
      this.$emit('input', val);
    }
  },
  methods: {
    /** 格式化时间戳 */
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-');
    },
    /** 加载数据 */
    loadData() {
      if (!this.couponCode) return;
      
      this.loading = true;
      Promise.all([
        this.getStatsData(),
        this.getPurchaseList()
      ]).finally(() => {
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    getStatsData() {
      return getCouponStats(this.couponCode).then(response => {
        this.statsData = response.data;
      });
    },
    /** 获取购买详情 */
    getPurchaseList() {
      this.purchaseLoading = true;
      return getCouponPurchaseDetails(this.couponCode, this.queryParams).then(response => {
        this.purchaseList = response.rows || [];
        this.purchaseTotal = response.total || 0;
        this.purchaseLoading = false;
      }).catch(() => {
        this.purchaseLoading = false;
      });
    },
    /** 关闭弹窗 */
    handleClose() {
      this.visible = false;
      this.statsData = null;
      this.purchaseList = [];
      this.purchaseTotal = 0;
      this.queryParams.pageNum = 1;
    },
    /** 跳转到完整统计页面 */
    goToStatsPage() {
      const query = {};
      if (this.couponCode) {
        query.couponCode = this.couponCode;
      }
      if (this.type) {
        query.type = this.type;
      }
      this.$router.push({
        path: '/admin/coupon/stats',
        query: query
      });
      this.handleClose();
    }
  }
};
</script>

<style scoped>
.stats-card {
  margin-bottom: 20px;
}

.purchase-card {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.dialog-footer {
  text-align: right;
}
</style>
