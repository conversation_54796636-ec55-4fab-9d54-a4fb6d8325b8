package com.ai.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.util.List;

public class JsonUtils {
  private static final ObjectMapper objectMapper = new ObjectMapper();

  /**
   * 将String转换成实体类
   * @param jsonString
   * @param clazz
   * @param <T>
   * @return
   * @throws JsonProcessingException
   */
  public static <T> T fromString(String jsonString, Class<T> clazz) throws JsonProcessingException {
    return objectMapper.readValue(jsonString, clazz);
  }

  /**
   * 将json转换成实体类
   * @param jsonNode
   * @param clazz
   * @param <T>
   * @return
   * @throws JsonProcessingException
   */
  public static <T> T fromJsonNode(JsonNode jsonNode, Class<T> clazz) throws JsonProcessingException {
    return objectMapper.treeToValue(jsonNode, clazz);
  }

  /**
   * 将json数据转换成list
   */
  public static <T> List<T> writeToList(String json, Class<T> beanType) throws JsonProcessingException {
    JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, beanType);
    return objectMapper.readValue(json, javaType);
  }

  /**
   *将对象转换成string
   * @param value
   * @return
   * @throws JsonProcessingException
   */
  public static String writeToString(Object value) throws JsonProcessingException {
    return objectMapper.writeValueAsString(value);
  }

  /**
   * 将对象转换成json
   * @param value
   * @return
   * @throws JsonProcessingException
   */
  public static JsonNode writeToJsonNode(Object value) throws JsonProcessingException {
    return objectMapper.valueToTree(value);
  }

  /**
   * 将字符串转换成json
   * @param value
   * @return
   * @throws JsonProcessingException
   */
  public static JsonNode writeStringToJsonNode(String value) throws JsonProcessingException {
    return objectMapper.readTree(value);
  }

  public static <T> T fromStringRegisterModule(String jsonString, Class<T> clazz) throws JsonProcessingException {
    objectMapper.registerModule(new JavaTimeModule());
    return objectMapper.readValue(jsonString, clazz);
  }

  public static String writeToStringRegisterModule(Object value) throws JsonProcessingException {
    objectMapper.registerModule(new JavaTimeModule());
    return objectMapper.writeValueAsString(value);
  }

  public static boolean isValidJson(String json) {
    if (json == null || json.trim().isEmpty()) {
      return false;
    }
    try {
      objectMapper.readTree(json);  // 尝试解析 JSON
      return true;
    } catch (Exception e) {
      return false; // 抛异常说明不是有效的 JSON
    }
  }

}
