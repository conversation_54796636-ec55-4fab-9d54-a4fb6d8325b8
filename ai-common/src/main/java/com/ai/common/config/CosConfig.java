package com.ai.common.config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */
@Configuration
public class CosConfig {
    @Autowired
    private TencentCloudStorageConfig config;

    @Bean("baseCosClient")
    public COSClient baseCosClient() {
        return getCosClientResult(config.getAccelerateSuffix());
    }

    @Bean("baseCosClientOld")
    public COSClient baseCosClientOld() {
        return getCosClientResult(config.getBaseSuffix());
    }

    @Bean(name = "transferManager", destroyMethod = "shutdownNow")
    public TransferManager accelerateTransferManager() {
        return createTransferManager(config.getBaseSuffix());
    }

    private COSClient getCosClientResult(String suffix) {
        COSCredentials cred = new BasicCOSCredentials(config.getSecretId(), config.getSecretKey());
        ClientConfig clientConfig = new ClientConfig(new Region(config.getRegion()));
        // 推荐使用 https 协议
        clientConfig.setHttpProtocol(HttpProtocol.https);
        // 3 设置全球加速域名后缀
        clientConfig.setEndPointSuffix(suffix);
        // 4 生成 cos 客户端
        return new COSClient(cred, clientConfig);
    }

    private TransferManager createTransferManager(String suffix) {
        // 1 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(config.getSecretId(), config.getSecretKey());
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(config.getRegion()));
        clientConfig.setHttpProtocol(HttpProtocol.https);
        clientConfig.setShutdownTimeout(10 * 60 * 1000);
        clientConfig.setEndPointSuffix(suffix);
        // 3 生成cos客户端
        COSClient cosclient = new COSClient(cred, clientConfig);
        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        // 传入一个threadpool, 若不传入线程池, 默认TransferManager中会生成一个单线程的线程池。
        return new TransferManager(cosclient, threadPool);
    }
}
