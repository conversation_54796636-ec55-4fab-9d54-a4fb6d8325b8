package com.ai.ga.service.impl;

import com.ai.common.utils.DoubleMathUtils;
import com.ai.ga.service.GaService;
import com.ai.operation.domain.KpiGaData;
import com.ai.operation.mapper.KpiGaDataMapper;
import com.google.analytics.data.v1alpha.StringFilter;
import com.google.analytics.data.v1beta.*;
import com.google.api.client.util.Value;
import com.google.api.gax.core.FixedCredentialsProvider;
import com.google.api.gax.rpc.ApiException;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class GaServiceImpl implements GaService {

    private static final String PROPERTY_ID = "445844306"; // 替换为你的 GA4 Property ID
    private static final String CREDENTIALS_JSON_PATH = "piclumen-c034b-b0ffa477b5ee-web.json"; // 资源路径
    private static final  String PROPERTY_ID_APP_Piclumen = "459693876";
    private static final String CREDENTIALS_JSON_PATH_APP_Piclumen = "piclumen-new.json"; // 资源路径

    private static final String CREDENTIALS_JSON_PATH_IOS_AIEASE = "ai-ease-90be3-5c4dcf91838b.json";

    private static final String CREDENTIALS_JSON_PATH_WEB_AIEASE = "aiease-934e7-97a7682c2e4f.json";

    private static final  String PROPERTY_ID_IOS_AIEASE = "462764236";

    private static final  String PROPERTY_ID_WEB_AIEASE = "426010660";


    private static final  String PROPERTY_ID_ANDROID_AIEASE = "471351559";

    @Autowired
    private  KpiGaDataMapper kpiGaDataMapper;


    // 初始化 Google Analytics Data API 客户端
    static BetaAnalyticsDataClient initializeAnalyticsDataClient(String credentialsJsonPath) throws IOException {
        // 使用类路径加载资源文件
        Resource resource = new ClassPathResource(credentialsJsonPath);

        try (InputStream credentialsStream = resource.getInputStream()) {
            // 使用类路径的输入流初始化凭证
            GoogleCredentials credentials = GoogleCredentials.fromStream(credentialsStream)
                    .createScoped(
                            "https://www.googleapis.com/auth/analytics.readonly",
                            "https://www.googleapis.com/auth/analytics");

            // 设置凭证并返回 BetaAnalyticsDataClient
            BetaAnalyticsDataSettings betaAnalyticsDataSettings =
                    BetaAnalyticsDataSettings.newBuilder()
                            .setCredentialsProvider(FixedCredentialsProvider.create(credentials))
                            .build();

            return BetaAnalyticsDataClient.create(betaAnalyticsDataSettings);
        }
    }


    @Override
    public  void getGaReport(String jsonPath, String propertyId, String date,KpiGaData kpiGaData) {
        try {

            BetaAnalyticsDataClient analyticsData = initializeAnalyticsDataClient(jsonPath);
            sampleRunReport(analyticsData,date,propertyId,kpiGaData);
            runReport(analyticsData,date,propertyId,kpiGaData);
            runTrafficReport(analyticsData,date,propertyId,kpiGaData);
            runNewUsersReport(analyticsData,date,propertyId,kpiGaData);
            runReferralTrafficReport(analyticsData,date,propertyId,kpiGaData);
            kpiGaDataMapper.insert(kpiGaData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // 运行报告并打印结果
    static void sampleRunReport(BetaAnalyticsDataClient analyticsData,String date,String propertyId, KpiGaData  kpiGaData) throws Exception {
        // 初始化 Analytics Data 客户端
        try  {
            // 创建请求并设置查询参数
            RunReportRequest request = RunReportRequest.newBuilder()
                    .setProperty("properties/" + propertyId)
                    .addDimensions(Dimension.newBuilder().setName("date"))
                    .addMetrics(Metric.newBuilder().setName("active1DayUsers"))
                    .addMetrics(Metric.newBuilder().setName("active7DayUsers"))
                    .addMetrics(Metric.newBuilder().setName("active28DayUsers"))
                    .addMetrics(Metric.newBuilder().setName("dauPerMau"))
                    .addDimensions(Dimension.newBuilder().setName("platform"))
                    .addDateRanges(DateRange.newBuilder().setStartDate(date).setEndDate(date))
                    .build();

            // 执行报告请求并获取响应
            RunReportResponse response = analyticsData.runReport(request);

            for (Row row : response.getRowsList()) {

                // 解析并计算 DAU/MAU 比率
                Long active1DayUsers = Long.parseLong(row.getMetricValues(0).getValue());
                Long active7DayUsers = Long.parseLong(row.getMetricValues(1).getValue());
                Double dauPerMau = Double.parseDouble(row.getMetricValues(3).getValue());
                String platform = row.getDimensionValues(1).getValue();
                if (  platform.equals(kpiGaData.getPlatform())){
                    kpiGaData.setDau(active1DayUsers);
                    kpiGaData.setWau(active7DayUsers);
                    kpiGaData.setMau((long) (active1DayUsers/dauPerMau));
                }
                log.info(
                        "Date: {}, DAU: {}, WAU: {}, MAU: {}, DAU/MAU Ratio: {}, Platform: {}",
                        row.getDimensionValues(0).getValue(),
                        active1DayUsers,
                        active7DayUsers,
                        active1DayUsers/dauPerMau,
                        dauPerMau,
                        platform
                );
            }
        } catch (ApiException e) {
            kpiGaData.setDau(0l);
            kpiGaData.setWau(0l);
            kpiGaData.setMau(0l);
            log.info("Error during report request: " + e.getStatusCode().getCode());
        }
    }
//    United State
    public static void runReport(BetaAnalyticsDataClient analyticsData,String date,String propertyId, KpiGaData  kpiGaData) {
        // 构建请求并设置要查询的度量标准
        RunReportRequest request = RunReportRequest.newBuilder()
                .setProperty("properties/" + propertyId)
                .addMetrics(Metric.newBuilder().setName("active1DayUsers"))
                .addMetrics(Metric.newBuilder().setName("active7DayUsers"))
                .addMetrics(Metric.newBuilder().setName("active28DayUsers"))
                .addMetrics(Metric.newBuilder().setName("dauPerMau"))

                .addDateRanges(DateRange.newBuilder().setStartDate(date).setEndDate(date))
                .setDimensionFilter(
                        FilterExpression.newBuilder()
                                .setFilter(
                                        Filter.newBuilder()
                                                .setFieldName("countryId")
                                                .setStringFilter(
                                                        Filter.StringFilter.newBuilder().setValue("US"))))
                .build();
        // 执行报告请求并获取结果
        try {
            RunReportResponse response = analyticsData.runReport(request);

            // 输出报告数据
            for (Row row : response.getRowsList()) {
                Long active1DayUsers =  Long.parseLong(row.getMetricValues(0).getValue());
                Long active7DayUsers =  Long.parseLong(row.getMetricValues(1).getValue());
                Double dauPerMau = Double.parseDouble(row.getMetricValues(3).getValue());
                kpiGaData.setUsaDau(active1DayUsers);
                kpiGaData.setUsaWau(active7DayUsers);
                kpiGaData.setUsaMau((long) (active1DayUsers/dauPerMau));
                log.info("Active 1 Day Users (USA): " + active1DayUsers+",  Active 7 Day Users (USA): " + active7DayUsers+",  Active 30 Day Users (USA): " + active1DayUsers/dauPerMau);
            }
        } catch (ApiException e) {
            kpiGaData.setUsaDau(0l);
            kpiGaData.setUsaWau(0l);
            kpiGaData.setUsaMau(0l);
            log.info("Error during report request: " + e.getStatusCode().getCode());
        }
    }

    // 获取日页面访问量、周页面访问量、月页面访问量
    public static void runTrafficReport(BetaAnalyticsDataClient analyticsData,String date,String propertyId,KpiGaData kpiGaData) {

        // 解析传入的日期字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate endDate = LocalDate.parse(date, formatter);

        // 计算 startDate（传入日期减去30天）
        LocalDate startDate = endDate.minusDays(30);

        // 将日期格式化为字符串
        String startDateString = startDate.format(formatter);
        String endDateString = endDate.format(formatter);
        // 构建请求并设置要查询的度量标准
        RunReportRequest request = RunReportRequest.newBuilder()
                .setProperty("properties/" + propertyId)
                .addMetrics(Metric.newBuilder().setName("screenPageViews")) // 使用 screenPageViews 获取页面访问量
                .addDimensions(Dimension.newBuilder().setName("date"))
                .addDateRanges(DateRange.newBuilder().setStartDate(startDateString).setEndDate(endDateString))
                .build();

        // 执行报告请求并获取结果
        try {
            Long weekPageViews = 0L;
            Long monthPageViews = 0L;
            Long todayPageViews = 0L;

            // 假设这是从 API 获取到的响应数据
            RunReportResponse response = analyticsData.runReport(request);

            // 设定日期格式
            DateTimeFormatter sdf =DateTimeFormatter.ofPattern("yyyyMMdd");
            for (Row row : response.getRowsList()) {
                Long dayPageViews = Long.valueOf(row.getMetricValues(0).getValue());
                LocalDate day =  LocalDate.parse(row.getDimensionValues(0).getValue(),sdf);

                // 定义目标日期 一周前的日期
                LocalDate weekStartDate = endDate.minusDays(7);

                // 如果日期大于 weekStartDate，则加到 weekPageViews 中
                if (day.isAfter(weekStartDate)) {
                    weekPageViews += dayPageViews;
                }

                // 如果日期等于 endDate，则赋值给 todayPageViews
                if (day.equals(endDate)) {
                    todayPageViews = dayPageViews;
                }

                // 月度数据始终加到 monthPageViews
                monthPageViews += dayPageViews;
            }
            kpiGaData.setTodayPageViews(todayPageViews);
            kpiGaData.setWeekPageViews(weekPageViews);
            kpiGaData.setMonthPageViews(monthPageViews);
            // 输出结果
           log.info("todayPageViews: " + todayPageViews + ", weekPageViews: " + weekPageViews + ", monthPageViews: " + monthPageViews);
        } catch (ApiException e) {
            log.info("Error during report request: " + e.getStatusCode().getCode());
            kpiGaData.setTodayPageViews(0l);
            kpiGaData.setWeekPageViews(0l);
            kpiGaData.setMonthPageViews(0l);
        }
    }


    // 获取首次访问次数（新用户）
    public static void runNewUsersReport(BetaAnalyticsDataClient analyticsData,String date,String propertyId,KpiGaData kpiGaData) {
        // 解析传入的日期字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate endDate = LocalDate.parse(date, formatter);

        // 计算 startDate（传入日期减去30天）
        LocalDate startDate = endDate.minusDays(30);

        // 将日期格式化为字符串
        String startDateString = startDate.format(formatter);
        String endDateString = endDate.format(formatter);
        // 构建请求并设置要查询的度量标准
        RunReportRequest request = RunReportRequest.newBuilder()
                .setProperty("properties/" + propertyId)
                .addMetrics(Metric.newBuilder().setName("newUsers"))
                .addDimensions(Dimension.newBuilder().setName("date"))

                .addDateRanges(DateRange.newBuilder().setStartDate(startDateString).setEndDate(endDateString))
                .build();

        // 执行报告请求并获取结果
        try {
            Long weekNewUsers = 0L;
            Long monthNewUsers = 0L;
            Long todayNewUsers = 0L;

            // 假设这是从 API 获取到的响应数据
            RunReportResponse response = analyticsData.runReport(request);

            // 设定日期格式
            DateTimeFormatter sdf =DateTimeFormatter.ofPattern("yyyyMMdd");
            for (Row row : response.getRowsList()) {
                Long dayNewUsers = Long.valueOf(row.getMetricValues(0).getValue());
                LocalDate day =  LocalDate.parse  (row.getDimensionValues(0).getValue(),sdf);

                // 定义目标日期 一周前的日期
                LocalDate weekStartDate = endDate.minusDays(7);

                // 如果日期大于 weekStartDate，则加到 weekPageViews 中
                if (day.isAfter(weekStartDate)) {
                    weekNewUsers += dayNewUsers;
                }

                // 如果日期等于 endDate，则赋值给 todayPageViews
                if (day.equals(endDate)) {
                    todayNewUsers = dayNewUsers;
                }

                // 月度数据始终加到 monthPageViews
                monthNewUsers += dayNewUsers;
            }
            kpiGaData.setTodayNewUsers(todayNewUsers);
            kpiGaData.setWeekNewUsers(weekNewUsers);
            kpiGaData.setMonthNewUsers(monthNewUsers);
            // 输出结果
            log.info("todayNewUsers: " + todayNewUsers + ", todayNewUsers: " + weekNewUsers + ", monthNewUsers: " + monthNewUsers);
        } catch (ApiException e) {
            kpiGaData.setTodayNewUsers(0l);
            kpiGaData.setWeekNewUsers(0l);
            kpiGaData.setMonthNewUsers(0l);
            log.info("Error during report request: " + e.getStatusCode().getCode());
        }
    }



    // 获取引荐流量（排除 Organic Search 和 Direct 流量）
    public static void runReferralTrafficReport(BetaAnalyticsDataClient analyticsData,String date,String propertyId,KpiGaData kpiGaData) {
        // 构建请求并设置要查询的度量标准（使用 sessions 获取引荐流量）
        RunReportRequest request = RunReportRequest.newBuilder()
                .setProperty("properties/" + propertyId)
                .addMetrics(Metric.newBuilder().setName("sessions"))
                .addDimensions(Dimension.newBuilder().setName("sessionDefaultChannelGroup"))
                .addDateRanges(DateRange.newBuilder().setStartDate(date).setEndDate(date))
                .setDimensionFilter(
                FilterExpression.newBuilder()
                        .setNotExpression(
                                FilterExpression.newBuilder()
                                        .setFilter(
                                                Filter.newBuilder()
                                                        .setFieldName("sessionDefaultChannelGroup")
                                                        .setInListFilter(
                                                                Filter.InListFilter.newBuilder()
                                                                        .addAllValues(
                                                                                new ArrayList<String>() {
                                                                                    {
                                                                                        add("Organic Search"); // 排除 Organic Search
                                                                                        add("Direct"); // 排除 Direct
                                                                                    }
                                                                                })))))
                .build();


        // 执行报告请求并获取结果
        try {
            RunReportResponse response = analyticsData.runReport(request);
            Long  sessions=0l;
            // 输出报告数据
            for (Row row : response.getRowsList()) {
                Long  session= Long.valueOf(row.getMetricValues(0).getValue());
                sessions=sessions+session;
            }
            kpiGaData.setSession(sessions);
            log.info("session: " + sessions);
        } catch (ApiException e) {
            kpiGaData.setSession(0l);
            log.info("Error during report request: " + e.getStatusCode().getCode());
        }
    }

    @Override
    public Long runNewUsersReportByDate(String path,String date,String propertyId ,String platform)   {
        try {
            BetaAnalyticsDataClient analyticsData = initializeAnalyticsDataClient(path);
            // 解析传入的日期字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate endDate = LocalDate.parse(date, formatter);

            String endDateString = endDate.format(formatter);
            // 构建请求并设置要查询的度量标准
            RunReportRequest request = RunReportRequest.newBuilder()
                    .setProperty("properties/" + propertyId)
                    .addMetrics(Metric.newBuilder().setName("newUsers"))
                    .addDimensions(Dimension.newBuilder().setName("date"))
                    .setDimensionFilter(
                            FilterExpression.newBuilder()
                                    .setFilter(
                                            Filter.newBuilder()
                                                    .setFieldName("platform")
                                                    .setStringFilter(
                                                            Filter.StringFilter.newBuilder().setValue(platform))))
                    .addDateRanges(DateRange.newBuilder().setStartDate(endDateString).setEndDate(endDateString))
                    .build();

            // 执行报告请求并获取结果
            try {
                // 假设这是从 API 获取到的响应数据
                RunReportResponse response = analyticsData.runReport(request);

                Long dayNewUsers = 0l;
                for (Row row : response.getRowsList()) {
                    dayNewUsers = Long.valueOf(row.getMetricValues(0).getValue());
                }
                // 输出结果
                log.info("todayNewUsers: " + dayNewUsers);
                return dayNewUsers;
            } catch (ApiException e) {
                log.info("Error during report request: " + e.getStatusCode().getCode());
                return 0l;
            }
        } catch (IOException e) {
            log.info("Error during report request: ",e);
            return 0l;
        }

    }
    public Map<String, Double> runCohortRetentionReport(String path, String date, String propertyId, String platform) {
        try {
            BetaAnalyticsDataClient analyticsData = initializeAnalyticsDataClient(path);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate cohortDate = LocalDate.parse(date, formatter);
            String cohortDateString = cohortDate.format(formatter);

            // **创建 CohortSpec**
            CohortSpec cohortSpec = CohortSpec.newBuilder()
                    .addCohorts(Cohort.newBuilder()
                            .setName("newUsers")
                            .setDimension("firstSessionDate")
                            .setDateRange(DateRange.newBuilder()
                                    .setStartDate(cohortDateString)
                                    .setEndDate(cohortDateString))
                    )
                    .setCohortsRange(
                            CohortsRange.newBuilder()
                                    .setStartOffset(0)  // 当天
                                    .setEndOffset(30)   // 追踪 30 天
                                    .setGranularity(CohortsRange.Granularity.DAILY) // **必须设置 DAILY**
                    )
                    .setCohortReportSettings(
                            CohortReportSettings.newBuilder().setAccumulate(false) // 不累积，按具体天数计算
                    )
                    .build();

            // **构建请求**
            RunReportRequest request = RunReportRequest.newBuilder()
                    .setProperty("properties/" + propertyId)
                    .addMetrics(Metric.newBuilder().setName("cohortTotalUsers"))  // 新用户数
                    .addMetrics(Metric.newBuilder().setName("cohortActiveUsers")) // 活跃用户数
                    .addDimensions(Dimension.newBuilder().setName("cohort"))  // **必须添加 cohort 维度**
                    .addDimensions(Dimension.newBuilder().setName("cohortNthDay")) // 第几天的留存情况
                    .setCohortSpec(cohortSpec) // **添加 CohortSpec**
                    .setDimensionFilter(
                            FilterExpression.newBuilder()
                                    .setFilter(Filter.newBuilder()
                                            .setFieldName("platform")
                                            .setStringFilter(Filter.StringFilter.newBuilder().setValue(platform))))
                    .build();

            // **执行查询**
            try {
                RunReportResponse response = analyticsData.runReport(request);
                Long totalUsers = 0L;
                Map<String, Long> activeUsers = new HashMap<>();

                // **解析 API 返回数据**
                for (Row row : response.getRowsList()) {
                    String cohortNthDay = row.getDimensionValues(1).getValue(); // 获取第 N 天的活跃用户数
                    Long users = Long.valueOf(row.getMetricValues(1).getValue()); // 活跃用户数
                    log.info("cohortNthDay: " + cohortNthDay + ", users: " + users);
                    if (cohortNthDay.equals("0001")) { // **次日留存**
                        activeUsers.put("day1", users);
                    } else if (Integer.parseInt(cohortNthDay) >= 3 && Integer.parseInt(cohortNthDay) <= 7) {
                        activeUsers.put("day3-7", activeUsers.getOrDefault("day3-7", 0L) + users);
                    } else if (Integer.parseInt(cohortNthDay) >= 8 && Integer.parseInt(cohortNthDay) <= 30) {
                        activeUsers.put("day8-30", activeUsers.getOrDefault("day8-30", 0L) + users);
                    }
                }

                // **获取 cohortTotalUsers**
                totalUsers = response.getRowsList().isEmpty() ? 0L :
                        Long.valueOf(response.getRowsList().get(0).getMetricValues(0).getValue());

                // **计算留存率**
                Map<String, Double> retentionRates = new HashMap<>();
                retentionRates.put("day1", totalUsers == 0 ? 0 : (activeUsers.getOrDefault("day1", 0L) * 100.0 / totalUsers));
                retentionRates.put("day3-7", totalUsers == 0 ? 0 : (activeUsers.getOrDefault("day3-7", 0L) * 100.0 / totalUsers));
                retentionRates.put("day8-30", totalUsers == 0 ? 0 : (activeUsers.getOrDefault("day8-30", 0L) * 100.0 / totalUsers));

                log.info("Cohort Retention Rates: " + retentionRates);
                return retentionRates;
            } catch (ApiException e) {
                log.info("Error during report request: " + e.getStatusCode().getCode());
                return new HashMap<>();
            }
        } catch (IOException e) {
            log.info("Error during report request: ", e);
            return new HashMap<>();
        }
    }

    @Override
    public Long getCohortRetentionReport(String path, String date, String propertyId, String platform,Integer start,Integer end) {
        try {
            BetaAnalyticsDataClient analyticsData = initializeAnalyticsDataClient(path);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate cohortDate = LocalDate.parse(date, formatter);
            String cohortDateString = cohortDate.format(formatter);

            // **创建 CohortSpec**
            CohortSpec cohortSpec = CohortSpec.newBuilder()
                    .addCohorts(Cohort.newBuilder()
                            .setName("newUsers")
                            .setDimension("firstSessionDate")
                            .setDateRange(DateRange.newBuilder()
                                    .setStartDate(cohortDateString)
                                    .setEndDate(cohortDateString))
                    )
                    .setCohortsRange(
                            CohortsRange.newBuilder()
                                    .setStartOffset(start)  // 追踪开始天数
                                    .setEndOffset(end)   // 追踪结束天数
                                    .setGranularity(CohortsRange.Granularity.DAILY) // **必须设置 DAILY**
                    )
                    .setCohortReportSettings(
                            CohortReportSettings.newBuilder().setAccumulate(false) // 不累积，按具体天数计算
                    )
                    .build();

            // **构建请求**
            RunReportRequest request = RunReportRequest.newBuilder()
                    .setProperty("properties/" + propertyId)
                    .addMetrics(Metric.newBuilder().setName("cohortTotalUsers"))  // 新用户数
                    .addMetrics(Metric.newBuilder().setName("cohortActiveUsers")) // 活跃用户数
                    .addDimensions(Dimension.newBuilder().setName("cohort"))  // **必须添加 cohort 维度**
                    .setCohortSpec(cohortSpec) // **添加 CohortSpec**
                    .setDimensionFilter(
                            FilterExpression.newBuilder()
                                    .setFilter(Filter.newBuilder()
                                            .setFieldName("platform")
                                            .setStringFilter(Filter.StringFilter.newBuilder().setValue(platform))))
                    .build();

            // **执行查询**
            try {
                RunReportResponse response = analyticsData.runReport(request);
                Long users = 0L;
                // **解析 API 返回数据**
                for (Row row : response.getRowsList()) {
                    users = Long.valueOf(row.getMetricValues(1).getValue()); // 活跃用户数
                }


                return users;
            } catch (ApiException e) {
                log.info("error:", e);
                log.info("Error during report request: " + e.getStatusCode().getCode());
                return 0l;
            }
        } catch (IOException e) {
            log.info("Error during report request: ", e);
            return 0l;
        }
    }
 

    public static void main(String[] args) throws IOException {
        GaServiceImpl gaService = new GaServiceImpl();
//        gaService.runNewUsersReportByDate(CREDENTIALS_JSON_PATH,"2025-03-10",PROPERTY_ID,"web");
//        gaService.runNewUsersReportByDate(CREDENTIALS_JSON_PATH_APP_Piclumen,"2025-03-10",PROPERTY_ID_APP_Piclumen,"iOS");
        gaService.runCohortRetentionReport(CREDENTIALS_JSON_PATH,"2025-02-15",PROPERTY_ID,"web");
        Long total =  gaService.runNewUsersReportByDate(CREDENTIALS_JSON_PATH,"2025-02-15",PROPERTY_ID,"web");
        System.out.println("2025-02-15首次访问量为"+total);
        Long morrow = gaService.getCohortRetentionReport(CREDENTIALS_JSON_PATH, "2025-02-15", PROPERTY_ID, "web", 1, 2);
        //        gaService.runNewUsersReportByDate(CREDENTIALS_JSON_PATH_APP_Piclumen,"2025-03-10",PROPERTY_ID_APP_Piclumen,"iOS");
        System.out.println("2025-02-15次日留存为"+morrow+"占比为"+ DoubleMathUtils.div(morrow, total, 4) * 100);

        Long three = gaService.getCohortRetentionReport(CREDENTIALS_JSON_PATH, "2025-02-15", PROPERTY_ID, "web", 2, 8);
//        gaService.runCohortRetentionReport1(CREDENTIALS_JSON_PATH_APP_Piclumen,"2025-02-14",PROPERTY_ID_APP_Piclumen,"iOS",3,7);
        System.out.println("2025-02-15 2-7日留存为"+three+"占比为"+ DoubleMathUtils.div(three, total, 4) * 100);

        Long     eight = gaService.getCohortRetentionReport(CREDENTIALS_JSON_PATH, "2025-02-15", PROPERTY_ID, "web", 8, 31);
        System.out.println("2025-02-15 8-30日留存为"+eight+"占比为"+ DoubleMathUtils.div(eight, total, 4) * 100);


    }
}
