package com.ai.constants;

/**
 * 逻辑参数对象 logic_params
 *
 * <AUTHOR>
 * @date 2023-12-10
 */

public class LogicParamsCons {

    public static final String GPU_INSTANCES = "gpuInstances";

    public static final String LOADBALANCE_MODEL = "loadbalance_model"; //模型redis key

    public static final String LOADBALANCE_FEATURE = "loadbalance_feature"; //功能redis key

    public static final String MODEL_FEATURE = "model_feature";  //模型功能redis key
    //监控服务下架保存对应实例下架的规则key
    public static final String MODEL_FEATURE_OF_DOWN = "model_feature_of_down";

    public static final String LB_SYSTEM_PARA = "lb_system_para";

    public static final String GUARANTEES = "m0f0"; //保底机制模型功能组

    public static final String FAIR_TASK_THRESHOLD = "fairTaskThreshold";   // 公平队列任务阈值

    public static final String UNFAIR_TASK_THRESHOLD = "unfairTaskThreshold"; // 非公平队列任务阈

    public static final String PRELOADING_TASK_THRESHOLD = "preloadingTaskThreshold"; // 预载gpu队列任务阈值

    public static final String BLOB_FAIR_WAIT_TIME = "blobFairWaitTime"; // 阻塞等待时长，毫秒

    public static final String DAY_MAX_TASK_SIZE = "dayMaxTaskSizeList"; // 最大并发任务数

    public static final String USER_TASK_TIMESTAMP = "user_task_timestamp";


    public static final String OTHER_TASK_SCHEDULING_CACHES = "otherTaskSchedulingCaches"; //其他缓存列表

    public static final String MARKID_SERVERID_LIST = "markId_serverId_list"; //其他缓存列表

    public static final String WEB_VERSION = "Web-Version"; //web 端版本号

    public static final String IOS_VERSION = "Ios-Version"; //ios 端版本号

    public static final String ANDROID_VERSION = "Android-Version"; //android版本号

    public static final String BL_RULE_FLUX = "blRuleFlux"; //拉黑flux实例key名

    public static final String BL_RULE_COMMON = "blRuleCommon"; //拉黑common实例key名


    public static final String ALL_FEATURE_SERVERIDS = "all_feature_serverIds"; //拉黑common实例key名


    public static final String MODEL_LIST_KEY = "modelListKey";  //模型列表

    public static final String WEB_LIST_KEY = "webModelListKey";  //模型列表_web

    public static final String IOS_LIST_KEY = "iosModelListKey";  //模型列表_ios

    public static final String ANDROID_LIST_KEY = "androidModelListKey";  //模型列表_android

    public static final String CHILD_PRON_COUNT = "child_pron_count";  // 儿童色情词过滤次数

    public static final String USER_RECHARGE_TOTAL_LUMENS = "user_recharge_total_lumens"; // 用户目前充值的有效总点数

    public static final String USER_VIP_TOTAL_LUMENS = "user_vip_total_lumens"; // 用户目前vip赠送的有效总点数

    public static final String USER_RECHARGE_USE_LUMENS = "user_recharge_use_lumens"; // 用户目前充值的已经使用点数

    public static final String USER_VIP_USE_LUMENS = "user_vip_use_lumens"; // 用户目前vip赠送的已使用点数

    public static final String SUSPENSION_VERSION = "suspension-Version"; // 停服公告版本号

    public static final String SUSPENSION_Time = "suspension-time"; // 停服时间

    public static final String SUSPENSION_Time_START = "suspension-start-time"; // 停服开始时间

    public static final String SUSPENSION_Time_END = "suspension-end-time"; // 停服结束时间

    public static final String PROMPT_File_RANKING = "prompt_file_rankin"; // 生图排名

    public static final String MODEL_NUM = "model_num";  // 模型生成数量

    public static final String AMOUNT_NUN = "amount_num";


    /**
     * 系统更新总数
     */
    public static final String SYSUPDATE_NUMS = "SYSUPDATE_NUMS";

    /**
     * web系统更新总数
     */
    public static final String WEB_SYSUPDATE_NUMS = "WEB_SYSUPDATE_NUMS";

    /**
     * ios系统更新总数
     */
    public static final String IOS_SYSUPDATE_NUMS = "IOS_SYSUPDATE_NUMS";

    /**
     * 安卓系统更新总数
     */
    public static final String ANDROID_SYSUPDATE_NUMS = "ANDROID_SYSUPDATE_NUMS";


    /**
     * 会员系统活动公告总数，包含给所有用户和vip用户的公告活动
     */
    public static final String VIP_PLATFORM_ACTIVITY_NUMS = "VIP_PLATFORM_ACTIVITY_NUMS";

    /**
     * 非会员系统活动公告总数，包含给所有用户和非vip用户的公告活动
     */
    public static final String NOT_VIP_PLATFORM_ACTIVITY_NUMS = "NOT_VIP_PLATFORM_ACTIVITY_NUMS";

    public static final String COMM_USER_NOT_READ_MESSAGE = "COMM_USER_NOT_READ_MESSAGE" + ":";

    public static final String NOT_READ_PLATFORM_MESSAGE_NUMS = "NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * web平台专属用户未读消息数
     */
    public static final String WEB_NOT_READ_PLATFORM_MESSAGE_NUMS = "WEB_NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * ios平台专属用户未读消息数
     */
    public static final String IOS_NOT_READ_PLATFORM_MESSAGE_NUMS = "IOS_NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * android平台专属用户未读消息数
     */
    public static final String ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS = "ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS";

    public static final String VIP_DAU = "vipDau:";

    public static final String WEB_QUESTIONNAIRE_ANSWER = "web_questionnaire_answer"; // web问卷用户信息

    public static final String IOS_QUESTIONNAIRE_ANSWER = "ios_questionnaire_answer"; // ios问卷用户信息

    public static final String ANDROID_QUESTIONNAIRE_ANSWER = "android_questionnaire_answer"; // android问卷用户信息

    public static final String WEB_QUESTIONNAIRE_RECORD = "web_questionnaire_record"; // web问卷

    public static final String IOS_QUESTIONNAIRE_RECORD = "ios_questionnaire_record"; // ios问卷

    public static final String ANDROID_QUESTIONNAIRE_RECORD = "android_questionnaire_record"; // android问卷

    /**
     * 数据库逻辑删除：未删除
     */
    public static final Integer DB_VALID = 0;

    /**
     * 数据库逻辑删除：已删除
     */
    public static final Integer DB_INVALID = 1;

    /**
     * 社区首页banner图缓存key
     */
    public static final String BANNER_IMG_CACHE_KEY = "cache:community_banner_img";

    public static final String AD_CONFIG_PUBLIC = "adconfig:public";

    public static final String AD_CONFIG_IOS = "adconfig:ios";

    /**
     * 每日UV统计redis缓存key前缀
     */
    public static final String DAILY_UNIQUE_VISITOR_KEY_PREFIX = "daily_unique_visitor:";

    /**
     * PUSH打开统计redis缓存key前缀
     */
    public static final String PUSH_OPEN_KEY_PREFIX = "push:open:";

    public static final String COMM_ACTIVITY_PUBLIC = "commActivity:public";

    /**
     * 自动推送开关
     */
    public static final String DISABLE_AUTO_PUSH_KEY = "push:disable_auto_push";

    /**
     * PUSH每日自动推送任务分布式锁key
     */
    public static final String DAILY_PUSH_EXECUTE_LOCK_KEY = "push:daily_push_execute_lock";

    /**
     * PUSH推送最近ID，用于任务中途异常终止的推送恢复
     */
    public static final String PUSH_LAST_ID_KEY_PREFIX = "push:last_id:";

    /**
     * PUSH手动推送任务分布式锁key
     */
    public static final String MANUAL_PUSH_EXECUTE_LOCK_KEY = "push:manual_push_execute_lock";

    /**
     * PUSH单次查找用户数量
     */
    public static final String PUSH_SELECT_USER_BATCH_SIZE_KEY = "push:select_user_batch_size";

    /**
     * 会员到期通知定时任务分布式锁key
     */
    public static final String MEMBER_EXPIRE_NOTIFICATION_LOCK_KEY = "member_expire_notification:execute_lock";

    /**
     * 会员到期通知最近ID，用于任务中途异常终止的推送恢复
     */
    public static final String MEMBER_EXPIRE_NOTIFICATION_LAST_ID_KEY = "member_expire_notification:last_id";

    /**
     * 会员到期通知任务上一次执行时间
     */
    public static final String MEMBER_EXPIRE_NOTIFICATION_LAST_EXECUTE_TIME_KEY = "member_expire_notification:last_execute_time";
}
