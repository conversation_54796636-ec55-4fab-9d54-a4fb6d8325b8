package com.ai.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * AI工具 Scheme 枚举
 * Android格式：aiease://aiease.ai/tools?tool={toolType}
 * iOS格式：aiease://aiease.ai/tools?tool={toolType}
 */
@Getter
@RequiredArgsConstructor
public enum SchemeEnum {
    ADVANCED_SETTINGS(
            "advanced_settings",
            "Advanced Settings",
            "piclumen://piclumen.com/create/advanced_settings",
            "https://www.piclumen.com/create/advanced_settings",
            "image"
    ),

    REWARDS_CENTER(
            "rewards_center",
            "Rewards Center",
            "piclumen://piclumen.com/rewards/rewards_center",
            "https://www.piclumen.com/rewards/rewards_center",
            "image"
    );


    private final String toolType;
    private final String description;
    private final String androidScheme;
    private final String iosScheme;
    private final String type;


    /**
     * 根据工具类型获取对应的枚举值
     *
     * @param toolType 工具类型
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static SchemeEnum getByToolType(String toolType) {
        if (toolType == null) {
            return null;
        }
        for (SchemeEnum scheme : values()) {
            if (scheme.getToolType().equals(toolType)) {
                return scheme;
            }
        }
        return null;
    }

    /**
     * 检查工具类型是否有效
     *
     * @param toolType 工具类型
     * @return 是否有效
     */
    public static boolean isValid(String toolType) {
        return getByToolType(toolType) != null;
    }
}
