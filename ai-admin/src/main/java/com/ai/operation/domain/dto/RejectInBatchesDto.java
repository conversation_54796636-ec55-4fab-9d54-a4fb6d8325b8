package com.ai.operation.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "批量拒绝传入参数")
public class RejectInBatchesDto {

   @ApiModelProperty("id数组")
   private Long[] ids;

   @ApiModelProperty("拒绝内容")
   private String rejectionContent;

   @ApiModelProperty("拒绝原因")
   private String rejectionType;
}
