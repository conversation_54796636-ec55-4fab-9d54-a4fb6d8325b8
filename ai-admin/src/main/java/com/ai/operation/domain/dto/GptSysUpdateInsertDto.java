package com.ai.operation.domain.dto;


import com.ai.common.annotation.Excel;
import com.ai.operation.domain.vo.SysUpdateDetailsForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "系统新增传参")
public class GptSysUpdateInsertDto {

    @ApiModelProperty("标题")
    private Long id;

    /** 标题 */
    @ApiModelProperty("标题")
    private String title;

    /** 简介 */
    @ApiModelProperty("简介")
    private String introduction;

    /** 详情 */
    @ApiModelProperty("详情")
    private String details;

    @ApiModelProperty("详情")
    private SysUpdateDetailsForm detailsForm;

    /**
     * 来源平台： web android ios
     */
    private String platform;

}
