package com.ai.operation.domain;

import java.time.LocalDate;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * Kpi汇总对象 kpi_mix
 *
 * <AUTHOR>
 * @date 2024-08-02
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "kpi_mix", description = "Kpi汇总")
@TableName("kpi_mix")
public class KpiMix extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 日期 */
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate recordDate;

    /** 平均任务生图时间 */
    @ApiModelProperty("平均任务生图时间")
    @Excel(name = "平均任务生图时间")
    private Double avgChartTime;

    /** 平均等待时长 */
    @ApiModelProperty("平均等待时长")
    @Excel(name = "平均等待时长")
    private Double avgWait;

    /** 1图平均生图时间 */
    @ApiModelProperty("1图平均生图时间")
    @Excel(name = "1图平均生图时间")
    private Double avgChartOneTime;

    /** 2图平均生图时间 */
    @ApiModelProperty("2图平均生图时间")
    @Excel(name = "2图平均生图时间")
    private Double avgChartTwoTime;

    /** 3图平均生图时间 */
    @ApiModelProperty("3图平均生图时间")
    @Excel(name = "3图平均生图时间")
    private Double avgChartThreeTime;

    /** 4图平均生图时间 */
    @ApiModelProperty("4图平均生图时间")
    @Excel(name = "4图平均生图时间")
    private Double avgChartFourTime;

    /** 平均单图生成时间 */
    @ApiModelProperty("平均单图生成时间")
    @Excel(name = "平均单图生成时间")
    private Double avgPerChartTime;

    /** 生图数量 */
    @ApiModelProperty("生图数量")
    @Excel(name = "生图数量")
    private Long chartCount;

    /** 日活 */
    @ApiModelProperty("日活")
    @Excel(name = "日活")
    private Long dau;

    /** 周活 */
    @ApiModelProperty("周活")
    @Excel(name = "周活")
    private Long wau;

    /** 月活 */
    @ApiModelProperty("月活")
    @Excel(name = "月活")
    private Long mau;

    /** 新注册用户量 */
    @ApiModelProperty("新注册用户量")
    @Excel(name = "新注册用户量")
    private Long newRegisters;

    /** 总用户量 */
    @ApiModelProperty("总用户量")
    @Excel(name = "总用户量")
    private Long totalRegisters;

    /** 单用户平均生图数量 */
    @ApiModelProperty("单用户平均生图数量")
    @Excel(name = "单用户平均生图数量")
    private Long avgChartsPerUser;

    /** 当日最大并发生图任务数 */
    @ApiModelProperty("当日最大并发生图任务数")
    @Excel(name = "当日最大并发生图任务数")
    private Long maxConcurrentChartTasks;

    /** 单用户最大生图数量 */
    @ApiModelProperty("单用户最大生图数量")
    @Excel(name = "单用户最大生图数量")
    private Long maxChartsPerUser;

    /** 生成单图任务数 */
    @ApiModelProperty("生成单图任务数")
    @Excel(name = "生成单图任务数")
    private Long chartCountOne;

    /** 生成2图任务数 */
    @ApiModelProperty("生成2图任务数")
    @Excel(name = "生成2图任务数")
    private Long chartCountTwo;

    /** 生成3图任务数 */
    @ApiModelProperty("生成3图任务数")
    @Excel(name = "生成3图任务数")
    private Long chartCountThree;

    /** 生成4图任务数 */
    @ApiModelProperty("生成4图任务数")
    @Excel(name = "生成4图任务数")
    private Long chartCountFour;

    /** 文生图任务数 */
    @ApiModelProperty("文生图任务数")
    @Excel(name = "文生图任务数")
    private Long text2picTasks;

    /** 高清修复任务数 */
    @ApiModelProperty("高清修复任务数")
    @Excel(name = "高清修复任务数")
    private Long hiresfixTasks;

    /** 去背景任务数 */
    @ApiModelProperty("去背景任务数")
    @Excel(name = "去背景任务数")
    private Long removebgTasks;

    /** 图生图任务数 */
    @ApiModelProperty("图生图任务数")
    @Excel(name = "图生图任务数")
    private Long pic2picTasks;

    /** 快速生图任务数 */
    @ApiModelProperty("快速生图任务数")
    @Excel(name = "快速生图任务数")
    private Long fastTasks;

    /** 单用户remix点击次数 */
    @ApiModelProperty("单用户remix点击次数")
    @Excel(name = "单用户remix点击次数")
    private Long remixPerUser;

    /** 生图任务数量 */
    @ApiModelProperty("生图任务数量")
    @Excel(name = "生图任务数量")
    private Long chartTaskCount;

    /** 图生图character_ref次数 */
    @ApiModelProperty("图生图character_ref次数")
    @Excel(name = "图生图character_ref次数")
    private Long pic2picCharacterRef;

    /** 图生图content_ref次数 */
    @ApiModelProperty("图生图content_ref次数")
    @Excel(name = "图生图content_ref次数")
    private Long pic2picContentRef;

    /** 图生图style_ref次数 */
    @ApiModelProperty("图生图style_ref次数")
    @Excel(name = "图生图style_ref次数")
    private Long pic2picStyleRef;

    /** 最喜爱生图比例 */
    @ApiModelProperty("最喜爱生图比例")
    @Excel(name = "最喜爱生图比例")
    private String favoriteAspectRatio;

    /** realistic生图数量 */
    @ApiModelProperty("realistic生图数量")
    @Excel(name = "realistic生图数量")
    private Long realisticCount;

    /** anime生图数量 */
    @ApiModelProperty("anime生图数量")
    @Excel(name = "anime生图数量")
    private Long animeCount;

    /** lineart生图数量 */
    @ApiModelProperty("lineart生图数量")
    @Excel(name = "lineart生图数量")
    private Long lineartCount;


    /** 当日Relax任务数量 */
    @ApiModelProperty("当日Relax任务数量")
    @Excel(name = "当日Relax任务数量")
    private Long  relaxChartTaskRate;

    /** 当日Fast任务数量 */
    @ApiModelProperty("当日Fast任务数量")
    @Excel(name = "当日Fast任务数量")
    private Long  fastChartTaskRate;

    /** 任务执行成功率 */
    @ApiModelProperty("任务执行成功率")
    @Excel(name = "任务执行成功率")
    private Double  chartSuccessTaskRate;

    /** 当日成功完成任务数量 */
    @ApiModelProperty("当日成功完成任务数量")
    @Excel(name = "当日成功完成任务数量")
    private Long  chartSuccessTaskCount;

    /** Relax任务，单图生成耗时 */
    @ApiModelProperty("Relax任务，单图生成耗时")
    @Excel(name = "Relax任务，单图生成耗时")
    private Double  relaxOnePicTime;

    /** relax任务，任务平均时间 */
    @ApiModelProperty("relax任务，任务平均时间")
    @Excel(name = "relax任务，任务平均时间")
    private Double  relaxTaskAvgTime;

    /** Fast任务，单图生成耗时 */
    @ApiModelProperty("Fast任务，单图生成耗时")
    @Excel(name = "Fast任务，单图生成耗时")
    private Double  fastOnePicTime;

    /** Fast任务，任务平均时间 */
    @ApiModelProperty("Fast任务，任务平均时间")
    @Excel(name = "Fast任务，任务平均时间")
    private Double  fastTaskAvgTime;

    /** 用户上传任务数 */
    @ApiModelProperty("用户上传任务数")
    @Excel(name = "用户上传任务数")
    private Long  customUploadTasks;

    /** 阔图任务数 */
    @ApiModelProperty("阔图任务数")
    @Excel(name = "阔图任务数")
    private Long  enlargeImageTasks;

    /** 线稿上色任务数 */
    @ApiModelProperty("线稿上色任务数")
    @Excel(name = "线稿上色任务数")
    private Long  lineRecolorTasks;

    /** 局部重绘任务数 */
    @ApiModelProperty("局部重绘任务数")
    @Excel(name = "局部重绘任务数")
    private Long  localRedrawTasks;

    /** 图片渐变任务数 */
    @ApiModelProperty("图片渐变任务数")
    @Excel(name = "varyTasksvaryTasks")
    private Long  varyTasks;

    /** flux 任务数 */
    @ApiModelProperty("flux 任务数")
    @Excel(name = "flux 任务数")
    private Long  fluxCount ;

    /** pony 任务数 */
    @ApiModelProperty("pony 任务数")
    @Excel(name = "pony 任务数")
    private Long  ponyCount ;

    /** art 任务数 */
    @ApiModelProperty("art 任务数")
    @Excel(name = "art 任务数")
    private Long artCount;

    @ApiModelProperty("flux dev  任务数")
    @Excel(name = "flux dev  任务数")
    private Long  fluxDevCount;

    /** 非fast当日最大并发生图任务数*/
    @ApiModelProperty("非fast当日最大并发生图任务数")
    @Excel(name = "非fast当日最大并发生图任务数")
    private Long unfairQueueMaxConcurrentChartTasks;

    /** fast当日最大并发生图任务数*/
    @ApiModelProperty("fast当日最大并发生图任务数")
    @Excel(name = "fast当日最大并发生图任务数")
    private Long fairQueueMaxConcurrentChartTasks;

    @ApiModelProperty("web当日UV")
    @Excel(name = "web当日UV")
    private Long webDuv;

    @ApiModelProperty("ios当日UV")
    @Excel(name = "ios当日UV")
    private Long iosDuv;

    @ApiModelProperty("android当日UV")
    @Excel(name = "android当日UV")
    private Long androidDuv;
}
