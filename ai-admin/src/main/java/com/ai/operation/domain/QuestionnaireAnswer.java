package com.ai.operation.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 用户问卷调查回答对象 questionnaire_answer
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "questionnaire_answer", description = "用户问卷调查回答")
@TableName("questionnaire_answer")
public class QuestionnaireAnswer extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 账号 */
    @ApiModelProperty("账号")
    @Excel(name = "账号")
    private String loginName;

    /** 问卷id */
    @ApiModelProperty("问卷id")
    @Excel(name = "问卷id")
    private String questionnaireId;

    /** 问卷标题 */
    @ApiModelProperty("问卷标题")
    @Excel(name = "问卷标题")
    private String questionnaireTitle;

    /** 平台信息 */
    @ApiModelProperty("平台信息")
    @Excel(name = "平台信息")
    private String platform;

    /** 单选题1答案 */
    @ApiModelProperty("单选题1答案")
    @Excel(name = "单选题1答案")
    private String scAnswer1;

    /** 单选题2答案 */
    @ApiModelProperty("单选题2答案")
    @Excel(name = "单选题2答案")
    private String scAnswer2;

    /** 单选题3答案 */
    @ApiModelProperty("单选题3答案")
    @Excel(name = "单选题3答案")
    private String scAnswer3;

    /** 单选题4答案 */
    @ApiModelProperty("单选题4答案")
    @Excel(name = "单选题4答案")
    private String scAnswer4;

    /** 单选题5答案 */
    @ApiModelProperty("单选题5答案")
    @Excel(name = "单选题5答案")
    private String scAnswer5;

    /** 单选题6答案 */
    @ApiModelProperty("单选题6答案")
    @Excel(name = "单选题6答案")
    private String scAnswer6;

    /** 单选题7答案 */
    @ApiModelProperty("单选题7答案")
    @Excel(name = "单选题7答案")
    private String scAnswer7;

    /** 单选题8答案 */
    @ApiModelProperty("单选题8答案")
    @Excel(name = "单选题8答案")
    private String scAnswer8;

    /** 单选题9答案 */
    @ApiModelProperty("单选题9答案")
    @Excel(name = "单选题9答案")
    private String scAnswer9;

    /** 单选题10答案 */
    @ApiModelProperty("单选题10答案")
    @Excel(name = "单选题10答案")
    private String scAnswer10;

    /** 多选题1答案 */
    @ApiModelProperty("多选题1答案")
    @Excel(name = "多选题1答案")
    private String mcAnswer1;

    /** 多选题2答案 */
    @ApiModelProperty("多选题2答案")
    @Excel(name = "多选题2答案")
    private String mcAnswer2;

    /** 多选题3答案 */
    @ApiModelProperty("多选题3答案")
    @Excel(name = "多选题3答案")
    private String mcAnswer3;

    /** 多选题4答案 */
    @ApiModelProperty("多选题4答案")
    @Excel(name = "多选题4答案")
    private String mcAnswer4;

    /** 多选题5答案 */
    @ApiModelProperty("多选题5答案")
    @Excel(name = "多选题5答案")
    private String mcAnswer5;

    @ApiModelProperty("多选题6答案")
    @Excel(name = "多选题6答案")
    private String mcAnswer6;

    @ApiModelProperty("多选题7答案")
    @Excel(name = "多选题7答案")
    private String mcAnswer7;

    @ApiModelProperty("多选题8答案")
    @Excel(name = "多选题8答案")
    private String mcAnswer8;

    @ApiModelProperty("多选题9答案")
    @Excel(name = "多选题9答案")
    private String mcAnswer9;

    @ApiModelProperty("多选题10答案")
    @Excel(name = "多选题10答案")
    private String mcAnswer10;

    /** 问答题1答案 */
    @ApiModelProperty("问答题1答案")
    @Excel(name = "问答题1答案")
    private String essayAnswer1;

    /** 问答题2答案 */
    @ApiModelProperty("问答题2答案")
    @Excel(name = "问答题2答案")
    private String essayAnswer2;

    /** 问答题3答案 */
    @ApiModelProperty("问答题3答案")
    @Excel(name = "问答题3答案")
    private String essayAnswer3;

    /** 问答题4答案 */
    @ApiModelProperty("问答题4答案")
    @Excel(name = "问答题4答案")
    private String essayAnswer4;

    /** 问答题5答案 */
    @ApiModelProperty("问答题5答案")
    @Excel(name = "问答题5答案")
    private String essayAnswer5;

    @ApiModelProperty("评分题1答案")
    @Excel(name = "评分题1答案")
    private String gradeAnswer1;

    @ApiModelProperty( "评分题2答案")
    @Excel(name = "评分题2答案")
    private String gradeAnswer2;

    @ApiModelProperty("评分题3答案")
    @Excel(name = "评分题3答案")
    private String gradeAnswer3;

    @ApiModelProperty("评分题4答案")
    @Excel(name = "评分题4答案")
    private String gradeAnswer4;

    @ApiModelProperty("评分题5答案")
    @Excel(name = "评分题5答案")
    private String gradeAnswer5;

    /** 0:未删除 1：已删除 */
    private Integer del;

}
