package com.ai.operation.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 社区赛事信息
 */
@Data
@Document(collection = "challenge")
public class Challenge {

    @Id
    private String id;

    /**
     * 赛事状态状态
     */
    private String status;

    /**
     * 赛事标签
     */
    private List<String> tag;


    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}