package com.ai.operation.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.CommActivityRewardSettings;

/**
 * 社区活动信息奖励配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ICommActivityRewardSettingsService extends IService<CommActivityRewardSettings> {
    /**
     * 查询社区活动信息奖励配置
     * 
     * @param id 社区活动信息奖励配置主键
     * @return 社区活动信息奖励配置
     */
    CommActivityRewardSettings selectCommActivityRewardSettingsById(Long id);

    /**
     * 查询社区活动信息奖励配置列表
     * 
     * @param commActivityRewardSettings 社区活动信息奖励配置
     * @return 社区活动信息奖励配置集合
     */
    List<CommActivityRewardSettings> selectCommActivityRewardSettingsList(CommActivityRewardSettings commActivityRewardSettings);

    /**
     * 新增社区活动信息奖励配置
     * 
     * @param commActivityRewardSettings 社区活动信息奖励配置
     * @return 结果
     */
    int insertCommActivityRewardSettings(CommActivityRewardSettings commActivityRewardSettings);

    /**
     * 修改社区活动信息奖励配置
     * 
     * @param commActivityRewardSettings 社区活动信息奖励配置
     * @return 结果
     */
    int updateCommActivityRewardSettings(CommActivityRewardSettings commActivityRewardSettings);

    /**
     * 批量删除社区活动信息奖励配置
     * 
     * @param ids 需要删除的社区活动信息奖励配置主键集合
     * @return 结果
     */
    int deleteCommActivityRewardSettingsByIds(Long[] ids);

    /**
     * 删除社区活动信息奖励配置信息
     * 
     * @param id 社区活动信息奖励配置主键
     * @return 结果
     */
    int deleteCommActivityRewardSettingsById(Long id);


}
