package com.ai.operation.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.ai.admin.domain.GptPromptFile;
import com.ai.admin.domain.GptUser;
import com.ai.admin.domain.repository.CommFileRepository;
import com.ai.admin.mapper.GptPromptFileMapper;
import com.ai.admin.mapper.GptPromptRecordMapper;
import com.ai.admin.mapper.GptUserMapper;
import com.ai.common.constant.HttpStatus;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.NoticeMessType;
import com.ai.common.enums.PublicType;
import com.ai.common.enums.ReviewStatus;
import com.ai.common.utils.ShapeFinderUtil;
import com.ai.constants.LogicParamsCons;
import com.ai.cos.CosCommonService;
import com.ai.defined.utils.ShardingTableCalculator;
import com.ai.operation.domain.CommActivity;
import com.ai.operation.domain.dto.ActivityImageUpdateDTO;
import com.ai.operation.domain.dto.GptPromptFileBatchRejectDto;
import com.ai.operation.domain.dto.RejectInBatchesDto;
import com.ai.operation.domain.entity.AccountInfo;
import com.ai.operation.domain.entity.CommFile;
import com.ai.operation.domain.entity.CommUser;
import com.ai.operation.domain.entity.mongo.UserPlatformMessage;
import com.ai.operation.domain.vo.GptPublicFileReviewPageVo;
import com.ai.operation.mapper.CommActivityMapper;
import com.ai.operation.mapper.GptExploreFileMapper;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.github.pagehelper.PageInfo;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.GptPublicFileReviewMapper;
import com.ai.operation.domain.GptPublicFileReview;
import com.ai.operation.service.IGptPublicFileReviewService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import static com.ai.cos.CosCommonService.buildFileKey;

/**
 * 用户图片公开审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-27
 */
@Service
@Slf4j
public class GptPublicFileReviewServiceImpl extends ServiceImpl<GptPublicFileReviewMapper, GptPublicFileReview> implements IGptPublicFileReviewService
{
    @Autowired
    private GptPublicFileReviewMapper gptPublicFileReviewMapper;


    @Resource
    private GptPromptFileMapper gptPromptFileMapper;


    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private GptUserMapper gptUserMapper;

    @Resource
    private CosCommonService cosCommonService;

    @Resource
    private CommActivityMapper commActivityMapper;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    /**
     * 查询用户图片公开审核
     *
     * @param id 用户图片公开审核主键
     * @return 用户图片公开审核
     */
    @Override
    public GptPublicFileReview selectGptPublicFileReviewById(Long id)
    {
        return gptPublicFileReviewMapper.selectGptPublicFileReviewById(id);
    }

    /**
     * 查询用户图片公开审核列表
     *
     * @param gptPublicFileReview 用户图片公开审核
     * @return 用户图片公开审核
     */
    @Override
    public List<GptPublicFileReviewPageVo> selectGptPublicFileReviewList(GptPublicFileReview gptPublicFileReview) {
        List<GptPublicFileReviewPageVo> gptPublicFileReviews = gptPublicFileReviewMapper.selectGptPublicFileReviewList(gptPublicFileReview);
        return gptPublicFileReviews;
    }

    /**
     * 新增用户图片公开审核
     *
     * @param gptPublicFileReview 用户图片公开审核
     * @return 结果
     */
    @Override
    public int insertGptPublicFileReview(GptPublicFileReview gptPublicFileReview)
    {
        gptPublicFileReview.setCreateTime(LocalDateTime.now());
        return gptPublicFileReviewMapper.insertGptPublicFileReview(gptPublicFileReview);
    }

    /**
     * 修改用户图片公开审核
     *
     * @param gptPublicFileReview 用户图片公开审核
     * @return 结果
     */
    @Override
    public  AjaxResult updateGptPublicFileReview(GptPublicFileReview gptPublicFileReview)
    {

        // 审核通过
        if (gptPublicFileReview.getReviewStatus().equals(ReviewStatus.pass.getValue())){

            //将图片类型改为公开
            LambdaUpdateWrapper <GptPromptFile> updateWrapper = new LambdaUpdateWrapper<GptPromptFile>();
            updateWrapper.set(GptPromptFile::getIsPublic, PublicType.publicity.getValue());
            updateWrapper.eq(GptPromptFile::getLoginName,gptPublicFileReview.getLoginName());
            updateWrapper.eq(GptPromptFile::getId,gptPublicFileReview.getFileId());
            gptPromptFileMapper.update(null,updateWrapper);

            // 插入mongodb 代码
            CommFile commFile = new CommFile();
            commFile.setFileCommentNums(0);
            commFile.setFileLikeNums(0);
            commFile.setFileId(gptPublicFileReview.getFileId());
            copyImgToCommunityAsync(gptPublicFileReview, commFile).join();

            if (StringUtils.isNotBlank(gptPublicFileReview.getBrief())) {
                // 获取 tags 和 describe
                List<String> tags = extractTags(gptPublicFileReview.getBrief());
//            String describe = extractDescribe(gptPublicFileReview.getBrief());
                commFile.setDescribe(gptPublicFileReview.getBrief());  // describe 是 String
                // 设置 tags 和 describe
                commFile.setTags(tags);  // tags 是 List<String>
            }
            commFile.setCreateTime(LocalDateTime.now());

            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setUserId(Long.valueOf(gptPublicFileReview.getCreateBy()));
            accountInfo.setUserLoginName(gptPublicFileReview.getLoginName());

            GptUser gptUser = gptUserMapper.selectById(gptPublicFileReview.getCreateBy());
            accountInfo.setUserName(gptUser.getUserName());
            accountInfo.setUserAvatarUrl(gptUser.getAvatarUrl());
            accountInfo.setWhetherPro(false);
            commFile.setAccountInfo(accountInfo);

            commFile.setGenInfo(gptPublicFileReview.getGenInfo());
            commFile.setPrompt(gptPublicFileReview.getPrompt());
            commFile.setRealWidth(gptPublicFileReview.getRealWidth());
            commFile.setRealHeight(gptPublicFileReview.getRealHeight());
            commFile.setPublicType(gptPublicFileReview.getPublicType());
            if (!Objects.isNull(gptPublicFileReview.getPassActivity())&& gptPublicFileReview.getPassActivity()
                    && !Objects.isNull(gptPublicFileReview.getActivityId())){
                commFile.setActivityId(gptPublicFileReview.getActivityId());
                commFile.setActivityDeleted(Boolean.FALSE);
                LambdaUpdateWrapper<CommActivity> cau = new LambdaUpdateWrapper<>();
                cau.setSql("image_num = image_num + 1");
                cau.eq(CommActivity::getId, gptPublicFileReview.getActivityId());
                commActivityMapper.update(null, cau);
            }
            commFile.setChallenge(null);
            mongoTemplate.save(commFile);

            // 发布到社区图片数加1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(Long.valueOf(gptPublicFileReview.getCreateBy()))),
                    new Update().inc("publicImgNums", 1),
                    CommUser.class
            );
            return AjaxResult.success(gptPublicFileReviewMapper.deleteById(gptPublicFileReview.getId()));
        }else if (gptPublicFileReview.getReviewStatus().equals(ReviewStatus.rejection.getValue())){

            // 把图片公开状态改为已拒绝
            LambdaUpdateWrapper<GptPromptFile> fuw = new LambdaUpdateWrapper<>();
            fuw.eq(GptPromptFile::getLoginName,gptPublicFileReview.getLoginName());
            fuw.eq(GptPromptFile::getFileName,gptPublicFileReview.getFileName());
            fuw.eq(GptPromptFile::getPromptId,gptPublicFileReview.getPromptId());
            fuw.eq(GptPromptFile::getDel,Boolean.FALSE);
            fuw.set(GptPromptFile::getIsPublic,PublicType.reject.getValue());
            fuw.set(GptPromptFile::getRejectionContent, gptPublicFileReview.getRejectionContent());

            gptPromptFileMapper.update(null,fuw);

            // 拒绝图片为逻辑删除
            LambdaUpdateWrapper<GptPublicFileReview> puw = new LambdaUpdateWrapper<>();
            puw.set(GptPublicFileReview::getDel, Boolean.TRUE);
            puw.eq(GptPublicFileReview::getId,gptPublicFileReview.getId());
            return AjaxResult.success(gptPublicFileReviewMapper.update(null,puw));
        }

        gptPublicFileReview.setUpdateTime(LocalDateTime.now());
        return   AjaxResult.success(gptPublicFileReviewMapper.updateGptPublicFileReview(gptPublicFileReview));
    }

    private CompletableFuture<Void> copyImgToCommunityAsync(GptPublicFileReview gptPublicFileReview, CommFile commFile) {

        String fileUrl = gptPublicFileReview.getFileUrl();
        String thumbnailUrl = gptPublicFileReview.getThumbnailUrl();
        String highThumbnailUrl = gptPublicFileReview.getHighThumbnailUrl();
        String highMiniUrl = gptPublicFileReview.getHighMiniUrl();
        String miniThumbnailUrl = gptPublicFileReview.getMiniThumbnailUrl();

        Map<String, String> copyMap = new HashMap<>();
        Map<String, String> copyMapOld = new HashMap<>();

        if (highThumbnailUrl != null) {
            String highThumbnailKey =  buildFileKey("community","webp");
            commFile.setHighThumbnailUrl(cosCommonService.getConfig().getAccelerateDomain() + highThumbnailKey);
            commFile.setFileUrl(cosCommonService.getConfig().getAccelerateDomain() + highThumbnailKey);
            addToMap(highThumbnailUrl, highThumbnailKey, copyMap, copyMapOld);
        } else {
            String fileKey = buildFileKey("community", "webp");
            commFile.setFileUrl(cosCommonService.getConfig().getAccelerateDomain() + fileKey);
            addToMap(fileUrl, fileKey, copyMap, copyMapOld);
        }

        if (thumbnailUrl != null){
            String thumbnailUrlKey = buildFileKey("community", "webp");
            commFile.setThumbnailUrl(cosCommonService.getConfig().getAccelerateDomain() + thumbnailUrlKey);
            addToMap(thumbnailUrl, thumbnailUrlKey, copyMap, copyMapOld);
        }

        if (miniThumbnailUrl != null) {
            String miniThumbnailUrlKey =  buildFileKey("community","webp");
            commFile.setMiniThumbnailUrl(cosCommonService.getConfig().getAccelerateDomain() + miniThumbnailUrlKey);
            addToMap(miniThumbnailUrl, miniThumbnailUrlKey, copyMap, copyMapOld);
        }

        if (highMiniUrl != null) {
            String highMiniUrlKey = buildFileKey("community", "webp");
            commFile.setHighMiniUrl(cosCommonService.getConfig().getAccelerateDomain() + highMiniUrlKey);
            addToMap(highMiniUrl, highMiniUrlKey, copyMap, copyMapOld);
        }

        CompletableFuture<Void> voidFuture = cosCommonService.copyObject(copyMap, copyMapOld);
        return voidFuture;
    }

    private static void addToMap(String fileUrl, String fileKey, Map<String, String> copyMap, Map<String, String> copyMapOld) {
        if (StringUtils.isBlank(fileUrl)) {
            return;
        }
        if (fileUrl.contains("piclumen-1324066212")) {
            copyMapOld.put(URLUtil.getPath(fileUrl), fileKey);
        } else {
            copyMap.put(URLUtil.getPath(fileUrl), fileKey);
        }
    }

    /**
     * 批量删除用户图片公开审核
     *
     * @param ids 需要删除的用户图片公开审核主键
     * @return 结果
     */
    @Override
    public int deleteGptPublicFileReviewByIds(Long[] ids)
    {
        LambdaUpdateWrapper<GptPublicFileReview> updateWrapper = new LambdaUpdateWrapper<>();

        updateWrapper.set(GptPublicFileReview::getDel,Boolean.TRUE);
        updateWrapper.in(GptPublicFileReview::getId,ids);

        return gptPublicFileReviewMapper.update(null,updateWrapper);
    }

    /**
     * 删除用户图片公开审核信息
     *
     * @param id 用户图片公开审核主键
     * @return 结果
     */
    @Override
    public int deleteGptPublicFileReviewById(Long id)
    {
        return gptPublicFileReviewMapper.deleteGptPublicFileReviewById(id);
    }

    // 提取标签
    public  List<String> extractTags(String brief) {
        // 定义正则表达式
        String regex = "#([^ \t\r\n\f!@#$%^&*()=+.,?\":{}|<>，。！？、；：“”‘’（）【】《》]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(brief);

        // 存储匹配到的标签
        List<String> tags = new ArrayList<>();

        // 遍历所有匹配项
        while (matcher.find()) {
            String tag = matcher.group(1).trim();
            tag = tag.replaceAll("\\p{Zs}+", "");
            // 提取匹配的标签内容（去掉 #）
            if (StringUtils.isNotBlank(tag)){
                tags.add(tag);
            }
        }

        return tags;
    }

    // 提取标签
//    public static List<String> extractTags(String input) {
//        List<String> validTags = new ArrayList<>();
//
//        // 以 "#" 分隔字符串
//        String[] parts = input.split("#");
//
//        for (String part : parts) {
//            // 去掉首尾空格
//            String trimmedPart = part.trim();
//
//            // 如果部分为空，或者不包含有效字符（如汉字、字母、数字），跳过
//            if (trimmedPart.isEmpty() || startsWithPunctuation(trimmedPart)) {
//                continue;
//            }
//
//            // 分割含有空格的部分，确保只提取首个有效子部分作为标签
//            String[] subParts = trimmedPart.split("\\s+");
//            String tagCandidate = subParts[0].trim();
//
//            // 检查是否以字母或数字开头
//            if (!tagCandidate.isEmpty() && containsValidCharacters(tagCandidate)) {
//                validTags.add(tagCandidate);
//            }
//        }
//
//        return validTags;
//    }

    // 判断部分是否包含有效字符（汉字、字母、数字、日文等）
    private static boolean containsValidCharacters(String str) {
        return str.matches(".*[\\p{L}\\p{N}].*");
    }

    // 判断部分是否以标点符号开头
    private static boolean startsWithPunctuation(String str) {
        return str.matches("^[\\p{Punct}].*");
    }

    // 提取描述部分（去掉标签后的文本）
    public String extractDescribe(String brief) {
        StringBuilder describeBuilder = new StringBuilder();
        String[] parts = brief.split("\\s+");  // 使用正则分隔多个空格

        for (String part : parts) {
            part = part.trim();  // 去除前后空格
            if (!part.startsWith("#")) {
                describeBuilder.append(part).append(" ");  // 收集描述部分
            }
        }

        // 去掉尾部空格
        String describe = describeBuilder.toString().trim();
        return describe.isEmpty() ? "" : describe;  // 如果描述为空，返回空字符串
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult  passInBatches(Long[] ids,Boolean featured,Boolean activity){

        // 查询出列表
        LambdaQueryWrapper<GptPublicFileReview> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GptPublicFileReview::getId,ids);
        queryWrapper.eq(GptPublicFileReview::getReviewStatus,ReviewStatus.review.getValue());
        List<GptPublicFileReview> gptPublicFileReviews = gptPublicFileReviewMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(gptPublicFileReviews)){
            return AjaxResult.error("没有需要审核的图片");
        }

        if (gptPublicFileReviews.size() > 20){
            return AjaxResult.error("一次最多审核20张图片");
        }

        List<String> userIds = gptPublicFileReviews.stream().map(GptPublicFileReview::getCreateBy).collect(Collectors.toList());
        LambdaQueryWrapper<GptUser> quw = new LambdaQueryWrapper<>();
        quw.in(GptUser::getId,userIds);
        List<GptUser> gptUsers = gptUserMapper.selectList(quw);
        Map<String,String> userNameMap = new HashMap<>();
        Map<String,String> userAvatarUrlMap = new HashMap<>();
        for (GptUser gptUser : gptUsers) {
            userNameMap.put(gptUser.getId().toString(),gptUser.getUserName());
            userAvatarUrlMap.put(gptUser.getId().toString(),gptUser.getAvatarUrl());
        }

        List<CommFile> insertFiles = new ArrayList<>();
        Map<Long,Integer> activityIdMap = new HashMap<>();
        // 循环更新
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (GptPublicFileReview gptPublicFileReview : gptPublicFileReviews) {
            // 插入mongodb 代码
            CommFile commFile = new CommFile();
            commFile.setFileCommentNums(0);
            commFile.setFileLikeNums(0);
            commFile.setFileId(gptPublicFileReview.getFileId());
            commFile.setFileUrl(gptPublicFileReview.getFileUrl());
            commFile.setThumbnailUrl(gptPublicFileReview.getThumbnailUrl());
            commFile.setHighThumbnailUrl(gptPublicFileReview.getHighThumbnailUrl());
            if (StringUtils.isNotBlank(gptPublicFileReview.getBrief())) {
                // 获取 tags 和 describe
                List<String> tags = extractTags(gptPublicFileReview.getBrief());
                String describe = extractDescribe(gptPublicFileReview.getBrief());

                // 设置 tags 和 describe
                commFile.setTags(tags);  // tags 是 List<String>
                commFile.setDescribe(describe);  // describe 是 String
            }
            commFile.setCreateTime(LocalDateTime.now());

            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setUserId(Long.valueOf(gptPublicFileReview.getCreateBy()));
            accountInfo.setUserLoginName(gptPublicFileReview.getLoginName());
            accountInfo.setUserName(userNameMap.get(gptPublicFileReview.getCreateBy()));
            accountInfo.setUserAvatarUrl(userAvatarUrlMap.get(gptPublicFileReview.getCreateBy()));
            accountInfo.setWhetherPro(false);
            commFile.setAccountInfo(accountInfo);

            CompletableFuture<Void> voidCompletableFuture = copyImgToCommunityAsync(gptPublicFileReview, commFile);
            futures.add(voidCompletableFuture);
            if (!Objects.isNull(featured)&&featured){
                commFile.setFeatured(Boolean.TRUE);
            }
            commFile.setGenInfo(gptPublicFileReview.getGenInfo());
            commFile.setPrompt(gptPublicFileReview.getPrompt());
            commFile.setRealWidth(gptPublicFileReview.getRealWidth());
            commFile.setRealHeight(gptPublicFileReview.getRealHeight());
            commFile.setPublicType(gptPublicFileReview.getPublicType());
            if (activity && !Objects.isNull(gptPublicFileReview.getActivityId())){
                commFile.setActivityId(gptPublicFileReview.getActivityId());
                commFile.setActivityDeleted(Boolean.FALSE);
                if (Objects.isNull(activityIdMap.get(gptPublicFileReview.getActivityId()))){
                    activityIdMap.put(gptPublicFileReview.getActivityId(),1);
                }else{
                    Integer count = activityIdMap.get(gptPublicFileReview.getActivityId());
                    activityIdMap.put(gptPublicFileReview.getActivityId(),count+1);
                }
            }
            commFile.setChallenge(null);
            insertFiles.add(commFile);
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

//        Collection<CommFile> saveFiles = new ArrayList<>(insertFiles); // 如果saveFiles不是Collection
        Collection<CommFile> commFiles = mongoTemplate.insertAll(insertFiles);

        if (!Objects.isNull(featured) && featured) {
            List<UserPlatformMessage> insertUserPlatformMessages = new ArrayList<>();

            String newFileId = null;
            for (CommFile file : commFiles) {
                boolean assignableRatio = ShapeFinderUtil.isAssignableRatio(file.getRealWidth(), file.getRealHeight(), "9:16");
                if (assignableRatio) {
                    newFileId = file.getId();
                }
                // 刷新reids
                Integer nPlatformMessageNums =
                        (int) Optional.ofNullable(redisCachePiclumen.
                                getCacheMapValue(LogicParamsCons.COMM_USER_NOT_READ_MESSAGE + file.getAccountInfo().getUserLoginName(),LogicParamsCons.NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
                nPlatformMessageNums++;
                redisCachePiclumen.setCacheMapValue(LogicParamsCons.COMM_USER_NOT_READ_MESSAGE + file.getAccountInfo().getUserLoginName(),LogicParamsCons.NOT_READ_PLATFORM_MESSAGE_NUMS,nPlatformMessageNums);
                UserPlatformMessage userPlatformMessage = new UserPlatformMessage();
                userPlatformMessage.setAccountInfo(file.getAccountInfo());
                userPlatformMessage.setTitle("Your Creation is Featured!");
                userPlatformMessage.setIntroduction("Great news! Your image has been selected as Featured.");
                userPlatformMessage.setFileId(file.getId());
                userPlatformMessage.setCreateTime(LocalDateTime.now());
                userPlatformMessage.setMiniThumbnailUrl(file.getMiniThumbnailUrl());
                userPlatformMessage.setRead(Boolean.FALSE);
                userPlatformMessage.setMessType(NoticeMessType.featured.getValue());
                insertUserPlatformMessages.add(userPlatformMessage);
            }
            if (newFileId != null) {
                redisCachePiclumen.stringSet("img:visitor:login", newFileId);
            }
            mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, UserPlatformMessage.class).insert(insertUserPlatformMessages).execute();
        }
        Map<AccountInfo, List<Long>> collect = insertFiles.stream().collect(Collectors.groupingBy(e -> e.getAccountInfo(),
                Collectors.mapping(o ->Long.valueOf(o.getFileId()), Collectors.toList())));

        collect.forEach((accountInfo, fileIds) -> {
            LambdaUpdateWrapper<GptPromptFile> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(GptPromptFile::getIsPublic, PublicType.publicity.getValue());
            updateWrapper.eq(GptPromptFile::getLoginName, accountInfo.getUserLoginName());
            updateWrapper.in(GptPromptFile::getId, fileIds);
            gptPromptFileMapper.update(null, updateWrapper);

            // 发布到社区图片数加n
             mongoTemplate.updateFirst(
                        new Query(Criteria.where("accountInfo.userId").is(accountInfo.getUserId())),
                        new Update().inc("publicImgNums", fileIds.size()),
                        CommUser.class);

        });

        // 添加活动投稿数量
        if (activity) {
            List<ActivityImageUpdateDTO> updateList = new ArrayList<>();
            activityIdMap.forEach((activityId, size) -> {
                updateList.add(new ActivityImageUpdateDTO(activityId, size));
            });

            if (!CollectionUtils.isEmpty(updateList)) {
                commActivityMapper.batchUpdateActivityImageNum(updateList);
            }
        }


        List<Long> publicids = gptPublicFileReviews.stream().map(GptPublicFileReview::getId).collect(Collectors.toList());
        LambdaQueryWrapper<GptPublicFileReview> dqpw = new LambdaQueryWrapper<>();
        dqpw.in(GptPublicFileReview::getId, publicids);
        gptPublicFileReviewMapper.delete(dqpw);
        return AjaxResult.success();
    }


    @Override
    public AjaxResult rejectInBatches(RejectInBatchesDto rejectInBatchesDto) {
        // 查询待审核数据
        LambdaQueryWrapper<GptPublicFileReview> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GptPublicFileReview::getId, rejectInBatchesDto.getIds());
        queryWrapper.eq(GptPublicFileReview::getReviewStatus, ReviewStatus.review.getValue());
        List<GptPublicFileReview> reviewList = gptPublicFileReviewMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(reviewList)) {
            return AjaxResult.error("没有需要审核的图片");
        }
        if (reviewList.size() > 20) {
            return AjaxResult.error("一次最多审核20张图片");
        }

        // 构造分表批处理结构
        Map<String, GptPromptFileBatchRejectDto> batchMap = new HashMap<>();

        for (GptPublicFileReview review : reviewList) {
            String tableName = ShardingTableCalculator.getGptPromptFileTable(review.getLoginName());
            GptPromptFileBatchRejectDto dto = batchMap.computeIfAbsent(tableName, key -> {
                GptPromptFileBatchRejectDto d = new GptPromptFileBatchRejectDto();
                d.setTableName(tableName);
                d.setIsPublic(PublicType.reject.getValue());
                d.setRejectionContent(rejectInBatchesDto.getRejectionContent());
                d.setLoginNames(new ArrayList<>());
                d.setIds(new ArrayList<>());
                return d;
            });
            dto.getLoginNames().add(review.getLoginName());
            dto.getIds().add(review.getFileId());
        }

        // 只调用一次 mapper 方法
        gptPromptFileMapper.batchRejectMultiTable(new ArrayList<>(batchMap.values()));

        // 批量更新 review 表
        List<Long> reviewIds = reviewList.stream().map(GptPublicFileReview::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<GptPublicFileReview> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(GptPublicFileReview::getId, reviewIds);
        updateWrapper.set(GptPublicFileReview::getReviewStatus, ReviewStatus.rejection.getValue());
        updateWrapper.set(GptPublicFileReview::getRejectionContent, rejectInBatchesDto.getRejectionContent());
        updateWrapper.set(GptPublicFileReview::getDel, true);

        gptPublicFileReviewMapper.update(null, updateWrapper);

        return AjaxResult.success();
    }


    @Override
    public TableDataInfo  getRecentThreeDaysData(Integer page, Integer pageSize,
                                                String markFileId, Boolean isNext,
                                                LocalDate recordDate,Boolean featured,
                                                String loginName,String userName,
                                                String publicType,String id, Long activityId) {
        // 校验页码和每页记录数的有效性
        if (pageSize == null || pageSize < 1) {
            throw new IllegalArgumentException("Page and pageSize must be greater than 0");
        }

        // 获取当前时间
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_YEAR, -3);
        Date threeDaysAgo = calendar.getTime();

        // 创建时间过滤条件
        Criteria criteria = new Criteria();
        if (StringUtils.isBlank(loginName) && StringUtils.isBlank(userName) &&  StringUtils.isBlank(id)  &&  Objects.isNull(activityId)) {
            // 判断 recordDate 是否为空
            if (recordDate == null) {
                // 查询最近三天的数据
                criteria = criteria.and("createTime").gte(threeDaysAgo);
            } else {
                // 查询指定日期的数据，从当天凌晨00:00:00到23:59:59
                LocalDateTime startOfDay = recordDate.atStartOfDay();  // 获取当天凌晨
                LocalDateTime endOfDay = recordDate.atTime(23, 59, 59);  // 获取当天23:59:59
                Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
                Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

                // 设置查询时间范围：当天从00:00到23:59
                criteria = criteria.and("createTime").gte(startDate).lte(endDate);
            }
        }else {
            if (StringUtils.isNotBlank(loginName)) {
                criteria = criteria.and("accountInfo.userLoginName").is(loginName);
            }
            if (StringUtils.isNotBlank(userName)) {
                criteria = criteria.and("accountInfo.userName").is(userName);
            }
            if (recordDate != null) {
                // 查询指定日期的数据，从当天凌晨00:00:00到23:59:59
                LocalDateTime startOfDay = recordDate.atStartOfDay();  // 获取当天凌晨
                LocalDateTime endOfDay = recordDate.atTime(23, 59, 59);  // 获取当天23:59:59
                Date startDate = Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
                Date endDate = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

                // 设置查询时间范围：当天从00:00到23:59
                criteria = criteria.and("createTime").gte(startDate).lte(endDate);
            }
        }

        // 创建分页查询
        Query query = new Query(criteria);
        if (StringUtils.isNotBlank(markFileId)) {
            if (isNext) {
                query.addCriteria(Criteria.where("id").gt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.ASC, "id"));
            } else {
                query.addCriteria(Criteria.where("id").lt(new ObjectId(markFileId)));
                query.with(Sort.by(Sort.Direction.DESC, "id"));
            }
        } else {
            query.with(Sort.by(Sort.Direction.ASC, "id"));
        }

        if (!Objects.isNull(featured)){
            query.addCriteria(Criteria.where("featured").is(featured));
        }

        if (!Objects.isNull(publicType)){
            query.addCriteria(Criteria.where("publicType").is(publicType));
        }


        if (!Objects.isNull(id)){
            query.addCriteria(Criteria.where("id").is(id));
        }

        if (!Objects.isNull(activityId)){
            query.addCriteria(Criteria.where("activityId").is(activityId));
        }

        query.limit(pageSize);

        // 查询符合条件的数据
        List<CommFile> commFiles = mongoTemplate.find(query, CommFile.class);

        if (StringUtils.isNotBlank(markFileId) && !isNext) {
            Collections.reverse(commFiles);
        }

        // 封装响应数据
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(commFiles);
        rspData.setTotal(10);
        return rspData;
    }

    @Override
    public AjaxResult setFeatured(String[] ids){
        if (ids.length > 20){
            return AjaxResult.error("最多只能设置20张图片为精选");
        }
        mongoTemplate.updateMulti(
                new Query(Criteria.where("id").in(ids)),
                new Update().set("featured", Boolean.TRUE),
                CommFile.class
        );


        // 创建时间过滤条件
        Criteria criteria = new Criteria();
        Query query = new Query(criteria);
        query.addCriteria(Criteria.where("id").in(ids));
        List<CommFile> commFiles = mongoTemplate.find(query, CommFile.class);
        List<UserPlatformMessage> insertUserPlatformMessages = new ArrayList<>();
        for (CommFile commFile : commFiles) {
            // 刷新reids
            Integer nPlatformMessageNums =
                    (int) Optional.ofNullable(redisCachePiclumen.
                            getCacheMapValue(LogicParamsCons.COMM_USER_NOT_READ_MESSAGE + commFile.getAccountInfo().getUserLoginName(),LogicParamsCons.NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
            nPlatformMessageNums++;
            redisCachePiclumen.setCacheMapValue(LogicParamsCons.COMM_USER_NOT_READ_MESSAGE + commFile.getAccountInfo().getUserLoginName(),LogicParamsCons.NOT_READ_PLATFORM_MESSAGE_NUMS,nPlatformMessageNums);

            UserPlatformMessage userPlatformMessage = new UserPlatformMessage();
            userPlatformMessage.setAccountInfo(commFile.getAccountInfo());
            userPlatformMessage.setTitle("Your Creation is Featured!");
            userPlatformMessage.setIntroduction("Great news! Your image has been selected as Featured.");
            userPlatformMessage.setFileId(commFile.getId());
            userPlatformMessage.setCreateTime(LocalDateTime.now());
            userPlatformMessage.setMiniThumbnailUrl(commFile.getMiniThumbnailUrl());
            userPlatformMessage.setRead(Boolean.FALSE);
            userPlatformMessage.setMessType(NoticeMessType.featured.getValue());
            insertUserPlatformMessages.add(userPlatformMessage);
        }

        // 给客户发送图片已被精选的消息
        mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, UserPlatformMessage.class).insert(insertUserPlatformMessages).execute();
        return AjaxResult.success();
    }

    @Override
    public AjaxResult cancelFeatured(String[] ids){
        if (ids.length > 20){
            return AjaxResult.error("最多只能取消20张图片精选");
        }
        mongoTemplate.updateMulti(
                new Query(Criteria.where("id").in(ids)),
                new Update().set("featured", Boolean.FALSE),
                CommFile.class
        );
        return AjaxResult.success();
    }

    @Override
    public AjaxResult deleteImgs(String[] ids){

        // 删除社区图片
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(ids)); // 精确匹配当前用户id
        List<CommFile> commFiles = mongoTemplate.find(query, CommFile.class);

        Map<Long, List<String>> collect = commFiles.stream().collect(Collectors.groupingBy(e -> e.getAccountInfo().getUserId(),
                Collectors.mapping(o ->o.getId(), Collectors.toList())));


        collect.forEach((userId, fileIds) -> {
            //用户发布到社区的图片数减去对应数量
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("accountInfo.userId").is(userId)),
                    new Update().inc("publicImgNums", -fileIds.size()),
                    CommUser.class
            );
        });

        mongoTemplate.remove(query, CommFile.class);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult cancelActivityImgs(String[] ids) {
        // 查询指定 ID 的图片
        Query query = new Query();
        query.addCriteria(Criteria.where("id").in(ids).and("activityId").ne(null)); // 只处理有活动 ID 的图片

        List<CommFile> activityFiles = mongoTemplate.find(query, CommFile.class);
        if (activityFiles.isEmpty()) {
            return AjaxResult.error("未找到需要取消的活动图片");
        }

        // 批量更新，将 activityId 设置为 null
        Update update = new Update().unset("activityId");
        UpdateResult result = mongoTemplate.updateMulti(query, update, CommFile.class);

        return AjaxResult.success("已取消活动图片绑定，共处理 " + result.getModifiedCount() + " 条记录");
    }


}
