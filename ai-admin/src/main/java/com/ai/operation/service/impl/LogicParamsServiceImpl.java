package com.ai.operation.service.impl;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.utils.DateUtils;
import com.ai.common.utils.JsonUtils;
import com.ai.operation.domain.LogicParams;
import com.ai.operation.mapper.LogicParamsMapper;
import com.ai.operation.service.ILogicParamsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

/**
 * 逻辑参数Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-10
 */
@Service
@Slf4j
public class LogicParamsServiceImpl extends ServiceImpl<LogicParamsMapper, LogicParams> implements ILogicParamsService {
    @Autowired
    private LogicParamsMapper logicParamsMapper;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;


    /**
     * 查询逻辑参数
     * 
     * @param id 逻辑参数主键
     * @return 逻辑参数
     */
    @Override
    public LogicParams selectLogicParamsById(Long id)
    {
        return logicParamsMapper.selectLogicParamsById(id);
    }

    /**
     * 查询逻辑参数列表
     * 
     * @param logicParams 逻辑参数
     * @return 逻辑参数
     */
    @Override
    public List<LogicParams> selectLogicParamsList(LogicParams logicParams)
    {
        return logicParamsMapper.selectLogicParamsList(logicParams);
    }

    /**
     * 新增逻辑参数
     * 
     * @param logicParams 逻辑参数
     * @return 结果
     */
    @Override
    public int insertLogicParams(LogicParams logicParams)
    {
        logicParams.setCreateTime(DateUtils.getNowLocalDateTime());
        return logicParamsMapper.insertLogicParams(logicParams);
    }

    /**
     * 修改逻辑参数
     * 
     * @param logicParams 逻辑参数
     * @return 结果
     */
    @Override
    public int updateLogicParams(LogicParams logicParams)
    {
        logicParams.setUpdateTime(DateUtils.getNowLocalDateTime());
        return logicParamsMapper.updateLogicParams(logicParams);
    }

    /**
     * 批量删除逻辑参数
     * 
     * @param ids 需要删除的逻辑参数主键
     * @return 结果
     */
    @Override
    public int deleteLogicParamsByIds(Long[] ids)
    {
        return logicParamsMapper.deleteLogicParamsByIds(ids);
    }

    /**
     * 删除逻辑参数信息
     * 
     * @param id 逻辑参数主键
     * @return 结果
     */
    @Override
    public int deleteLogicParamsById(Long id)
    {
        return logicParamsMapper.deleteLogicParamsById(id);
    }

    @Override
    public AjaxResult refreshCacheLogicParams(Long id){
        LogicParams logicParams = logicParamsMapper.selectById(id);
        boolean validJson = JsonUtils.isValidJson(logicParams.getValueData());
        if (!validJson) {
            return AjaxResult.error("存储的值非json 格式");
        }
        log.info("删除缓存：{}",logicParams.getKeyName());
        redisCachePiclumen.deleteObject(logicParams.getKeyName());
        return AjaxResult.success();
    }
}
