package com.ai.operation.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.Platform;
import com.ai.common.utils.StringUtils;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.domain.dto.GptSysUpdateInsertDto;
import com.ai.operation.domain.vo.GptSysUpdateVo;
import com.ai.operation.domain.vo.SysUpdateDetailsForm;
import com.ai.operation.domain.vo.SysUpdateDomains;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.GptSysUpdateMapper;
import com.ai.operation.domain.GptSysUpdate;
import com.ai.operation.service.IGptSysUpdateService;

/**
 * 系统更新消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-20
 */
@Service
@Slf4j
public class GptSysUpdateServiceImpl extends ServiceImpl<GptSysUpdateMapper, GptSysUpdate> implements IGptSysUpdateService
{
    @Autowired
    private GptSysUpdateMapper gptSysUpdateMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;

    /**
     * 查询系统更新消息
     * 
     * @param id 系统更新消息主键
     * @return 系统更新消息
     */
    @Override
    public GptSysUpdateVo selectGptSysUpdateById(Long id)
    {
        GptSysUpdate gptSysUpdate = gptSysUpdateMapper.selectGptSysUpdateById(id);
        GptSysUpdateVo gptSysUpdateVo = new GptSysUpdateVo();
        BeanUtils.copyProperties(gptSysUpdate, gptSysUpdateVo);
        gptSysUpdateVo.setDetailsForm(new SysUpdateDetailsForm());

//        try {
//            SysUpdateDetailsForm sysUpdateDetailsForm = JsonUtils.fromString(gptSysUpdate.getDetails(), SysUpdateDetailsForm.class);
//            gptSysUpdateVo.setDetailsForm(sysUpdateDetailsForm);
//        } catch (JsonProcessingException e) {
//            gptSysUpdateVo.setDetailsForm(new SysUpdateDetailsForm());
//            log.info("列表查询系统更新时json解析错误",e);
//        }
        return gptSysUpdateVo;
    }

    /**
     * 查询系统更新消息列表
     * 
     * @param gptSysUpdate 系统更新消息
     * @return 系统更新消息
     */
    @Override
    public List<GptSysUpdateVo> selectGptSysUpdateList(GptSysUpdate gptSysUpdate)
    {
        List<GptSysUpdate> gptSysUpdates = gptSysUpdateMapper.selectGptSysUpdateList(gptSysUpdate);

        // 封装为前端需要的参数
        List<GptSysUpdateVo> gptSysUpdateVoList = new ArrayList<>();
        for (GptSysUpdate item : gptSysUpdates) {
            GptSysUpdateVo gptSysUpdateVo = new GptSysUpdateVo();
            BeanUtils.copyProperties(item, gptSysUpdateVo);
//            try {
//                gptSysUpdateVo.setDetails(formatUpdateDetails(JsonUtils.fromString(item.getDetails(),SysUpdateDetailsForm.class)));
////                gptSysUpdateVoList.add(gptSysUpdateVo);
//            } catch (JsonProcessingException e) {
//                gptSysUpdateVo.setDetails("");
//                log.info("列表查询系统更新时json解析错误",e);
//            }
            gptSysUpdateVoList.add(gptSysUpdateVo);

        }
        return gptSysUpdateVoList;
    }

    public String formatUpdateDetails(SysUpdateDetailsForm updateDetailsForm) {
        // 获取内容（content）
        StringBuilder result = new StringBuilder(updateDetailsForm.getContent()+"   ");

        // 获取 domains 并根据序号拼接每一项
        List<SysUpdateDomains> domains = updateDetailsForm.getDomains();


        // 遍历 domains 列表，生成后续的每一行内容
        for (int i = 0; i < domains.size(); i++) {
            result.append(domains.get(i).getNumber()).append(". ").append(domains.get(i).getValue()).append("; ");
        }

        return result.toString();
    }

    /**
     * 新增系统更新消息
     * 
     * @param gptSysUpdateInsertDto 系统更新消息新增传参
     * @return 结果
     */
    @Override
    public int insertGptSysUpdate(GptSysUpdateInsertDto gptSysUpdateInsertDto)
    {
        GptSysUpdate gptSysUpdate = new GptSysUpdate();
        BeanUtils.copyProperties(gptSysUpdateInsertDto, gptSysUpdate);
        if (StringUtils.isBlank(gptSysUpdateInsertDto.getPlatform())){
            // 默认为web
            gptSysUpdate.setPlatform(Platform.WEB.getPlatformName());
        }
        gptSysUpdate.setPublish(Boolean.FALSE);
        gptSysUpdate.setCreateTime(LocalDateTime.now());
        return gptSysUpdateMapper.insert(gptSysUpdate);
    }

    /**
     * 修改系统更新消息
     * 
     * @param gptSysUpdateInsertDto 系统更新消息
     * @return 结果
     */
    @Override
    public int updateGptSysUpdate(GptSysUpdateInsertDto gptSysUpdateInsertDto)
    {
        GptSysUpdate gptSysUpdate = new GptSysUpdate();
        BeanUtils.copyProperties(gptSysUpdateInsertDto, gptSysUpdate);
//        SysUpdateDetailsForm sysUpdateDetailsForm = gptSysUpdateInsertDto.getDetailsForm();
//        try {
//            gptSysUpdate.setDetails(JsonUtils.writeToString(sysUpdateDetailsForm));
//        } catch (JsonProcessingException e) {
//            gptSysUpdate.setDetails("");
//            log.info("新增系统更新时json解析错误",e);
//        }
        gptSysUpdate.setUpdateTime(LocalDateTime.now());
        return gptSysUpdateMapper.updateById(gptSysUpdate);
    }

    /**
     * 批量删除系统更新消息
     * 
     * @param ids 需要删除的系统更新消息主键
     * @return 结果
     */
    @Override
    public int deleteGptSysUpdateByIds(Long[] ids)
    {
        return gptSysUpdateMapper.deleteGptSysUpdateByIds(ids);
    }

    /**
     * 删除系统更新消息信息
     * 
     * @param id 系统更新消息主键
     * @return 结果
     */
    @Override
    public int deleteGptSysUpdateById(Long id)
    {
        return gptSysUpdateMapper.deleteGptSysUpdateById(id);
    }

    /**
     * 发布系统更新消息公告
     *
     * @param id 系统更新消息主键
     * @return 结果
     */
    @Override
    public Boolean toPublish(Long id) {

        GptSysUpdate gptSysUpdate = baseMapper.selectById(id);

        // 更新状态为已发布
        gptSysUpdate.setPublish(Boolean.TRUE);
        gptSysUpdate.setPublishTime(LocalDateTime.now());
        gptSysUpdate.setUpdateTime(LocalDateTime.now());

        gptSysUpdateMapper.updateById(gptSysUpdate);

        if (Platform.WEB.getPlatformName().equalsIgnoreCase(gptSysUpdate.getPlatform())) {
            Integer sysupdateNums = (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.WEB_SYSUPDATE_NUMS)).orElse(0);
            sysupdateNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.WEB_SYSUPDATE_NUMS, sysupdateNums);
        } else if (Platform.IOS.getPlatformName().equalsIgnoreCase(gptSysUpdate.getPlatform())) {
            Integer sysupdateNums = (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.IOS_SYSUPDATE_NUMS)).orElse(0);
            sysupdateNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.IOS_SYSUPDATE_NUMS, sysupdateNums);
        } else if (Platform.ANDROID.getPlatformName().equalsIgnoreCase(gptSysUpdate.getPlatform())) {
            Integer sysupdateNums = (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.ANDROID_SYSUPDATE_NUMS)).orElse(0);
            sysupdateNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.ANDROID_SYSUPDATE_NUMS, sysupdateNums);
        } else {
            Integer sysupdateNums = (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.SYSUPDATE_NUMS)).orElse(0);
            sysupdateNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.SYSUPDATE_NUMS, sysupdateNums);
        }
        return Boolean.TRUE;
    }


    /**
     * 刷新已发布的数据到 Redis
     *
     * @return 结果
     */
    @Override
    public Boolean refreshPublish() {
        // 统计所有已发布的数量
        LambdaQueryWrapper<GptSysUpdate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GptSysUpdate::getPublish, Boolean.TRUE);
        List<GptSysUpdate> gptSysUpdates = gptSysUpdateMapper.selectList(queryWrapper);
        Integer webNums = 0;
        Integer iosNums = 0;
        Integer androidNums = 0;
        Integer sysUpdateNums= 0;
        for  (GptSysUpdate gptSysUpdate:gptSysUpdates){
            if (Platform.WEB.getPlatformName().equalsIgnoreCase(gptSysUpdate.getPlatform())) {
                webNums++;
            } else if (Platform.IOS.getPlatformName().equalsIgnoreCase(gptSysUpdate.getPlatform())) {
                iosNums++;
            } else if (Platform.ANDROID.getPlatformName().equalsIgnoreCase(gptSysUpdate.getPlatform())) {
                androidNums++;
            } else {
                sysUpdateNums++;
            }
        }

        // 刷新 Redis
        redisCachePiclumen.setCacheObject(LogicParamsCons.SYSUPDATE_NUMS, sysUpdateNums);
        redisCachePiclumen.setCacheObject(LogicParamsCons.WEB_SYSUPDATE_NUMS, webNums);
        redisCachePiclumen.setCacheObject(LogicParamsCons.IOS_SYSUPDATE_NUMS, iosNums);
        redisCachePiclumen.setCacheObject(LogicParamsCons.ANDROID_SYSUPDATE_NUMS, androidNums);

        return Boolean.TRUE;
    }

}
