package com.ai.operation.service.impl;

import java.util.List;

import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.NoticeUserType;
import com.ai.constants.LogicParamsCons;
import com.ai.operation.domain.GptSysUpdate;
import com.ai.operation.domain.vo.UserTypeCountVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.operation.mapper.GptPlatformActivityMapper;
import com.ai.operation.domain.GptPlatformActivity;
import com.ai.operation.service.IGptPlatformActivityService;

/**
 * 平台公告通知Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-25
 */
@Service
public class GptPlatformActivityServiceImpl extends ServiceImpl<GptPlatformActivityMapper, GptPlatformActivity> implements IGptPlatformActivityService
{
    @Autowired
    private GptPlatformActivityMapper gptPlatformActivityMapper;

    @Autowired
    private RedisCachePiclumen redisCachePiclumen;


    /**
     * 查询平台公告通知
     * 
     * @param id 平台公告通知主键
     * @return 平台公告通知
     */
    @Override
    public GptPlatformActivity selectGptPlatformActivityById(Long id)
    {
        return gptPlatformActivityMapper.selectGptPlatformActivityById(id);
    }

    /**
     * 查询平台公告通知列表
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 平台公告通知
     */
    @Override
    public List<GptPlatformActivity> selectGptPlatformActivityList(GptPlatformActivity gptPlatformActivity)
    {
        return gptPlatformActivityMapper.selectGptPlatformActivityList(gptPlatformActivity);
    }

    /**
     * 新增平台公告通知
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 结果
     */
    @Override
    public int insertGptPlatformActivity(GptPlatformActivity gptPlatformActivity)
    {
        gptPlatformActivity.setCreateTime(LocalDateTime.now());
        gptPlatformActivity.setPublish(Boolean.FALSE);
        return gptPlatformActivityMapper.insert(gptPlatformActivity);
    }

    /**
     * 修改平台公告通知
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 结果
     */
    @Override
    public int updateGptPlatformActivity(GptPlatformActivity gptPlatformActivity)
    {
        gptPlatformActivity.setUpdateTime(LocalDateTime.now());
        return gptPlatformActivityMapper.updateGptPlatformActivity(gptPlatformActivity);
    }

    /**
     * 批量删除平台公告通知
     * 
     * @param ids 需要删除的平台公告通知主键
     * @return 结果
     */
    @Override
    public int deleteGptPlatformActivityByIds(Long[] ids)
    {
        return gptPlatformActivityMapper.deleteGptPlatformActivityByIds(ids);
    }

    /**
     * 删除平台公告通知信息
     * 
     * @param id 平台公告通知主键
     * @return 结果
     */
    @Override
    public int deleteGptPlatformActivityById(Long id)
    {
        return gptPlatformActivityMapper.deleteGptPlatformActivityById(id);
    }

    /**
     * 发布平台公告通知信息
     *
     * @param id 系统更新消息主键
     * @return 结果
     */
    @Override
    public Boolean toPublish(Long id){
        // 查询相应的公告通知
        GptPlatformActivity gptPlatformActivity = gptPlatformActivityMapper.selectById(id);

        // 更新状态为已发布
        gptPlatformActivity.setPublish(Boolean.TRUE);
        gptPlatformActivity.setPublishTime(LocalDateTime.now());
        gptPlatformActivity.setUpdateTime(LocalDateTime.now());
        gptPlatformActivityMapper.updateById(gptPlatformActivity);

        // 更新redis中的数据
        if (NoticeUserType.vip.getValue().equals(gptPlatformActivity.getUserType())){
            // vip 平台公告通知缓存+1
            Integer vipNums =  (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            vipNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS,vipNums);
        }else if (NoticeUserType.not_vip.getValue().equals(gptPlatformActivity.getUserType())){
            // 非vip 平台公告通知缓存+1
            Integer notVipNums =  (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            notVipNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS,notVipNums);
        } else if (NoticeUserType.all.getValue().equals(gptPlatformActivity.getUserType())) {
            // 非vip 和 vip 平台公告通知缓存都+1
            Integer vipNums =  (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            Integer notVipNums =  (int) Optional.ofNullable(redisCachePiclumen.getCacheObject(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            vipNums++;
            notVipNums++;
            redisCachePiclumen.setCacheObject(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS,vipNums);
            redisCachePiclumen.setCacheObject(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS,notVipNums);
        }
        return Boolean.TRUE;
    }

    /**
     * 刷新已发布的数据到redis
     *
     * @return 结果
     */
    @Override
    public Boolean refreshPublish() {

        // 查询各有多少条已经公布的数据
        List<UserTypeCountVo> userTypeCountVos = gptPlatformActivityMapper.selectUserTypeCount();
        Integer vipNums = 0;
        Integer notVipNums = 0;
        for (UserTypeCountVo userTypeCountVo : userTypeCountVos) {
            // 更新redis中的数据
            if (NoticeUserType.vip.getValue().equals(userTypeCountVo.getUserType())){
                // 加 vip 平台公告通知数量
                vipNums = vipNums + userTypeCountVo.getNumber();
            }else if (NoticeUserType.not_vip.getValue().equals(userTypeCountVo.getUserType())){
                //加 非vip 平台公告通知数量
                notVipNums = notVipNums + userTypeCountVo.getNumber();
            } else if (NoticeUserType.all.getValue().equals(userTypeCountVo.getUserType())) {
                //加 非vip 和 vip 平台公告通知数量
                vipNums = vipNums + userTypeCountVo.getNumber();
                notVipNums = notVipNums + userTypeCountVo.getNumber();
            }
        }

        // 更新缓存
        redisCachePiclumen.setCacheObject(LogicParamsCons.VIP_PLATFORM_ACTIVITY_NUMS,vipNums);
        redisCachePiclumen.setCacheObject(LogicParamsCons.NOT_VIP_PLATFORM_ACTIVITY_NUMS,notVipNums);
        return Boolean.TRUE;
    }
}
