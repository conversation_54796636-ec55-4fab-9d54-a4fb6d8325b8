package com.ai.operation.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.operation.domain.GptPlatformActivity;

/**
 * 平台公告通知Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-25
 */
public interface IGptPlatformActivityService extends IService<GptPlatformActivity> {
    /**
     * 查询平台公告通知
     * 
     * @param id 平台公告通知主键
     * @return 平台公告通知
     */
    GptPlatformActivity selectGptPlatformActivityById(Long id);

    /**
     * 查询平台公告通知列表
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 平台公告通知集合
     */
     List<GptPlatformActivity> selectGptPlatformActivityList(GptPlatformActivity gptPlatformActivity);

    /**
     * 新增平台公告通知
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 结果
     */
    int insertGptPlatformActivity(GptPlatformActivity gptPlatformActivity);

    /**
     * 修改平台公告通知
     * 
     * @param gptPlatformActivity 平台公告通知
     * @return 结果
     */
     int updateGptPlatformActivity(GptPlatformActivity gptPlatformActivity);

    /**
     * 批量删除平台公告通知
     * 
     * @param ids 需要删除的平台公告通知主键集合
     * @return 结果
     */
     int deleteGptPlatformActivityByIds(Long[] ids);

    /**
     * 删除平台公告通知信息
     * 
     * @param id 平台公告通知主键
     * @return 结果
     */
    int deleteGptPlatformActivityById(Long id);

    /**
     * 发布平台公告通知信息
     *
     * @param id 系统更新消息主键
     * @return 结果
     */
    Boolean toPublish(Long id);

    /**
     * 刷新已发布的数据到redis
     *
     * @return 结果
     */
     Boolean refreshPublish();
}
