package com.ai.operation.mapper;

import java.util.List;

import com.ai.operation.domain.dto.SendRewardSelectionDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.operation.domain.CommActivityRewardSelection;

/**
 * 社区活动信息奖励记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface CommActivityRewardSelectionMapper extends BaseMapper<CommActivityRewardSelection> {
    /**
     * 查询社区活动信息奖励记录
     * 
     * @param id 社区活动信息奖励记录主键
     * @return 社区活动信息奖励记录
     */
    CommActivityRewardSelection selectCommActivityRewardSelectionById(Long id);

    /**
     * 查询社区活动信息奖励记录列表
     * 
     * @param commActivityRewardSelection 社区活动信息奖励记录
     * @return 社区活动信息奖励记录集合
     */
    List<CommActivityRewardSelection> selectCommActivityRewardSelectionList(CommActivityRewardSelection commActivityRewardSelection);

    /**
     * 新增社区活动信息奖励记录
     * 
     * @param commActivityRewardSelection 社区活动信息奖励记录
     * @return 结果
     */
    int insertCommActivityRewardSelection(CommActivityRewardSelection commActivityRewardSelection);

    /**
     * 修改社区活动信息奖励记录
     * 
     * @param commActivityRewardSelection 社区活动信息奖励记录
     * @return 结果
     */
    int updateCommActivityRewardSelection(CommActivityRewardSelection commActivityRewardSelection);

    /**
     * 删除社区活动信息奖励记录
     * 
     * @param id 社区活动信息奖励记录主键
     * @return 结果
     */
    int deleteCommActivityRewardSelectionById(Long id);

    /**
     * 批量删除社区活动信息奖励记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCommActivityRewardSelectionByIds(Long[] ids);


    List<SendRewardSelectionDto> selectRewardSelectionUser(Long activityId);
}
