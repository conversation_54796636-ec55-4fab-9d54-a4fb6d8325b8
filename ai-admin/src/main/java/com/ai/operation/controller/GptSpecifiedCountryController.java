package com.ai.operation.controller;

import java.util.List;

import com.ai.common.core.domain.entity.SysUser;
import com.ai.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.GptSpecifiedCountry;
import com.ai.operation.service.IGptSpecifiedCountryService;
import com.ai.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * 特殊国家配置Controller
 * 
 * <AUTHOR>
 * @date 2024-10-24
 */
@RestController
@Api(value = "特殊国家配置控制器", tags = {"特殊国家配置管理"})
@RequestMapping("/operation/country")
public class GptSpecifiedCountryController extends BaseController {
    @Autowired
    private IGptSpecifiedCountryService gptSpecifiedCountryService;

    /**
     * 查询特殊国家配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:country:list')")
    @ApiOperation("查询特殊国家配置列表")
    @GetMapping("/list")
    public TableDataInfo list(GptSpecifiedCountry gptSpecifiedCountry) {
        startPage();
        List<GptSpecifiedCountry> list = gptSpecifiedCountryService.selectGptSpecifiedCountryList(gptSpecifiedCountry);
        return getDataTable(list);
    }

    /**
     * 导出特殊国家配置列表
     */
    @ApiOperation("导出特殊国家配置列表")
    @PreAuthorize("@ss.hasPermi('operation:country:export')")
    @Log(title = "特殊国家配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptSpecifiedCountry gptSpecifiedCountry) {
        List<GptSpecifiedCountry> list = gptSpecifiedCountryService.selectGptSpecifiedCountryList(gptSpecifiedCountry);
        ExcelUtil<GptSpecifiedCountry> util = new ExcelUtil<GptSpecifiedCountry>(GptSpecifiedCountry.class);
        util.exportExcel(response, list, "特殊国家配置数据");
    }

    /**
     * 获取特殊国家配置详细信息
     */
    @ApiOperation("获取特殊国家配置详细信息")
    @PreAuthorize("@ss.hasPermi('operation:country:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptSpecifiedCountryService.selectGptSpecifiedCountryById(id));
    }

    /**
     * 新增特殊国家配置
     */
    @ApiOperation("新增特殊国家配置")
    @PreAuthorize("@ss.hasPermi('operation:country:add')")
    @Log(title = "特殊国家配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptSpecifiedCountry gptSpecifiedCountry) {
        return toAjax(gptSpecifiedCountryService.insertGptSpecifiedCountry(gptSpecifiedCountry));
    }

    /**
     * 修改特殊国家配置
     */
    @ApiOperation("修改特殊国家配置")
    @PreAuthorize("@ss.hasPermi('operation:country:edit')")
    @Log(title = "特殊国家配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptSpecifiedCountry gptSpecifiedCountry) {
        return toAjax(gptSpecifiedCountryService.updateGptSpecifiedCountry(gptSpecifiedCountry));
    }

    /**
     * 删除特殊国家配置
     */
    @ApiOperation("删除特殊国家配置")
    @PreAuthorize("@ss.hasPermi('operation:country:remove')")
    @Log(title = "特殊国家配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptSpecifiedCountryService.deleteGptSpecifiedCountryByIds(ids));
    }

    @ApiOperation("下载模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<GptSpecifiedCountry> util = new ExcelUtil<GptSpecifiedCountry>(GptSpecifiedCountry.class);
        util.importTemplateExcel(response, "特殊国家配置数据");
    }


    @Log(title = "特殊国家配置", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('operation:country:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<GptSpecifiedCountry> util = new ExcelUtil<GptSpecifiedCountry>(GptSpecifiedCountry.class);
        List<GptSpecifiedCountry> gptSpecifiedCountryList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = gptSpecifiedCountryService.importSpecifiedCountry(gptSpecifiedCountryList, operName);
        return success(message);
    }
}
