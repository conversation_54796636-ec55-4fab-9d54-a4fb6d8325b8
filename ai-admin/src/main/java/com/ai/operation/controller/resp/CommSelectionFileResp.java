package com.ai.operation.controller.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;

@Data
@ApiModel(description = "社区投稿图片返回参数")
public class CommSelectionFileResp {


    @ApiModelProperty("图片id")
    private String fileId;

    @ApiModelProperty("图片点赞数")
    private Integer fileLikeNums;

    @ApiModelProperty("图片评论数")
    private Integer fileCommentNums;

    @ApiModelProperty("文件路径")
    private String fileUrl;

    @ApiModelProperty("缩略图路径")
    private String thumbnailUrl;

    @ApiModelProperty("高清缩略图路径")
    private String highThumbnailUrl;

    @ApiModelProperty("30% 高清图")
    private String highMiniUrl;

    @ApiModelProperty("小图路径")
    private String miniThumbnailUrl;

    @ApiModelProperty("活动奖章")
    private Integer prizeLevel;

    /** 奖牌名称 */
    @ApiModelProperty("奖牌名称")
    private String levelName;

    /** 图片链接 */
    @ApiModelProperty("图片链接")
    private String icon;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户昵称")
    private String userName;

    @ApiModelProperty("用户账号")
    private String loginName;

}
