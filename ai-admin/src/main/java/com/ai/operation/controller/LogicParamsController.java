package com.ai.operation.controller;

import java.util.ArrayList;
import java.util.List;

import com.ai.defined.utils.ShardingTableCalculator;
import com.ai.operation.domain.vo.LoginNameShardingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.operation.domain.LogicParams;
import com.ai.operation.service.ILogicParamsService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 配置信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-12
 */
@RestController
@Api(value = "配置信息控制器", tags = {"配置信息管理"})
@RequestMapping("/operation/params")
public class LogicParamsController extends BaseController {
    @Autowired
    private ILogicParamsService logicParamsService;

    /**
     * 查询配置信息列表
     */
    @PreAuthorize("@ss.hasPermi('operation:params:list')")
    @ApiOperation("查询配置信息列表")
    @GetMapping("/list")
    public TableDataInfo list(LogicParams logicParams) {
        startPage();
        List<LogicParams> list = logicParamsService.selectLogicParamsList(logicParams);
        return getDataTable(list);
    }

    /**
     * 导出配置信息列表
     */
    @ApiOperation("导出配置信息列表")
    @PreAuthorize("@ss.hasPermi('operation:params:export')")
    @Log(title = "配置信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogicParams logicParams) {
        List<LogicParams> list = logicParamsService.selectLogicParamsList(logicParams);
        ExcelUtil<LogicParams> util = new ExcelUtil<LogicParams>(LogicParams.class);
        util.exportExcel(response, list, "配置信息数据");
    }

    /**
     * 获取配置信息详细信息
     */
    @ApiOperation("获取配置信息详细信息")
    @PreAuthorize("@ss.hasPermi('operation:params:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(logicParamsService.selectLogicParamsById(id));
    }

    /**
     * 新增配置信息
     */
    @ApiOperation("新增配置信息")
    @PreAuthorize("@ss.hasPermi('operation:params:add')")
    @Log(title = "配置信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogicParams logicParams) {
        return toAjax(logicParamsService.insertLogicParams(logicParams));
    }

    /**
     * 修改配置信息
     */
    @ApiOperation("修改配置信息")
    @PreAuthorize("@ss.hasPermi('operation:params:edit')")
    @Log(title = "配置信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogicParams logicParams) {
        return toAjax(logicParamsService.updateLogicParams(logicParams));
    }

    /**
     * 删除配置信息
     */
    @ApiOperation("删除配置信息")
    @PreAuthorize("@ss.hasPermi('operation:params:remove')")
    @Log(title = "配置信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(logicParamsService.deleteLogicParamsByIds(ids));
    }

    /**
     * 查询loginName属于哪个分片表
     */
    @PreAuthorize("@ss.hasPermi('operation:params:query')")
    @ApiOperation("查询配置信息列表")
    @GetMapping("/queryLoginName/{loginName}")
    public AjaxResult queryLoginName(@PathVariable("loginName") String loginName){
        List<LoginNameShardingVo> list =new ArrayList<>();
        ShardingTableCalculator shardingTableCalculator = new ShardingTableCalculator();
        LoginNameShardingVo loginNameShardingVo = new LoginNameShardingVo();
        loginNameShardingVo.setTableName( shardingTableCalculator.getGptPromptFileTable(loginName));
        loginNameShardingVo.setMark("文件分区表");
        list.add(loginNameShardingVo);
        loginNameShardingVo = new LoginNameShardingVo();
        loginNameShardingVo.setTableName( shardingTableCalculator.getGptPromptRecordTable(loginName));
        loginNameShardingVo.setMark("任务分区表");
        list.add(loginNameShardingVo);
        return AjaxResult.success(list);
    }

    @ApiOperation("刷新到缓存")
    @GetMapping(value = "/refresh/{id}")
    public AjaxResult refreshCacheLogicParams(@PathVariable("id") Long id){
        return logicParamsService.refreshCacheLogicParams(id);
    }
}
