package com.ai.admin.domain;

import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("pay_coupon_log")
@Schema(description = "优惠券使用日志表")
public class PayCouponLog extends MyBaseEntity {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "优惠码CODE")
    private String couponCode;

    private String off;

    private String relationId;

    /**
     * vip_lumen_sale, coupon
     */
    private String type;

    private String productType;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户账号")
    private String loginName;

    @Schema(description = "使用时间(时间戳,秒)")
    private Long usedTime;

    @Schema(description = "产品名称(如standard/pro/100 Lumen)")
    private String productName;

    @Schema(description = "数量")
    private Integer qty;

    @Schema(description = "计划ID")
    private String planId;

    @Schema(description = "价格ID")
    private String priceId;

    private String amount;

    @Schema(description = "原价")
    private String srcAmount;

    @Schema(description = "优惠金额")
    private String discountAmount;
} 