package com.ai.admin.domain;

import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("pay_coupon")
@Schema(description = "通用优惠券表")
public class PayCoupon extends MyBaseEntity {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "适用产品类型(plan/one/all)")
    private String productType;

    @Schema(description = "适用等级")
    private String level;

    @Schema(description = "优惠码")
    private String code;

    @Schema(description = "折扣百分比(如20代表八折)")
    private Integer percentOff;

    @Schema(description = "生效时间(时间戳,秒)")
    private Long startTime;

    @Schema(description = "截止时间(时间戳,秒)")
    private Long redeemBy;

    @Schema(description = "最大兑换次数(-1为无限)")
    private Integer maxRedemptions;

    @Schema(description = "是否有效(0无效,1有效)")
    private Boolean valid;

    @Schema(description = "备注")
    private String mark;

    @Schema(description = "红人名称")
    private String celebrityName;
} 