package com.ai.admin.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel(description = "vip用户分页查询参数")
public class VipUserDto {

    @ApiModelProperty(value = "用户账号")
    private String loginName;

    /**
     * 用户昵称
     */
    @ApiModelProperty(value = "用户昵称")
    private String userName;

    /**
     * 会员类型： basic 非会员 standard 普通会员 pro 高级会员
     */
    @ApiModelProperty(value = "会员类型： basic 非会员 standard 普通会员 pro 高级会员")
    private String vipType;


    /**
     * 价格间隔
     */
    @ApiModelProperty(value = "价格间隔（例如：year，month）")
    private String priceInterval;


    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

}
