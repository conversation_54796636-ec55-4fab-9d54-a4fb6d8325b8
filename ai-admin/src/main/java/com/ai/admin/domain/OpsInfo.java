package com.ai.admin.domain;

import com.ai.common.core.domain.MyBaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("gpt_ops_info")
public class OpsInfo extends MyBaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 邮件地址
     */
    private String email;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 是否为运营账号：（0 ： 非运营账户  1： 运营账户）
     */
    private Boolean opsFlag;

    /**
     * 是否发送运营数据邮件：（0 ：非发送邮件账户  1：发送邮件账户）
     */
    private Boolean sendOpsInfo;
}
