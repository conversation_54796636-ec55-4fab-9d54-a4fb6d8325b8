package com.ai.admin.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Midjourney API响应类
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MidjourneyResponse {

    /**
     * 状态：SUCCESS/FAILED
     */
    @JsonProperty("status")
    private String status;

    /**
     * 消息
     */
    @JsonProperty("message")
    private String message;

    /**
     * 数据
     */
    @JsonProperty("data")
    private Object data;


    /**
     * 用户信息响应
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InfoResponse {
        @JsonProperty("email")
        private String email;

        @JsonProperty("emailVerifiedAt")
        private String emailVerifiedAt;

        @JsonProperty("balance")
        private BigDecimal balance;

        @JsonProperty("freezeBalance")
        private BigDecimal freezeBalance;

        @JsonProperty("maxQueue")
        private Integer maxQueue;

        @JsonProperty("createdAt")
        private String createdAt;
    }
}
