package com.ai.admin.domain;

import java.time.LocalDateTime;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 用户信息对象 gpt_user
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_user", description = "用户信息")
@TableName("gpt_user")
public class GptUser extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long id;

    /** 部门ID */
    private Long deptId;

    /** microsoft登录唯一标识 */
    private String microsoftId;

    /** apple登录唯一标识 */
    private String appleId;

    /** facebook登录唯一标识 */
    private String facebookId;

    /** google登录唯一标识 */
    @ApiModelProperty("google登录唯一标识")
    @Excel(name = "google登录唯一标识")
    private String googleId;

    /** 微信小程序唯一标识 */
    private String wechatOpenId;

    /** 登录账号 */
    @ApiModelProperty("登录账号")
    @Excel(name = "登录账号")
    private String loginName;

    /** 用户昵称 */
    @ApiModelProperty("用户昵称")
    @Excel(name = "用户昵称")
    private String userName;


    /** 角色id */
    private Long roleId;

    /** 用户邮箱 */
    @ApiModelProperty("用户邮箱")
    @Excel(name = "用户邮箱")
    private String email;


    /** 性别 */
    @ApiModelProperty("性别")
    @Excel(name = "性别")
    private String sex;

    /** 头像路径 */
    private String avatar;

    /** 密码密文 */
    @ApiModelProperty("密码密文")
    @Excel(name = "密码密文")
    private String password;

    /** 删除标志（0代表存在 2代表删除） */
    private Boolean delFlag;
    
    /** 重置密码验证码 */
    @ApiModelProperty("重置密码验证码")
    @Excel(name = "重置密码验证码")
    private String resetPasswordVcode;


    /** 头像图片名称 */
    @ApiModelProperty("头像图片名称")
    @Excel(name = "头像图片名称")
    private String avatarName;

    /** 头像图片缩略图名称 */
    @ApiModelProperty("头像图片缩略图名称")
    @Excel(name = "头像图片缩略图名称")
    private String thumbnailAvatarName;

    /** 头像图片路径 */
    private String avatarUrl;

    /** 头像图片缩略图路径 */
    @ApiModelProperty("头像图片缩略图路径")
    @Excel(name = "头像图片缩略图路径")
    private String thumbnailAvatarUrl;

    /** 用户相册已经上传了图片数量 */
    @ApiModelProperty("用户相册已经上传了图片数量")
    @Excel(name = "用户相册已经上传了图片数量")
    private Long albumImgNum;

    /** 用户是否进入了黑名单 */
    @ApiModelProperty("用户是否进入了黑名单")
    @Excel(name = "用户是否进入了黑名单")
    private Boolean blackListFlag;


    /** 反馈黑名单标识 */
    @ApiModelProperty("反馈黑名单标识")
    private Boolean   contactBlackListFlag;

    /**
     * 注册用户所在国家
     */
    private String registCountry;

    /**
     * 每日免费点数
     */
    private Integer dailyLumens;


    /**
     * 剩余每日免费点数
     */
    private Integer useDailyLumens;

    /**
     * 每日免费点数日期(零时区开始时间戳)
     */
    private Long dailyLumensTime;

    private Long usedSize;

    private Integer usedCollectNum;

    /**
     * 会员类型： basic 非会员 standard 普通会员 pro 高级会员
     */
    private String vipType;

    /**
     * 价格间隔 年: year，月: month
     */
    private String priceInterval;

    /**
     * 普通会员生效时间
     */
    private Long vipBeginTime;

    /**
     * 普通会员过期时间
     */
    private Long vipEndTime;


    /**
     * 总存储空间 bytes
     */
    @ApiModelProperty(value = "总存储空间 bytes")
    private Long totalSize;

    private Long totalImgNum;

    /**
     * ios设备token
     */
    private String iosFcmToken;

    /**
     * android设备token
     */
    private String androidFcmToken;

    /**
     * 用户多语言设置
     */
    private String localLang;

    /**
     * 系统奖励点数，包含任务完成奖励
     */
    private Integer systemRewardLumen;

}