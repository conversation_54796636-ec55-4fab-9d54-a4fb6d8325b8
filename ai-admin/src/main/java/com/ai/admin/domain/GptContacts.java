package com.ai.admin.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 联系我们对象 gpt_contacts
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_contacts", description = "联系我们")
@TableName("gpt_contacts")
public class GptContacts extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */

    private Long id;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** 联系名称 */
    @ApiModelProperty("联系名称")
    @Excel(name = "联系名称")
    private String fullName;

    /** 原因 */
    @ApiModelProperty("原因")
    @Excel(name = "原因")
    private Integer reason;

    /** 内容 */
    @ApiModelProperty("内容")
    @Excel(name = "内容")
    private String message;

    /** 联系邮箱 */
    @ApiModelProperty("联系邮箱")
    @Excel(name = "联系邮箱")
    private String email;

    /**
     * 压缩图路径集(逗号分隔)
     */
    @ApiModelProperty("压缩图路径集(逗号分隔)")
    private String  thumbnailUrls;


    /**
     * 用户使用终端
     */
    @ApiModelProperty("用户使用终端")
    private String  userAgent;


}
