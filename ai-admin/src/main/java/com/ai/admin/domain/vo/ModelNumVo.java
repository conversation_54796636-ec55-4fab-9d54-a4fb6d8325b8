package com.ai.admin.domain.vo;


import com.ai.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel(description = "每日模型数量")
public class ModelNumVo {

   @ApiModelProperty("模型id")
   private String modelId;

   @ApiModelProperty("用户账号")
   @Excel(name = "图片数量")
   private Integer fileNum;

   @ApiModelProperty("任务数量")
   @Excel(name = "用户账号")
   private Integer recordNum;

   @ApiModelProperty("用户账号")
   @Excel(name = "用户使用数量")
   private Integer userNum;

   @JsonFormat(pattern = "yyyy-MM-dd")
   @Excel(name = "日期")
   private LocalDate date;

   @Excel(name = "模型")
   private String modelName;
}
