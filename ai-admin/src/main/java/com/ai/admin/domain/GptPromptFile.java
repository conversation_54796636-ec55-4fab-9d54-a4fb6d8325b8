package com.ai.admin.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 任务图片对应关系对象 gpt_prompt_file
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_prompt_file", description = "任务图片对应关系")
@TableName("gpt_prompt_file")
public class GptPromptFile extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** $column.columnComment */
    @ApiModelProperty("${comment}")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String promptId;

    /** 文件名称 */
    @ApiModelProperty("文件名称")
    @Excel(name = "文件名称")
    private String fileName;



    /** 缩略图名称 */
    @ApiModelProperty("缩略图名称")
    @Excel(name = "缩略图名称")
    private String thumbnailName;

    /** 图片路径 */
    private String fileUrl;

    /** 缩略图路径 */
    @ApiModelProperty("缩略图路径")
    @Excel(name = "缩略图路径")
    private String thumbnailUrl;

    /** 高清缩略图名称 */
    @ApiModelProperty("高清缩略图名称")
    @Excel(name = "高清缩略图名称")
    private String highThumbnailName;

    /** 高清缩略图路径 */
    @ApiModelProperty("高清缩略图路径")
    @Excel(name = "高清缩略图路径")
    private String highThumbnailUrl;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /** 涉黄：NSFW */
    @ApiModelProperty("涉黄：NSFW")
    @Excel(name = "涉黄：NSFW")
    private String sensitiveMessage;

    /** 图片结果宽度 */
    @ApiModelProperty("图片结果宽度")
    @Excel(name = "图片结果宽度")
    private Long width;

    /** 图片结果高度 */
    @ApiModelProperty("图片结果高度")
    @Excel(name = "图片结果高度")
    private Long height;

    /** 图片点赞数 */
    @ApiModelProperty("图片点赞数")
    @Excel(name = "图片点赞数")
    private Long likeNums;

    @ApiModelProperty("图片收藏数")
    @Excel(name = "图片收藏数")
    private Long collectNums;

    @ApiModelProperty("0:未公开 1:已公开 2:审核中 3:已拒绝")
    private Integer isPublic;


    /**
     *  拒绝内容描述
     */
    @Schema(description = "拒绝内容描述")
    private String rejectionContent;


    /** 0:未删除 1：已删除 */
    private Boolean del;

    @TableField(exist = false)
    private Integer isNext;

}
