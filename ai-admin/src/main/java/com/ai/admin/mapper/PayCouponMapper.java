package com.ai.admin.mapper;

import java.util.List;
import com.ai.admin.domain.PayCoupon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 通用优惠券Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface PayCouponMapper extends BaseMapper<PayCoupon> {
    /**
     * 查询通用优惠券
     * 
     * @param id 通用优惠券主键
     * @return 通用优惠券
     */
    PayCoupon selectPayCouponById(Long id);

    /**
     * 查询通用优惠券列表
     *
     * @param payCoupon 通用优惠券
     * @return 通用优惠券集合
     */
    List<PayCoupon> selectPayCouponList(PayCoupon payCoupon);

    /**
     * 新增通用优惠券
     * 
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    int insertPayCoupon(PayCoupon payCoupon);

    /**
     * 修改通用优惠券
     * 
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    int updatePayCoupon(PayCoupon payCoupon);

    /**
     * 删除通用优惠券
     * 
     * @param id 通用优惠券主键
     * @return 结果
     */
    int deletePayCouponById(Long id);

    /**
     * 批量删除通用优惠券
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePayCouponByIds(Long[] ids);

    /**
     * 根据优惠码查询优惠券数量
     *
     * @param code 优惠码
     * @return 数量
     */
    int countByCode(String code);
}
