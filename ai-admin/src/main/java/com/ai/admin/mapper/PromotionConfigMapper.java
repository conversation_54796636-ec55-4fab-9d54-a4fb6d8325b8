package com.ai.admin.mapper;

import java.util.List;
import com.ai.admin.domain.PromotionConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 优惠配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface PromotionConfigMapper extends BaseMapper<PromotionConfig> {
    /**
     * 查询优惠配置
     * 
     * @param id 优惠配置主键
     * @return 优惠配置
     */
    PromotionConfig selectPromotionConfigById(Long id);

    /**
     * 查询优惠配置列表
     * 
     * @param promotionConfig 优惠配置
     * @return 优惠配置集合
     */
    List<PromotionConfig> selectPromotionConfigList(PromotionConfig promotionConfig);

    /**
     * 新增优惠配置
     * 
     * @param promotionConfig 优惠配置
     * @return 结果
     */
    int insertPromotionConfig(PromotionConfig promotionConfig);

    /**
     * 修改优惠配置
     * 
     * @param promotionConfig 优惠配置
     * @return 结果
     */
    int updatePromotionConfig(PromotionConfig promotionConfig);

    /**
     * 删除优惠配置
     * 
     * @param id 优惠配置主键
     * @return 结果
     */
    int deletePromotionConfigById(Long id);

    /**
     * 批量删除优惠配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePromotionConfigByIds(Long[] ids);
}
