package com.ai.admin.mapper;

import java.util.List;

import com.ai.admin.domain.vo.PromptFileRanking;
import com.ai.common.annotation.DataScope;
import com.ai.operation.domain.dto.GptPromptFileBatchRejectDto;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.admin.domain.GptPromptFile;
import com.ai.admin.domain.vo.PromptFileRanking;
import com.ai.purge.vo.PurgeQueryParamVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.Collection;
import java.util.List;

/**
 * 任务图片对应关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-29
 */

public interface GptPromptFileMapper extends BaseMapper<GptPromptFile> {
    /**
     * 查询任务图片对应关系
     *
     * @param id 任务图片对应关系主键
     * @return 任务图片对应关系
     */
    public GptPromptFile selectGptPromptFileById(Long id,String loginName);

    /**
     * 查询任务图片对应关系列表
     *
     * @param gptPromptFile 任务图片对应关系
     * @return 任务图片对应关系集合
     */
    public List<GptPromptFile> selectGptPromptFileList(GptPromptFile gptPromptFile);

    /**
     * 新增任务图片对应关系
     *
     * @param gptPromptFile 任务图片对应关系
     * @return 结果
     */
    public int insertGptPromptFile(GptPromptFile gptPromptFile);

    /**
     * 修改任务图片对应关系
     *
     * @param gptPromptFile 任务图片对应关系
     * @return 结果
     */
    public int updateGptPromptFile(GptPromptFile gptPromptFile);

    /**
     * 删除任务图片对应关系
     *
     * @param id 任务图片对应关系主键
     * @return 结果
     */
    public int deleteGptPromptFileById(Long id);

    /**
     * 批量删除任务图片对应关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptPromptFileByIds(Long[] ids);


    List<PromptFileRanking> getPromptFileRanking(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<GptPromptFile> queryFileListByCondition(@Param("param") PurgeQueryParamVo paramVo);

    List<GptPromptFile> queryFileByDbNameWithPromptIds(@Param("dbName") String dbName, @Param("promptIds") Collection<String> promptIds);

    int deleteGptPromptFileByIdsWithDbName(@Param("dbName") String dbName, @Param("ids") List<Long> ids);

    public Double getTaskAvgTime(@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("opexLoginNameList") List<String> opexLoginNameList,@Param("fastHour") Integer fastHour);

    public Long getCreatePictureNums(@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("opexLoginNameList") List<String> opexLoginNameList);

    Long getUserAvgCratePictureNums(@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("opexLoginNameList") List<String> opexLoginNameList);

    Long getUserMaxCratePictureNums(@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("opexLoginNameList") List<String> opexLoginNameList);

    void insertTempIds(@Param("temDbName") String temDbName, @Param("dbName") String dbName, @Param("promptIds")  List<String> promptIds);

    @Delete("DELETE t FROM ${fileDbName} t JOIN ${temDbName} temp ON t.id = temp.id")
    @InterceptorIgnore(blockAttack = "true")
    void deleteFileByJoinTemp(@Param("temDbName") String temDbName, @Param("fileDbName") String fileDbName);
    @Update("TRUNCATE TABLE ${temDbName}")
    @InterceptorIgnore(blockAttack = "true")
    void truncateTemp(@Param("temDbName") String temDbName);
    @Update("CREATE TEMPORARY TABLE ${temDbName} ( id BIGINT PRIMARY KEY )")
    void createTmpDb(@Param("temDbName")  String temDbName);

    Double getAvgOnePicTime(@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("fastHour") Integer fastHour);

    public List<GptPromptFile> queryDeleteFileDataBatch(@Param("fileDbNum") String fileDbNum,
                                                    @Param("startTime") String startTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("limit") int limit);
    int physicRemoveDeleteDataBatch(@Param("fileDbNum") String fileDbNum, @Param("ids") List<Long> ids);

    List<GptPromptFile> queryDeleteFileDataBatchWithVip(@Param("fileDbNum") String fileDbNum,
                                                        @Param("startTime") String startTime,
                                                        @Param("vipExpireDays") Integer vipExpireDays,
                                                        @Param("limit") int limit);

    void batchRejectMultiTable(@Param("batchList") List<GptPromptFileBatchRejectDto> batchList);

}
