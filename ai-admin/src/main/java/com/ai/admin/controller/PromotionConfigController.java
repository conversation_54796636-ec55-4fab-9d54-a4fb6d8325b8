package com.ai.admin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.admin.domain.PromotionConfig;
import com.ai.admin.service.IPromotionConfigService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 优惠配置Controller
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@Api(value = "优惠配置控制器", tags = {"优惠配置管理"})
@RequestMapping("/admin/promotion")
public class PromotionConfigController extends BaseController {
    @Autowired
    private IPromotionConfigService promotionConfigService;

    /**
     * 查询优惠配置列表
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:list')")
    @ApiOperation("查询优惠配置列表")
    @GetMapping("/list")
    public TableDataInfo list(PromotionConfig promotionConfig) {
        startPage();
        List<PromotionConfig> list = promotionConfigService.selectPromotionConfigList(promotionConfig);
        return getDataTable(list);
    }

    /**
     * 导出优惠配置列表
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:export')")
    @ApiOperation("导出优惠配置列表")
    @Log(title = "优惠配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromotionConfig promotionConfig) {
        List<PromotionConfig> list = promotionConfigService.selectPromotionConfigList(promotionConfig);
        ExcelUtil<PromotionConfig> util = new ExcelUtil<PromotionConfig>(PromotionConfig.class);
        util.exportExcel(response, list, "优惠配置数据");
    }

    /**
     * 获取优惠配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:query')")
    @ApiOperation("获取优惠配置详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(promotionConfigService.selectPromotionConfigById(id));
    }

    /**
     * 新增优惠配置
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:add')")
    @ApiOperation("新增优惠配置")
    @Log(title = "优惠配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromotionConfig promotionConfig) {
        return toAjax(promotionConfigService.insertPromotionConfig(promotionConfig));
    }

    /**
     * 修改优惠配置
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:edit')")
    @ApiOperation("修改优惠配置")
    @Log(title = "优惠配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromotionConfig promotionConfig) {
        return toAjax(promotionConfigService.updatePromotionConfig(promotionConfig));
    }

    /**
     * 删除优惠配置
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:remove')")
    @ApiOperation("删除优惠配置")
    @Log(title = "优惠配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(promotionConfigService.deletePromotionConfigByIds(ids));
    }

    /**
     * 启用/禁用优惠配置
     */
    @PreAuthorize("@ss.hasPermi('admin:promotion:edit')")
    @ApiOperation("启用/禁用优惠配置")
    @Log(title = "优惠配置状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{enable}")
    public AjaxResult updateStatus(@PathVariable("id") Long id, @PathVariable("enable") Boolean enable) {
        return toAjax(promotionConfigService.updatePromotionConfigStatus(id, enable));
    }
}
