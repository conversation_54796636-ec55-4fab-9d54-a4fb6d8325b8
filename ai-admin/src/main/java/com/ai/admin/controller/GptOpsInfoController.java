package com.ai.admin.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.admin.domain.GptOpsInfo;
import com.ai.admin.service.IGptOpsInfoService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 运营信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-08
 */
@RestController
@Api(value = "运营信息控制器", tags = {"运营信息管理"})
@RequestMapping("/admin/info")
public class GptOpsInfoController extends BaseController {
    @Autowired
    private IGptOpsInfoService gptOpsInfoService;

    /**
     * 查询运营信息列表
     */
    @PreAuthorize("@ss.hasPermi('admin:info:list')")
    @ApiOperation("查询运营信息列表")
    @GetMapping("/list")
    public TableDataInfo list(GptOpsInfo gptOpsInfo) {
        startPage();
        List<GptOpsInfo> list = gptOpsInfoService.selectGptOpsInfoList(gptOpsInfo);
        return getDataTable(list);
    }

    /**
     * 导出运营信息列表
     */
    @ApiOperation("导出运营信息列表")
    @PreAuthorize("@ss.hasPermi('admin:info:export')")
    @Log(title = "运营信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptOpsInfo gptOpsInfo) {
        List<GptOpsInfo> list = gptOpsInfoService.selectGptOpsInfoList(gptOpsInfo);
        ExcelUtil<GptOpsInfo> util = new ExcelUtil<GptOpsInfo>(GptOpsInfo.class);
        util.exportExcel(response, list, "运营信息数据");
    }

    /**
     * 获取运营信息详细信息
     */
    @ApiOperation("获取运营信息详细信息")
    @PreAuthorize("@ss.hasPermi('admin:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptOpsInfoService.selectGptOpsInfoById(id));
    }

    /**
     * 新增运营信息
     */
    @ApiOperation("新增运营信息")
    @PreAuthorize("@ss.hasPermi('admin:info:add')")
    @Log(title = "运营信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptOpsInfo gptOpsInfo) {
        return toAjax(gptOpsInfoService.insertGptOpsInfo(gptOpsInfo));
    }

    /**
     * 修改运营信息
     */
    @ApiOperation("修改运营信息")
    @PreAuthorize("@ss.hasPermi('admin:info:edit')")
    @Log(title = "运营信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptOpsInfo gptOpsInfo) {
        return toAjax(gptOpsInfoService.updateGptOpsInfo(gptOpsInfo));
    }

    /**
     * 删除运营信息
     */
    @ApiOperation("删除运营信息")
    @PreAuthorize("@ss.hasPermi('admin:info:remove')")
    @Log(title = "运营信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptOpsInfoService.deleteGptOpsInfoByIds(ids));
    }
}
