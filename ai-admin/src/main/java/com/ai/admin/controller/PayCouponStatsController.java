package com.ai.admin.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.admin.domain.dto.PayCouponStatsDTO;
import com.ai.admin.domain.dto.PayCouponPurchaseDetailDTO;
import com.ai.admin.service.IPayCouponLogService;
import com.ai.common.core.page.TableDataInfo;

/**
 * 优惠券统计Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@Api(value = "优惠券统计控制器", tags = {"优惠券统计管理"})
@RequestMapping("/admin/coupon/stats")
public class PayCouponStatsController extends BaseController {
    @Autowired
    private IPayCouponLogService payCouponLogService;

    /**
     * 查询所有优惠码统计数据
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:stats')")
    @ApiOperation("查询所有优惠码统计数据")
    @GetMapping("/list")
    public TableDataInfo list() {
        List<PayCouponStatsDTO> list = payCouponLogService.getAllCouponStats();
        return getDataTable(list);
    }

    /**
     * 根据优惠码查询统计数据
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:stats')")
    @ApiOperation("根据优惠码查询统计数据")
    @GetMapping("/detail/{couponCode}")
    public AjaxResult getStatsByCouponCode(@PathVariable("couponCode") String couponCode) {
        PayCouponStatsDTO stats = payCouponLogService.getStatsByCouponCode(couponCode);
        return success(stats);
    }

    /**
     * 根据优惠码查询购买详情列表
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:stats')")
    @ApiOperation("根据优惠码查询购买详情列表")
    @GetMapping("/purchases/{couponCode}")
    public TableDataInfo getPurchaseDetails(@PathVariable("couponCode") String couponCode) {
        startPage();
        List<PayCouponPurchaseDetailDTO> list = payCouponLogService.getPurchaseDetailsByCouponCode(couponCode);
        return getDataTable(list);
    }
}
