package com.ai.admin.controller;

import com.ai.admin.domain.vo.BannerImgVO;
import com.ai.admin.service.IBannerImgService;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.common.core.page.TableDataInfo;
import com.ai.common.enums.BusinessType;
import com.ai.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 社区首页banner图配置Controller
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@Api(value = "社区首页banner图配置控制器", tags = {"社区首页banner图配置管理"})
@RequestMapping("/admin/img")
public class BannerImgController extends BaseController {
    @Autowired
    private IBannerImgService bannerImgService;

    /**
     * 查询社区首页banner图配置列表
     */
    @PreAuthorize("@ss.hasPermi('admin:img:list')")
    @ApiOperation("查询社区首页banner图配置列表")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(value = "imgUrl", required = false) String imgUrl,
                              @RequestParam(value = "sort", required = false) Integer sort,
                              @RequestParam(value = "status", required = false) Integer status) {
        startPage();
        List<BannerImgVO> list = bannerImgService.selectBannerImgList(imgUrl, sort, status);
        return getDataTable(list);
    }

    /**
     * 导出社区首页banner图配置列表
     */
    @ApiOperation("导出社区首页banner图配置列表")
    @PreAuthorize("@ss.hasPermi('admin:img:export')")
    @Log(title = "社区首页banner图配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(value = "imgUrl", required = false) String imgUrl,
                       @RequestParam(value = "sort", required = false) Integer sort,
                       @RequestParam(value = "status", required = false) Integer status) {
        List<BannerImgVO> list = bannerImgService.selectBannerImgList(imgUrl, sort, status);
        ExcelUtil<BannerImgVO> util = new ExcelUtil<>(BannerImgVO.class);
        util.exportExcel(response, list, "社区首页banner图配置数据");
    }

    /**
     * 获取社区首页banner图配置详细信息
     */
    @ApiOperation("获取社区首页banner图配置详细信息")
    @PreAuthorize("@ss.hasPermi('admin:img:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(bannerImgService.selectBannerImgById(id));
    }

    /**
     * 新增社区首页banner图配置
     */
    @ApiOperation("新增社区首页banner图配置")
    @PreAuthorize("@ss.hasPermi('admin:img:add')")
    @Log(title = "社区首页banner图配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BannerImgVO bannerImgVO) {
        LoginUser loginUser = getLoginUser();
        return toAjax(bannerImgService.insertBannerImg(bannerImgVO, loginUser));
    }

    /**
     * 修改社区首页banner图配置
     */
    @ApiOperation("修改社区首页banner图配置")
    @PreAuthorize("@ss.hasPermi('admin:img:edit')")
    @Log(title = "社区首页banner图配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BannerImgVO bannerImgVO) {
        LoginUser loginUser = getLoginUser();
        return toAjax(bannerImgService.updateBannerImg(bannerImgVO, loginUser));
    }

    /**
     * 删除社区首页banner图配置
     */
    @ApiOperation("删除社区首页banner图配置")
    @PreAuthorize("@ss.hasPermi('admin:img:remove')")
    @Log(title = "社区首页banner图配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        LoginUser loginUser = getLoginUser();
        return toAjax(bannerImgService.deleteBannerImgById(id, loginUser));
    }
}
