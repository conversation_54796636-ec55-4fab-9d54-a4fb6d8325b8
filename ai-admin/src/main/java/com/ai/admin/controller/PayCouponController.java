package com.ai.admin.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.admin.domain.PayCoupon;
import com.ai.admin.service.IPayCouponService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 通用优惠券Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@Api(value = "通用优惠券控制器", tags = {"通用优惠券管理"})
@RequestMapping("/admin/coupon")
public class PayCouponController extends BaseController {
    @Autowired
    private IPayCouponService payCouponService;

    /**
     * 查询通用优惠券列表
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:list')")
    @ApiOperation("查询通用优惠券列表")
    @GetMapping("/list")
    public TableDataInfo list(PayCoupon payCoupon) {
        startPage();
        List<PayCoupon> list = payCouponService.selectPayCouponList(payCoupon);
        return getDataTable(list);
    }

    /**
     * 导出通用优惠券列表
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:export')")
    @ApiOperation("导出通用优惠券列表")
    @Log(title = "通用优惠券", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayCoupon payCoupon) {
        List<PayCoupon> list = payCouponService.selectPayCouponList(payCoupon);
        ExcelUtil<PayCoupon> util = new ExcelUtil<PayCoupon>(PayCoupon.class);
        util.exportExcel(response, list, "通用优惠券数据");
    }

    /**
     * 获取通用优惠券详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:query')")
    @ApiOperation("获取通用优惠券详细信息")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payCouponService.selectPayCouponById(id));
    }

    /**
     * 新增通用优惠券
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:add')")
    @ApiOperation("新增通用优惠券")
    @Log(title = "通用优惠券", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayCoupon payCoupon) {
        return toAjax(payCouponService.insertPayCoupon(payCoupon));
    }

    /**
     * 修改通用优惠券
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:edit')")
    @ApiOperation("修改通用优惠券")
    @Log(title = "通用优惠券", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayCoupon payCoupon) {
        // 修改时不允许更改优惠码，确保优惠码不被覆盖
        PayCoupon existingCoupon = payCouponService.selectPayCouponById(payCoupon.getId());
        if (existingCoupon != null) {
            payCoupon.setCode(existingCoupon.getCode());
        }
        return toAjax(payCouponService.updatePayCoupon(payCoupon));
    }

    /**
     * 删除通用优惠券
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:remove')")
    @ApiOperation("删除通用优惠券")
    @Log(title = "通用优惠券", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payCouponService.deletePayCouponByIds(ids));
    }

    /**
     * 启用/禁用通用优惠券
     */
    @PreAuthorize("@ss.hasPermi('admin:coupon:edit')")
    @ApiOperation("启用/禁用通用优惠券")
    @Log(title = "通用优惠券状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status/{id}/{valid}")
    public AjaxResult updateStatus(@PathVariable("id") Long id, @PathVariable("valid") Boolean valid) {
        return toAjax(payCouponService.updatePayCouponStatus(id, valid));
    }
}
