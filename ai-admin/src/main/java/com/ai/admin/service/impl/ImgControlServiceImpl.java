package com.ai.admin.service.impl;

import cn.hutool.core.util.URLUtil;
import com.ai.admin.domain.GptUserAlbum;
import com.ai.admin.domain.ImgControl;
import com.ai.admin.mapper.GptUserAlbumMapper;
import com.ai.admin.mapper.ImgControlMapper;
import com.ai.admin.service.IGptUserAlbumService;
import com.ai.cos.CosCommonService;
import com.ai.purge.CustomThreadFactory;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.util.concurrent.RateLimiter;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.COSObjectInputStream;
import com.qcloud.cos.model.GetObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 用户相册Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-06
 */
@Service
@Slf4j
public class ImgControlServiceImpl extends ServiceImpl<ImgControlMapper, ImgControl>  {

    @Autowired
    private ImgControlMapper imgControlMapper;



}
