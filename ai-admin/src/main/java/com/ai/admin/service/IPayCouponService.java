package com.ai.admin.service;

import java.util.List;
import com.ai.admin.domain.PayCoupon;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 通用优惠券Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IPayCouponService extends IService<PayCoupon> {
    /**
     * 查询通用优惠券
     * 
     * @param id 通用优惠券主键
     * @return 通用优惠券
     */
    PayCoupon selectPayCouponById(Long id);

    /**
     * 查询通用优惠券列表
     * 
     * @param payCoupon 通用优惠券
     * @return 通用优惠券集合
     */
    List<PayCoupon> selectPayCouponList(PayCoupon payCoupon);

    /**
     * 新增通用优惠券
     * 
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    int insertPayCoupon(PayCoupon payCoupon);

    /**
     * 修改通用优惠券
     * 
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    int updatePayCoupon(PayCoupon payCoupon);

    /**
     * 批量删除通用优惠券
     * 
     * @param ids 需要删除的通用优惠券主键集合
     * @return 结果
     */
    int deletePayCouponByIds(Long[] ids);

    /**
     * 删除通用优惠券信息
     * 
     * @param id 通用优惠券主键
     * @return 结果
     */
    int deletePayCouponById(Long id);

    /**
     * 启用/禁用通用优惠券
     *
     * @param id 通用优惠券主键
     * @param valid 有效状态
     * @return 结果
     */
    int updatePayCouponStatus(Long id, Boolean valid);

    /**
     * 生成唯一的优惠码
     *
     * @return 8位大写字母+数字组成的优惠码
     */
    String generateUniqueCode();

    /**
     * 检查优惠码是否已存在
     *
     * @param code 优惠码
     * @return 是否存在
     */
    boolean isCodeExists(String code);
}
