package com.ai.admin.service;

import java.util.List;

import com.ai.admin.domain.dto.GptPromptRecordPageDto;
import com.ai.admin.domain.dto.UserProfileDto;
import com.ai.admin.domain.vo.UserProfileVo;
import com.ai.purge.vo.PurgeQueryParamVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.admin.domain.GptPromptRecord;

/**
 * 用户生图任务记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-06
 */
public interface IGptPromptRecordService extends IService<GptPromptRecord> {
    /**
     * 查询用户生图任务记录
     * 
     * @param id 用户生图任务记录主键
     * @return 用户生图任务记录
     */
    public GptPromptRecord selectGptPromptRecordById(Long id,String loginName);

    /**
     * 查询用户生图任务记录列表
     * 
     * @param gptPromptRecord 用户生图任务记录
     * @return 用户生图任务记录集合
     */
    public List<GptPromptRecord> selectGptPromptRecordList(GptPromptRecordPageDto gptPromptRecord);

    /**
     * 新增用户生图任务记录
     * 
     * @param gptPromptRecord 用户生图任务记录
     * @return 结果
     */
    public int insertGptPromptRecord(GptPromptRecord gptPromptRecord);

    /**
     * 修改用户生图任务记录
     * 
     * @param gptPromptRecord 用户生图任务记录
     * @return 结果
     */
    public int updateGptPromptRecord(GptPromptRecord gptPromptRecord);

    /**
     * 批量删除用户生图任务记录
     * 
     * @param ids 需要删除的用户生图任务记录主键集合
     * @return 结果
     */
    public int deleteGptPromptRecordByIds(Long[] ids,String[] loginNames);

    /**
     * 删除用户生图任务记录信息
     * 
     * @param id 用户生图任务记录主键
     * @return 结果
     */
    public int deleteGptPromptRecordById(Long id);

    /**
     * 查询用户生图报告
     *
     * @return 结果
     */
    List<UserProfileVo> getUserProfile(UserProfileDto userProfileDto);

    /**
     * 按条件
     * @param paramVo
     */
    List<GptPromptRecord> queryRecordListByCondition(PurgeQueryParamVo paramVo);

    int deleteGptPromptRecordByIdsWithDbName(String dbName, List<Long> ids);

    int physicRemoveDeleteDataBatch(String recordDbNum,  String endTime, int limit);

    Boolean genInfoExport(String loginName, String email);
}
