package com.ai.admin.service;

import java.util.List;
import com.ai.admin.domain.PromotionConfig;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 优惠配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IPromotionConfigService extends IService<PromotionConfig> {
    /**
     * 查询优惠配置
     * 
     * @param id 优惠配置主键
     * @return 优惠配置
     */
    PromotionConfig selectPromotionConfigById(Long id);

    /**
     * 查询优惠配置列表
     * 
     * @param promotionConfig 优惠配置
     * @return 优惠配置集合
     */
    List<PromotionConfig> selectPromotionConfigList(PromotionConfig promotionConfig);

    /**
     * 新增优惠配置
     * 
     * @param promotionConfig 优惠配置
     * @return 结果
     */
    int insertPromotionConfig(PromotionConfig promotionConfig);

    /**
     * 修改优惠配置
     * 
     * @param promotionConfig 优惠配置
     * @return 结果
     */
    int updatePromotionConfig(PromotionConfig promotionConfig);

    /**
     * 批量删除优惠配置
     * 
     * @param ids 需要删除的优惠配置主键集合
     * @return 结果
     */
    int deletePromotionConfigByIds(Long[] ids);

    /**
     * 删除优惠配置信息
     * 
     * @param id 优惠配置主键
     * @return 结果
     */
    int deletePromotionConfigById(Long id);

    /**
     * 启用/禁用优惠配置
     * 
     * @param id 优惠配置主键
     * @param enable 启用状态
     * @return 结果
     */
    int updatePromotionConfigStatus(Long id, Boolean enable);
}
