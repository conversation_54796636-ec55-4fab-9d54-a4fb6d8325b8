package com.ai.admin.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ai.admin.mapper.PayCouponLogMapper;
import com.ai.admin.domain.PayCouponLog;
import com.ai.admin.domain.dto.PayCouponStatsDTO;
import com.ai.admin.domain.dto.PayCouponPurchaseDetailDTO;
import com.ai.admin.service.IPayCouponLogService;

/**
 * 优惠券使用日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class PayCouponLogServiceImpl extends ServiceImpl<PayCouponLogMapper, PayCouponLog> implements IPayCouponLogService {
    @Autowired
    private PayCouponLogMapper payCouponLogMapper;

    /**
     * 查询优惠券使用日志列表
     * 
     * @param payCouponLog 优惠券使用日志
     * @return 优惠券使用日志
     */
    @Override
    public List<PayCouponLog> selectPayCouponLogList(PayCouponLog payCouponLog) {
        return payCouponLogMapper.selectPayCouponLogList(payCouponLog);
    }

    /**
     * 根据优惠码查询统计数据
     * 
     * @param couponCode 优惠码
     * @return 统计数据
     */
    @Override
    public PayCouponStatsDTO getStatsByCouponCode(String couponCode) {
        return payCouponLogMapper.selectStatsByCouponCode(couponCode);
    }

    /**
     * 根据优惠码查询购买详情列表
     * 
     * @param couponCode 优惠码
     * @return 购买详情列表
     */
    @Override
    public List<PayCouponPurchaseDetailDTO> getPurchaseDetailsByCouponCode(String couponCode) {
        return payCouponLogMapper.selectPurchaseDetailsByCouponCode(couponCode);
    }

    /**
     * 查询所有优惠码的统计数据
     *
     * @return 统计数据列表
     */
    @Override
    public List<PayCouponStatsDTO> getAllCouponStats() {
        return payCouponLogMapper.selectAllCouponStats();
    }

    /**
     * 根据类型查询优惠码统计数据
     *
     * @param type 类型
     * @return 统计数据列表
     */
    @Override
    public List<PayCouponStatsDTO> getCouponStatsByType(String type) {
        return payCouponLogMapper.selectCouponStatsByType(type);
    }

    /**
     * 查询所有类型列表
     *
     * @return 类型列表
     */
    @Override
    public List<String> getAllTypes() {
        return payCouponLogMapper.selectAllTypes();
    }
}
