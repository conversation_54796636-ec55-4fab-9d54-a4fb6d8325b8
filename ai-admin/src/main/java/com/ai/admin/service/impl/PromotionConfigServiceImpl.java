package com.ai.admin.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import com.ai.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ai.admin.mapper.PromotionConfigMapper;
import com.ai.admin.domain.PromotionConfig;
import com.ai.admin.service.IPromotionConfigService;

/**
 * 优惠配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
public class PromotionConfigServiceImpl extends ServiceImpl<PromotionConfigMapper, PromotionConfig> implements IPromotionConfigService {
    @Autowired
    private PromotionConfigMapper promotionConfigMapper;

    /**
     * 查询优惠配置
     * 
     * @param id 优惠配置主键
     * @return 优惠配置
     */
    @Override
    public PromotionConfig selectPromotionConfigById(Long id) {
        return promotionConfigMapper.selectPromotionConfigById(id);
    }

    /**
     * 查询优惠配置列表
     * 
     * @param promotionConfig 优惠配置
     * @return 优惠配置
     */
    @Override
    public List<PromotionConfig> selectPromotionConfigList(PromotionConfig promotionConfig) {
        return promotionConfigMapper.selectPromotionConfigList(promotionConfig);
    }

    /**
     * 新增优惠配置
     * 
     * @param promotionConfig 优惠配置
     * @return 结果
     */
    @Override
    public int insertPromotionConfig(PromotionConfig promotionConfig) {
        promotionConfig.setCreateTime(LocalDateTime.now());
        promotionConfig.setCreateBy(SecurityUtils.getUsername());
        return promotionConfigMapper.insertPromotionConfig(promotionConfig);
    }

    /**
     * 修改优惠配置
     * 
     * @param promotionConfig 优惠配置
     * @return 结果
     */
    @Override
    public int updatePromotionConfig(PromotionConfig promotionConfig) {
        promotionConfig.setUpdateTime(LocalDateTime.now());
        promotionConfig.setUpdateBy(SecurityUtils.getUsername());
        return promotionConfigMapper.updatePromotionConfig(promotionConfig);
    }

    /**
     * 批量删除优惠配置
     * 
     * @param ids 需要删除的优惠配置主键
     * @return 结果
     */
    @Override
    public int deletePromotionConfigByIds(Long[] ids) {
        return promotionConfigMapper.deletePromotionConfigByIds(ids);
    }

    /**
     * 删除优惠配置信息
     * 
     * @param id 优惠配置主键
     * @return 结果
     */
    @Override
    public int deletePromotionConfigById(Long id) {
        return promotionConfigMapper.deletePromotionConfigById(id);
    }

    /**
     * 启用/禁用优惠配置
     * 
     * @param id 优惠配置主键
     * @param enable 启用状态
     * @return 结果
     */
    @Override
    public int updatePromotionConfigStatus(Long id, Boolean enable) {
        PromotionConfig promotionConfig = new PromotionConfig();
        promotionConfig.setId(id);
        promotionConfig.setEnable(enable);
        promotionConfig.setUpdateTime(LocalDateTime.now());
        promotionConfig.setUpdateBy(SecurityUtils.getUsername());
        return promotionConfigMapper.updatePromotionConfig(promotionConfig);
    }
}
