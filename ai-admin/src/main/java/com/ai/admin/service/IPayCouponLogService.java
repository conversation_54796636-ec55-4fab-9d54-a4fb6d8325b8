package com.ai.admin.service;

import java.util.List;
import com.ai.admin.domain.PayCouponLog;
import com.ai.admin.domain.dto.PayCouponStatsDTO;
import com.ai.admin.domain.dto.PayCouponPurchaseDetailDTO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 优惠券使用日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IPayCouponLogService extends IService<PayCouponLog> {
    
    /**
     * 查询优惠券使用日志列表
     * 
     * @param payCouponLog 优惠券使用日志
     * @return 优惠券使用日志集合
     */
    List<PayCouponLog> selectPayCouponLogList(PayCouponLog payCouponLog);

    /**
     * 根据优惠码查询统计数据
     * 
     * @param couponCode 优惠码
     * @return 统计数据
     */
    PayCouponStatsDTO getStatsByCouponCode(String couponCode);

    /**
     * 根据优惠码查询购买详情列表
     * 
     * @param couponCode 优惠码
     * @return 购买详情列表
     */
    List<PayCouponPurchaseDetailDTO> getPurchaseDetailsByCouponCode(String couponCode);

    /**
     * 查询所有优惠码的统计数据
     *
     * @return 统计数据列表
     */
    List<PayCouponStatsDTO> getAllCouponStats();

    /**
     * 根据类型查询优惠码统计数据
     *
     * @param type 类型
     * @return 统计数据列表
     */
    List<PayCouponStatsDTO> getCouponStatsByType(String type);

    /**
     * 查询所有类型列表
     *
     * @return 类型列表
     */
    List<String> getAllTypes();
}
