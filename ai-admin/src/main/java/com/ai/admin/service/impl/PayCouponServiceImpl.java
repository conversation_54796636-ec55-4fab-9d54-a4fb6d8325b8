package com.ai.admin.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import com.ai.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ai.admin.mapper.PayCouponMapper;
import com.ai.admin.domain.PayCoupon;
import com.ai.admin.service.IPayCouponService;

/**
 * 通用优惠券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class PayCouponServiceImpl extends ServiceImpl<PayCouponMapper, PayCoupon> implements IPayCouponService {
    @Autowired
    private PayCouponMapper payCouponMapper;

    /**
     * 查询通用优惠券
     * 
     * @param id 通用优惠券主键
     * @return 通用优惠券
     */
    @Override
    public PayCoupon selectPayCouponById(Long id) {
        return payCouponMapper.selectPayCouponById(id);
    }

    /**
     * 查询通用优惠券列表
     * 
     * @param payCoupon 通用优惠券
     * @return 通用优惠券
     */
    @Override
    public List<PayCoupon> selectPayCouponList(PayCoupon payCoupon) {
        return payCouponMapper.selectPayCouponList(payCoupon);
    }

    /**
     * 新增通用优惠券
     *
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    @Override
    public int insertPayCoupon(PayCoupon payCoupon) {
        // 生成唯一的优惠码
        payCoupon.setCode(generateUniqueCode());
        payCoupon.setCreateTime(LocalDateTime.now());
        payCoupon.setCreateBy(SecurityUtils.getUsername());
        return payCouponMapper.insertPayCoupon(payCoupon);
    }

    /**
     * 修改通用优惠券
     * 
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    @Override
    public int updatePayCoupon(PayCoupon payCoupon) {
        payCoupon.setUpdateTime(LocalDateTime.now());
        payCoupon.setUpdateBy(SecurityUtils.getUsername());
        return payCouponMapper.updatePayCoupon(payCoupon);
    }

    /**
     * 批量删除通用优惠券
     * 
     * @param ids 需要删除的通用优惠券主键
     * @return 结果
     */
    @Override
    public int deletePayCouponByIds(Long[] ids) {
        return payCouponMapper.deletePayCouponByIds(ids);
    }

    /**
     * 删除通用优惠券信息
     * 
     * @param id 通用优惠券主键
     * @return 结果
     */
    @Override
    public int deletePayCouponById(Long id) {
        return payCouponMapper.deletePayCouponById(id);
    }

    /**
     * 启用/禁用通用优惠券
     *
     * @param id 通用优惠券主键
     * @param valid 有效状态
     * @return 结果
     */
    @Override
    public int updatePayCouponStatus(Long id, Boolean valid) {
        PayCoupon payCoupon = new PayCoupon();
        payCoupon.setId(id);
        payCoupon.setValid(valid);
        payCoupon.setUpdateTime(LocalDateTime.now());
        payCoupon.setUpdateBy(SecurityUtils.getUsername());
        return payCouponMapper.updatePayCoupon(payCoupon);
    }

    /**
     * 生成唯一的优惠码
     *
     * @return 8位大写字母+数字组成的优惠码
     */
    @Override
    public String generateUniqueCode() {
        String code;
        int maxAttempts = 100; // 最大尝试次数，防止无限循环
        int attempts = 0;

        do {
            code = generateRandomCode();
            attempts++;
            if (attempts > maxAttempts) {
                throw new RuntimeException("生成唯一优惠码失败，请重试");
            }
        } while (isCodeExists(code));

        return code;
    }

    /**
     * 生成8位随机优惠码（大写字母+数字）
     *
     * @return 8位优惠码
     */
    private String generateRandomCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < 8; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }

        return code.toString();
    }

    /**
     * 检查优惠码是否已存在
     *
     * @param code 优惠码
     * @return 是否存在
     */
    @Override
    public boolean isCodeExists(String code) {
        return payCouponMapper.countByCode(code) > 0;
    }
}
