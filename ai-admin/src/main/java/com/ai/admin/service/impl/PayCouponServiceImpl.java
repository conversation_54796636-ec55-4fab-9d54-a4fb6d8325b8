package com.ai.admin.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import com.ai.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ai.admin.mapper.PayCouponMapper;
import com.ai.admin.domain.PayCoupon;
import com.ai.admin.service.IPayCouponService;

/**
 * 通用优惠券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class PayCouponServiceImpl extends ServiceImpl<PayCouponMapper, PayCoupon> implements IPayCouponService {
    @Autowired
    private PayCouponMapper payCouponMapper;

    /**
     * 查询通用优惠券
     * 
     * @param id 通用优惠券主键
     * @return 通用优惠券
     */
    @Override
    public PayCoupon selectPayCouponById(Long id) {
        return payCouponMapper.selectPayCouponById(id);
    }

    /**
     * 查询通用优惠券列表
     * 
     * @param payCoupon 通用优惠券
     * @return 通用优惠券
     */
    @Override
    public List<PayCoupon> selectPayCouponList(PayCoupon payCoupon) {
        return payCouponMapper.selectPayCouponList(payCoupon);
    }

    /**
     * 新增通用优惠券
     *
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    @Override
    public int insertPayCoupon(PayCoupon payCoupon) {
        // 生成唯一的优惠码
        payCoupon.setCode(generateUniqueCode());
        payCoupon.setCreateTime(LocalDateTime.now());
        payCoupon.setCreateBy(SecurityUtils.getUsername());
        return payCouponMapper.insertPayCoupon(payCoupon);
    }

    /**
     * 修改通用优惠券
     * 
     * @param payCoupon 通用优惠券
     * @return 结果
     */
    @Override
    public int updatePayCoupon(PayCoupon payCoupon) {
        payCoupon.setUpdateTime(LocalDateTime.now());
        payCoupon.setUpdateBy(SecurityUtils.getUsername());
        return payCouponMapper.updatePayCoupon(payCoupon);
    }

    /**
     * 批量删除通用优惠券
     * 
     * @param ids 需要删除的通用优惠券主键
     * @return 结果
     */
    @Override
    public int deletePayCouponByIds(Long[] ids) {
        return payCouponMapper.deletePayCouponByIds(ids);
    }

    /**
     * 删除通用优惠券信息
     * 
     * @param id 通用优惠券主键
     * @return 结果
     */
    @Override
    public int deletePayCouponById(Long id) {
        return payCouponMapper.deletePayCouponById(id);
    }

    /**
     * 启用/禁用通用优惠券
     * 
     * @param id 通用优惠券主键
     * @param valid 有效状态
     * @return 结果
     */
    @Override
    public int updatePayCouponStatus(Long id, Boolean valid) {
        PayCoupon payCoupon = new PayCoupon();
        payCoupon.setId(id);
        payCoupon.setValid(valid);
        payCoupon.setUpdateTime(LocalDateTime.now());
        payCoupon.setUpdateBy(SecurityUtils.getUsername());
        return payCouponMapper.updatePayCoupon(payCoupon);
    }
}
