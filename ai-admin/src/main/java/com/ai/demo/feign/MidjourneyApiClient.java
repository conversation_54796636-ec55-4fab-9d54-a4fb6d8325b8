package com.ai.demo.feign;

import com.ai.admin.domain.dto.MidjourneyResponse;
import com.ai.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Midjourney API客户端接口
 *
 * <AUTHOR>
 */
@FeignClient(url = "${midjourney.api.base-url}", name = "midjourney-api", fallbackFactory = MidjourneyApiFallbackFactory.class,
        configuration = FeignConfig.class)
public interface MidjourneyApiClient {
    /**
     * Midjourney账号信息查询
     */
    @GetMapping(path = "/midjourney/v1/info", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    MidjourneyResponse info(@RequestHeader("TT-API-KEY") String apiKey);
}
