package com.ai.quartz.task.activity;

import com.ai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/activity")
public class UserActivityController {

    @Autowired
    private UserActivityService userActivityService;

    @PostMapping("/log")
    public void logUserActivity(@RequestParam String userId) {
        userActivityService.logUserActivity(userId);
    }

    @GetMapping("/dau")
    public long getDailyActiveUsers() {
        Date beijingDate = new Date();
        String startOfDay = DateUtils.formatDateByPattern(beijingDate,DateUtils.YYYY_MM_DD_00_00_00);
        String endOfDay = DateUtils.formatDateByPattern(beijingDate,DateUtils.YYYY_MM_DD_23_59_59);

        return userActivityService.getDailyActiveUsers(DateUtils.stringToLocalDate(startOfDay),DateUtils.stringToLocalDate(endOfDay));
    }

    @GetMapping("/yesterday-dau")
    public long getYesterdayDailyActiveUsers() {
        Date beijingDate = new Date();
        String theDayMidnight = DateUtils.formatDateByPattern(beijingDate,DateUtils.YYYY_MM_DD_00_00_00);
//        //昨天时间凌晨
        Date yesterdayDate = new Date(beijingDate.getTime() - 1000 * 60 * 60 * 24);
        String yesterdayMidnight = DateUtils.formatDateByPattern(new Date(beijingDate.getTime() - 1000 * 60 * 60 * 24),DateUtils.YYYY_MM_DD_00_00_00);


        return userActivityService.getDailyActiveUsers(DateUtils.stringToLocalDate(yesterdayMidnight),DateUtils.stringToLocalDate(theDayMidnight));
    }

//    @GetMapping("/wau")
//    public long getWeeklyActiveUsers(@RequestParam Date startDate, @RequestParam Date endDate) {
//        return userActivityService.getWeeklyActiveUsers(startDate, endDate);
//    }
//
//    @GetMapping("/mau")
//    public long getMonthlyActiveUsers(@RequestParam Date startDate, @RequestParam Date endDate) {
//        return userActivityService.getMonthlyActiveUsers(startDate, endDate);
//    }
}
