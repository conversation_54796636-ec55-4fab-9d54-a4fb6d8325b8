package com.ai.notice.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

import java.time.LocalDateTime;

/**
 * 停服公告对象 gpt_suspension_message
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_suspension_message", description = "停服公告")
@TableName("gpt_suspension_message")
public class GptSuspensionMessage extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 公告版本号 */
    @ApiModelProperty("公告版本号")
    @Excel(name = "公告版本号")
    private Integer version;

    /** 是否有效（0否 1是） */
    @ApiModelProperty("是否有效")
    @Excel(name = "是否有效", readConverterExp = "0=否,1=是")
    private Integer isEfficient;

    /** 停服开始时间(秒级别时间戳) */
    @ApiModelProperty("停服开始时间(秒级别时间戳)")
    private Long startTime;

    /** 停服结束时间(秒级别时间戳) */
    @ApiModelProperty("停服结束时间(秒级别时间戳)")
    private Long endTime;


    /** 停服结束时间(秒级别时间戳) */
    @ApiModelProperty("停服结束时间(北京时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    @Excel(name = "停服结束时间(秒级别时间戳)")
    private LocalDateTime endTimeDate;


    @ApiModelProperty("停服开始时间(北京时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(exist = false)
    @Excel(name = "停服开始时间(秒级别时间戳)")
    private LocalDateTime startTimeDate;

}
