package com.ai.task.controller;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.ai.common.utils.PythonApiUtils;
import com.ai.common.utils.dto.ServerStatus;
import com.ai.task.domain.dto.ShelvesInstance;
import com.ai.task.domain.dto.ShelvesInstanceBatch;
import com.ai.task.domain.vo.GetInstanceRuleVo;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.task.domain.GptLbInstance;
import com.ai.task.service.IGptLbInstanceService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 服务器实例列Controller
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@RestController
@Api(value = "服务器实例列控制器", tags = {"服务器实例列管理"})
@RequestMapping("/task/instance")
public class GptLbInstanceController extends BaseController {
    @Autowired
    private IGptLbInstanceService gptLbInstanceService;

    @Resource
    private PythonApiUtils pythonApiUtils;

    /**
     * 查询服务器实例列列表
     */
    @PreAuthorize("@ss.hasPermi('task:instance:list')")
    @ApiOperation("查询服务器实例列列表")
    @GetMapping("/list")
    public TableDataInfo list(GptLbInstance gptLbInstance) {
        startPage();
        List<GptLbInstance> list = gptLbInstanceService.selectGptLbInstanceList(gptLbInstance);
        return getDataTable(list);
    }

    /**
     * 导出服务器实例列列表
     */
    @ApiOperation("导出服务器实例列列表")
    @PreAuthorize("@ss.hasPermi('task:instance:export')")
    @Log(title = "服务器实例列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptLbInstance gptLbInstance) {
        List<GptLbInstance> list = gptLbInstanceService.selectGptLbInstanceList(gptLbInstance);
        ExcelUtil<GptLbInstance> util = new ExcelUtil<GptLbInstance>(GptLbInstance.class);
        util.exportExcel(response, list, "服务器实例列数据");
    }

    /**
     * 获取服务器实例列详细信息
     */
    @ApiOperation("获取服务器实例列详细信息")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptLbInstanceService.selectGptLbInstanceById(id));
    }

    /**
     * 新增服务器实例列
     */
    @ApiOperation("新增服务器实例列")
    @PreAuthorize("@ss.hasPermi('task:instance:add')")
    @Log(title = "服务器实例列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptLbInstance gptLbInstance) {
        return toAjax(gptLbInstanceService.insertGptLbInstance(gptLbInstance));
    }

    /**
     * 修改服务器实例列
     */
    @ApiOperation("修改服务器实例列")
    @PreAuthorize("@ss.hasPermi('task:instance:edit')")
    @Log(title = "服务器实例列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptLbInstance gptLbInstance) {
        return toAjax(gptLbInstanceService.updateGptLbInstance(gptLbInstance));
    }

    /**
     * 删除服务器实例列
     */
    @ApiOperation("删除服务器实例列")
    @PreAuthorize("@ss.hasPermi('task:instance:remove')")
    @Log(title = "服务器实例列", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptLbInstanceService.deleteGptLbInstanceByIds(ids));
    }


    /**
     * 服务器实例上架
     *
     * @param sInstance 任务调度服务器实例上架传入参数
     * @return 结果
     */
    @ApiOperation("服务器实例上架")
    @PreAuthorize("@ss.hasPermi('task:instance:edit')")
    @Log(title = "服务器实例上架", businessType = BusinessType.UPDATE)
    @PostMapping("/shelves")
    public AjaxResult shelvesInstance(@RequestBody ShelvesInstance sInstance) {
        return gptLbInstanceService.shelvesInstance(sInstance);
    }


    /**
     * 服务器实例下架
     *
     * @param sInstance 任务调度服务器实例上架传入参数
     * @return 结果
     */
    @ApiOperation("服务器实例下架")
    @PreAuthorize("@ss.hasPermi('task:instance:edit')")
    @Log(title = "服务器实例下架", businessType = BusinessType.UPDATE)
    @PostMapping("/offTheShelves")
    public AjaxResult offTheShelvesInstance(@RequestBody ShelvesInstance sInstance) {
        return gptLbInstanceService.offTheShelvesInstance(sInstance);
    }

    /**
     * 规则功能列表（下拉框）
     *
     * @return 结果
     */
    @ApiOperation("规则功能列表（下拉框）")
    @PreAuthorize("@ss.hasPermi('task:instance:list')")
    @GetMapping("/getFeaturesRule/{instance}")
    public AjaxResult setFeaturesRule(@PathVariable String instance) {
        return AjaxResult.success(gptLbInstanceService.getFeaturesRule(instance));
    }

    /**
     * 查看服务器规则模型功能组
     *
     * @return 结果
     */
    @ApiOperation("查看服务器规则模型功能组")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping("/getRuleModelFeatureGroup/{id}")
    public AjaxResult getRuleModelFeatureGroup(@PathVariable Long id) {
        return AjaxResult.success(gptLbInstanceService.getRuleModelFeatureGroup(id));
    }

    /**
     * 查看服务器规则
     *
     * @return 结果
     */
    @ApiOperation("查看服务器规则")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping("/getRule/{id}")
    public List<GetInstanceRuleVo> getRule(@PathVariable Long id) {
        return gptLbInstanceService.getRule(id);
    }

    /**
     * 热切换
     *
     * @return 结果
     */
    @ApiOperation("热切换")
    @PreAuthorize("@ss.hasPermi('task:instance:edit')")
    @Log(title = "热切换", businessType = BusinessType.UPDATE)
    @PostMapping("/hotSwapped")
    public AjaxResult hotSwapped(@RequestBody ShelvesInstance sInstance) {
        AjaxResult ajaxResult = gptLbInstanceService.offTheShelvesInstance(sInstance);
        if (ajaxResult.isSuccess()) {
            return gptLbInstanceService.shelvesInstance(sInstance);
        } else {
            return ajaxResult;
        }
    }

    /**
     * 返回服务器当前状态
     *
     * @return 结果
     */
    @ApiOperation("返回服务器当前状态")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping("/getServerStatus")
    public AjaxResult getServerStatus() throws JsonProcessingException {
        // 获取当前服务器状态列表
        List<ServerStatus> serverStatus = pythonApiUtils.getServerStatus();

        // 获取数据库中的实例列表
        List<GptLbInstance> list = gptLbInstanceService.selectGptLbInstanceList(null);

        // 从 list 中提取已存在的服务器 ID
        Set<String> existingIds = list.stream()
                .map(GptLbInstance::getInstance)
                .collect(Collectors.toSet());

        // 过滤 serverStatus，去除在 existingIds 中的服务器
        List<ServerStatus> filteredServerStatus = serverStatus.stream()
                .filter(server -> !existingIds.contains(server.getId()))
                .collect(Collectors.toList());

        return AjaxResult.success(filteredServerStatus);
    }

    /**
     * 查看服务器规则(下架下拉框)
     *
     * @return 结果
     */
    @ApiOperation("查看服务器规则(下架下拉框)")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping("/getOffRule/{id}")
    public AjaxResult getOffRule(@PathVariable Long id){
        return AjaxResult.success(gptLbInstanceService.getOffRule(id));
    }

    /**
     * redis 的规则从示例ID 改为ip
     *
     * @return 结果
     */
    @ApiOperation("redis 的规则从示例ID 改为ip")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping("/instance-to-ip")
    public AjaxResult copyInstanceToIp(){
        return gptLbInstanceService.copyInstanceToIp();
    }

    /**
     * redis 的规则ip 改为示例
     *
     * @return 结果
     */
    @ApiOperation("redis 的规则ip 改为示例")
    @PreAuthorize("@ss.hasPermi('task:instance:query')")
    @GetMapping("/ip-to-instance")
    public AjaxResult copyIpToInstance(){
        return gptLbInstanceService.copyIpToInstance();
    }

    /**
     * 服务器实例上架(批量)
     *
     * @param shelvesInstanceBatch 任务调度服务器实例上架传入参数
     * @return 结果
     */
    @ApiOperation("服务器实例上架(批量)")
    @PreAuthorize("@ss.hasPermi('task:instance:edit')")
    @Log(title = "服务器实例上架(批量)", businessType = BusinessType.UPDATE)
    @PostMapping("/shelves-batch")
    public AjaxResult shelvesInstanceBatch(@RequestBody ShelvesInstanceBatch shelvesInstanceBatch){
        return gptLbInstanceService.shelvesInstanceBatch(shelvesInstanceBatch);
    }

    /**
     * 服务器实例下架（批量）
     *
     * @param shelvesInstanceBatch 任务调度服务器实例上架传入参数
     * @return 结果
     */
    @ApiOperation("服务器实例下架（批量）")
    @PreAuthorize("@ss.hasPermi('task:instance:edit')")
    @Log(title = "服务器实例下架（批量）", businessType = BusinessType.UPDATE)
    @PostMapping("/off-shelves-batch")
    public AjaxResult offTheShelvesInstanceBatch(@RequestBody ShelvesInstanceBatch shelvesInstanceBatch){
        return gptLbInstanceService.offTheShelvesInstanceBatch(shelvesInstanceBatch);
    }




}
