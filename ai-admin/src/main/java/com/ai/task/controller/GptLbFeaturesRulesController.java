package com.ai.task.controller;

import java.util.List;

import com.ai.task.domain.dto.FairProportionsDto;
import com.ai.task.domain.vo.GuaranteesInstanceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;

import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.task.domain.GptLbFeaturesRules;
import com.ai.task.service.IGptLbFeaturesRulesService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 分配规则Controller
 * 
 * <AUTHOR>
 * @date 2024-08-22
 */
@RestController
@Api(value = "分配规则控制器", tags = {"分配规则管理"})
@RequestMapping("/task/rules")
public class GptLbFeaturesRulesController extends BaseController {
    @Autowired
    private IGptLbFeaturesRulesService gptLbFeaturesRulesService;

    /**
     * 查询分配规则列表
     */
    @PreAuthorize("@ss.hasPermi('task:rules:list')")
    @ApiOperation("查询分配规则列表")
    @GetMapping("/list")
    public TableDataInfo list(GptLbFeaturesRules gptLbFeaturesRules) {
        startPage();
        List<GptLbFeaturesRules> list = gptLbFeaturesRulesService.selectGptLbFeaturesRulesList(gptLbFeaturesRules);
        return getDataTable(list);
    }

    /**
     * 导出分配规则列表
     */
    @ApiOperation("导出分配规则列表")
    @PreAuthorize("@ss.hasPermi('task:rules:export')")
    @Log(title = "分配规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptLbFeaturesRules gptLbFeaturesRules) {
        List<GptLbFeaturesRules> list = gptLbFeaturesRulesService.selectGptLbFeaturesRulesList(gptLbFeaturesRules);
        ExcelUtil<GptLbFeaturesRules> util = new ExcelUtil<GptLbFeaturesRules>(GptLbFeaturesRules.class);
        util.exportExcel(response, list, "分配规则数据");
    }

    /**
     * 获取分配规则详细信息
     */
    @ApiOperation("获取分配规则详细信息")
    @PreAuthorize("@ss.hasPermi('task:rules:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptLbFeaturesRulesService.selectGptLbFeaturesRulesById(id));
    }

    /**
     * 新增分配规则
     */
    @ApiOperation("新增分配规则")
    @PreAuthorize("@ss.hasPermi('task:rules:add')")
    @Log(title = "分配规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptLbFeaturesRules gptLbFeaturesRules) {
        return toAjax(gptLbFeaturesRulesService.insertGptLbFeaturesRules(gptLbFeaturesRules));
    }

    /**
     * 修改分配规则
     */
    @ApiOperation("修改分配规则")
    @PreAuthorize("@ss.hasPermi('task:rules:edit')")
    @Log(title = "分配规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptLbFeaturesRules gptLbFeaturesRules) {
        return toAjax(gptLbFeaturesRulesService.updateGptLbFeaturesRules(gptLbFeaturesRules));
    }

    /**
     * 删除分配规则
     */
    @ApiOperation("删除分配规则")
    @PreAuthorize("@ss.hasPermi('task:rules:remove')")
    @Log(title = "分配规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gptLbFeaturesRulesService.deleteGptLbFeaturesRulesByIds(ids));
    }

    /**
     * 设置保底规则
     *
     * @param instance '实例集(多个英文逗号分隔)
     * @return 结果
     */
    @ApiOperation("设置保底规则")
    @PreAuthorize("@ss.hasPermi('task:rules:edit')")
    @Log(title = "设置保底规则", businessType = BusinessType.UPDATE)
    @PutMapping("/setGuaranteesRule/{instance}")
    public AjaxResult setGuaranteesRule(@PathVariable String instance){
       return gptLbFeaturesRulesService.setGuaranteesRule(instance);
    }

    /**
     * 设置公平比例
     *
     * @param fairProportionsDto  公平比例传入参数
     * @return 结果
     */
    @ApiOperation("设置公平比例")
    @PreAuthorize("@ss.hasPermi('task:rules:edit')")
    @Log(title = "设置公平比例", businessType = BusinessType.UPDATE)
    @PostMapping("/setFairProportions")
    public AjaxResult setFairProportions(@RequestBody FairProportionsDto fairProportionsDto){
        return gptLbFeaturesRulesService.setFairProportions(fairProportionsDto);
    }

    /**
     * 查看保底规则
     *
     * @return 结果
     */
    @ApiOperation("查看保底规则")
    @PreAuthorize("@ss.hasPermi('task:rules:query')")
    @GetMapping("/getGuaranteesRule")
    public AjaxResult getGuaranteesRule(){
        return AjaxResult.success(gptLbFeaturesRulesService.getGuaranteesRule());
    }

    /**
     * 查看公平比例
     *
     * @return 结果
     */
    @ApiOperation("查看公平比例")
    @PreAuthorize("@ss.hasPermi('task:rules:query')")
    @GetMapping("/getFairProportions")
    public FairProportionsDto getFairProportions(){
        return gptLbFeaturesRulesService.getFairProportions();
    }

    /**
     * 查看黑名单规则
     *
     * @return 结果
     */
    @ApiOperation("查看黑名单规则")
    @PreAuthorize("@ss.hasPermi('task:rules:query')")
    @GetMapping("/getBlacklistRule/{mark}")
    public AjaxResult getBlacklistRule(@PathVariable("mark") String mark){
        return AjaxResult.success(gptLbFeaturesRulesService.getBlacklistRule(mark));
    }


    /**
     * 设置黑名单规则
     *
     * @param instance '实例集(多个英文逗号分隔)
     * @return 结果
     */
    @ApiOperation("设置黑名单规则")
    @PreAuthorize("@ss.hasPermi('task:rules:update')")
    @GetMapping("/setBlacklistRule/{instance}/{isFlux}")
    public AjaxResult setBlacklistRule(@PathVariable("instance") String instance,@PathVariable("isFlux") Boolean isFlux){
        return gptLbFeaturesRulesService.setBlacklistRule(instance,isFlux);
    }


    /**
     * 查看全能服务器规则
     *
     * @return 结果
     */
    @ApiOperation("查看全能服务器规则")
    @PreAuthorize("@ss.hasPermi('task:rules:query')")
    @GetMapping("/getAllFeatureServerIds")
    public AjaxResult getBlacklistRule(){
        return AjaxResult.success(gptLbFeaturesRulesService.getAllFeatureServerIds());
    }


    /**
     * 设置全能服务器规则
     *
     * @return 结果
     */
    @ApiOperation("设置全能服务器规则")
    @PreAuthorize("@ss.hasPermi('task:rules:query')")
    @GetMapping("/setAllFeatureServerIds/{instance}")
    public AjaxResult setAllFeatureServerIds(@PathVariable("instance") String instance){
        return gptLbFeaturesRulesService.setAllFeatureServerIds(instance);
    }
}
