package com.ai.task.service.impl;


import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.common.enums.ModelOriginType;
import com.ai.common.utils.JsonUtils;
import com.ai.common.utils.PythonApiUtils;
import com.ai.common.utils.StringUtils;
import com.ai.common.utils.dto.ModelAboutDto;
import com.ai.common.utils.dto.ModelInformation;
import com.ai.constants.LogicParamsCons;
import com.ai.task.domain.GptModelAbout;
import com.ai.task.service.IGptModelAboutService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mongodb.DuplicateKeyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.task.mapper.GptLbModelMapper;
import com.ai.task.domain.GptLbModel;
import com.ai.task.service.IGptLbModelService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 模型列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
@Slf4j
public class GptLbModelServiceImpl extends ServiceImpl<GptLbModelMapper, GptLbModel> implements IGptLbModelService {
    @Autowired
    private GptLbModelMapper gptLbModelMapper;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    @Resource
    private PythonApiUtils pythonApiUtils;

    @Resource
    private IGptModelAboutService iGptModelAboutService;

    /**
     * 查询模型列表
     *
     * @param id 模型列表主键
     * @return 模型列表
     */
    @Override
    public GptLbModel selectGptLbModelById(Long id) {
        return gptLbModelMapper.selectGptLbModelById(id);
    }

    /**
     * 查询模型列表列表
     *
     * @param gptLbModel 模型列表
     * @return 模型列表
     */
    @Override
    public List<GptLbModel> selectGptLbModelList(GptLbModel gptLbModel) {
        return gptLbModelMapper.selectGptLbModelList(gptLbModel);
    }

    /**
     * 新增模型列表
     *
     * @param gptLbModel 模型列表
     * @return 结果
     */
    @Override
    public int insertGptLbModel(GptLbModel gptLbModel) {
        gptLbModel.setCreateTime(LocalDateTime.now());
        redisCachePiclumen.setCacheMapValue(LogicParamsCons.LOADBALANCE_MODEL, gptLbModel.getModelId(), gptLbModel.getModelGroup());
        return gptLbModelMapper.insertGptLbModel(gptLbModel);
    }

    /**
     * 修改模型列表
     *
     * @param gptLbModel 模型列表
     * @return 结果
     */
    @Override
    public int updateGptLbModel(GptLbModel gptLbModel) {
        gptLbModel.setUpdateTime(LocalDateTime.now());
        if (StringUtils.isNotEmpty(gptLbModel.getModelGroup()) || StringUtils.isNotEmpty(gptLbModel.getModelId())) {
            redisCachePiclumen.setCacheMapValue(LogicParamsCons.LOADBALANCE_MODEL, gptLbModel.getModelId(), gptLbModel.getModelGroup());
        }
        redisCachePiclumen.setCacheMapValue(LogicParamsCons.LOADBALANCE_MODEL, gptLbModel.getModelId(), gptLbModel.getModelGroup());
        return gptLbModelMapper.updateGptLbModel(gptLbModel);
    }

    /**
     * 批量删除模型列表
     *
     * @param ids 需要删除的模型列表主键
     * @return 结果
     */
    @Override
    public int deleteGptLbModelByIds(Long[] ids) {
        return gptLbModelMapper.deleteGptLbModelByIds(ids);
    }

    /**
     * 删除模型列表信息
     *
     * @param id 模型列表主键
     * @return 结果
     */
    @Override
    public int deleteGptLbModelById(Long id) {
        GptLbModel gptLbModel = gptLbModelMapper.selectGptLbModelById(id);
        redisCachePiclumen.deleteCacheMapValue(LogicParamsCons.LOADBALANCE_MODEL, gptLbModel.getModelId());
        return gptLbModelMapper.deleteGptLbModelById(id);
    }

    /**
     * 查询所有不同的模型组
     *
     * @return 结果
     */
    @Override
    public List<String> selectDistinctModelGroups() {
        return gptLbModelMapper.selectDistinctModelGroups();
    }

    @Override
    public List<ModelAboutDto> listModels() throws IOException {

        List<ModelInformation.ModelAbout> modelAboutList = new ArrayList<>();

        Boolean isEmpty = true;
        /**
         * 如果redis中有数据，则直接返回
         */
        String modelListResult = redisCachePiclumen.getCacheObject(LogicParamsCons.MODEL_LIST_KEY);
        if (StringUtils.isNotBlank(modelListResult)) {
            modelAboutList.addAll(JsonUtils.writeToList(modelListResult, ModelInformation.ModelAbout.class));
            isEmpty = false;
        }

        if (isEmpty) {
            ModelInformation modelInformation = pythonApiUtils.getModelInformation();

            //调用py后端查询相关信息
            if (!CollectionUtils.isEmpty(modelInformation.getModelMessage())) {
                redisCachePiclumen.setCacheObject(LogicParamsCons.MODEL_LIST_KEY, JsonUtils.writeToString(modelInformation.getModelMessage()), 1, TimeUnit.HOURS);
                modelAboutList.addAll(modelInformation.getModelMessage());

            }
        }

        List<ModelAboutDto> modelAboutDtoList = new ArrayList<>();

        for (ModelInformation.ModelAbout modelAbout : modelAboutList) {
            ModelAboutDto modelAboutDto = new ModelAboutDto();
            BeanUtils.copyProperties(modelAbout, modelAboutDto);

            // 获取各个平台的模型列表缓存
            String webModelListResult = redisCachePiclumen.getCacheObject(LogicParamsCons.WEB_LIST_KEY);
            String iosModelListResult = redisCachePiclumen.getCacheObject(LogicParamsCons.IOS_LIST_KEY);
            String androidModelListResult = redisCachePiclumen.getCacheObject(LogicParamsCons.ANDROID_LIST_KEY);

            // 创建 platforms 列表，用于存储模型支持的平台
            List<String> platforms = new ArrayList<>();

            // 如果 web 模型列表不为空且包含当前模型的 modelId，添加 web
            if (StringUtils.isNotBlank(webModelListResult)) {
                List<ModelInformation.ModelAbout> webModelAbouts = JsonUtils.writeToList(webModelListResult, ModelInformation.ModelAbout.class);
                if (webModelAbouts.stream().anyMatch(model -> model.getModelId().equals(modelAbout.getModelId()))) {
                    platforms.add("web");
                }
            }

            // 如果 ios 模型列表不为空且包含当前模型的 modelId，添加 ios
            if (StringUtils.isNotBlank(iosModelListResult)) {
                List<ModelInformation.ModelAbout> iosModelAbouts = JsonUtils.writeToList(iosModelListResult, ModelInformation.ModelAbout.class);
                if (iosModelAbouts.stream().anyMatch(model -> model.getModelId().equals(modelAbout.getModelId()))) {
                    platforms.add("ios");
                }
            }

            // 如果 android 模型列表不为空且包含当前模型的 modelId，添加 android
            if (StringUtils.isNotBlank(androidModelListResult)) {
                List<ModelInformation.ModelAbout> androidModelAbouts = JsonUtils.writeToList(androidModelListResult, ModelInformation.ModelAbout.class);
                if (androidModelAbouts.stream().anyMatch(model -> model.getModelId().equals(modelAbout.getModelId()))) {
                    platforms.add("android");
                }
            }

            // 设置 modelAboutDto 的 platforms 属性
            modelAboutDto.setPlatforms(platforms);
            modelAboutDtoList.add(modelAboutDto);
        }

        return modelAboutDtoList;
    }

    @Override
    public AjaxResult modelsAddPlatform(String modelId, String platform) throws IOException {
        // 1. 查询所有模型数据
        List<ModelAboutDto> modelAboutDtos = listModels();

        // 2. 查找对应 modelId 的 ModelAboutDto
        ModelAboutDto targetModelAboutDto = null;
        for (ModelAboutDto modelAboutDto : modelAboutDtos) {
            if (modelAboutDto.getModelId().equals(modelId)) {
                targetModelAboutDto = modelAboutDto;
                break;
            }
        }

        // 如果没有找到对应的 ModelAboutDto
        if (targetModelAboutDto == null) {
            return AjaxResult.error("找不到对应modelId 的模型: " + modelId);
        }

        // 3. 根据传入的 platform 获取平台的模型列表
        String platformKey = getRedisKeyForPlatform(platform);
        String platformModelListResult = redisCachePiclumen.getCacheObject(platformKey);

        List<ModelInformation.ModelAbout> platformModelList = null;
        // 如果平台的模型列表为空,则新增列表
        if (StringUtils.isBlank(platformModelListResult)) {
            platformModelList = new ArrayList<>();
        } else {
            platformModelList = JsonUtils.writeToList(platformModelListResult, ModelInformation.ModelAbout.class);
        }

        // 5. 查找目标平台列表中是否已存在指定的 modelId
        boolean isExist = false;
        for (ModelInformation.ModelAbout modelAboutDto : platformModelList) {
            if (modelAboutDto.getModelId().equals(modelId)) {
                isExist = true;
                break;
            }
        }

        // 如果目标平台模型列表中已经存在该 modelId，则不进行任何操作
        if (isExist) {
            return AjaxResult.success("此平台上已存在模型.");
        }

        // 6. 如果平台中没有该模型，则将该模型添加到平台模型列表中
        ModelInformation.ModelAbout modelAboutDto = new ModelInformation.ModelAbout();
        BeanUtils.copyProperties(targetModelAboutDto, modelAboutDto);

        platformModelList.add(modelAboutDto);

        // 7. 根据 modelOrder 对列表排序
        platformModelList.sort(Comparator.comparingInt(ModelInformation.ModelAbout::getModelOrder));

        // 8. 将更新后的平台模型列表重新插入 Redis
        redisCachePiclumen.setCacheObject(platformKey, JsonUtils.writeToString(platformModelList));

        return AjaxResult.success();
    }

    @Override
    public AjaxResult modelsRemovePlatform(String modelId, String platform) throws IOException {
        // 1. 查询所有模型数据
        List<ModelAboutDto> modelAboutDtos = listModels();

        // 2. 查找对应 modelId 的 ModelAboutDto
        ModelAboutDto targetModelAboutDto = null;
        for (ModelAboutDto modelAboutDto : modelAboutDtos) {
            if (modelAboutDto.getModelId().equals(modelId)) {
                targetModelAboutDto = modelAboutDto;
                break;
            }
        }

        // 如果没有找到对应的 ModelAboutDto
        if (targetModelAboutDto == null) {
            return AjaxResult.error("找不到对应modelId 的模型: " + modelId);
        }

        // 3. 根据传入的 platform 获取平台的模型列表
        String platformKey = getRedisKeyForPlatform(platform);
        String platformModelListResult = redisCachePiclumen.getCacheObject(platformKey);

        List<ModelInformation.ModelAbout> platformModelList = null;
        // 如果平台的模型列表为空，直接返回
        if (StringUtils.isBlank(platformModelListResult)) {
            return AjaxResult.error("该平台没有模型数据.");
        } else {
            platformModelList = JsonUtils.writeToList(platformModelListResult, ModelInformation.ModelAbout.class);
        }

        // 4. 查找平台列表中是否存在该模型
        boolean isExist = false;
        ModelInformation.ModelAbout modelToRemove = null;
        for (ModelInformation.ModelAbout modelAboutDto : platformModelList) {
            if (modelAboutDto.getModelId().equals(modelId)) {
                isExist = true;
                modelToRemove = modelAboutDto;
                break;
            }
        }

        // 如果目标平台模型列表中没有找到该 modelId，则返回提示
        if (!isExist) {
            return AjaxResult.success("该模型不在该平台上.");
        }

        // 5. 如果找到了该模型，则从平台模型列表中移除
        platformModelList.remove(modelToRemove);

        // 6. 根据 modelOrder 对列表排序
        platformModelList.sort(Comparator.comparingInt(ModelInformation.ModelAbout::getModelOrder));

        // 7. 将更新后的平台模型列表重新插入 Redis
        redisCachePiclumen.setCacheObject(platformKey, JsonUtils.writeToString(platformModelList));

        return AjaxResult.success("平台上的模型已成功移除.");
    }


    // 获取对应平台的 Redis key
    private String getRedisKeyForPlatform(String platform) {
        switch (platform) {
            case "web":
                return LogicParamsCons.WEB_LIST_KEY;
            case "ios":
                return LogicParamsCons.IOS_LIST_KEY;
            case "android":
                return LogicParamsCons.ANDROID_LIST_KEY;
            default:
                throw new IllegalArgumentException("Unsupported platform: " + platform);
        }
    }


    @Override
    public void initModeAboutList() {

        //  删除老工作流模型数据
        LambdaQueryWrapper<GptModelAbout> qw = new LambdaQueryWrapper<>();
        qw.eq(GptModelAbout::getModelOriginType, ModelOriginType.workflow.getValue());
        iGptModelAboutService.remove(qw);

        try {
            List<GptModelAbout> result = new ArrayList<>();
            List<ModelAboutDto> modelAboutDtos = listModels();
            for (ModelAboutDto modelAboutDto : modelAboutDtos) {
                GptModelAbout one = new GptModelAbout();
                BeanUtils.copyProperties(modelAboutDto, one);
                one.setModelOriginType(ModelOriginType.workflow.getValue());
                one.setCreateTime(LocalDateTime.now());
                one.setUpdateTime(LocalDateTime.now());
                if (modelAboutDto.getPlatforms() != null && !modelAboutDto.getPlatforms().isEmpty()) {
                    one.setPlatform(String.join(",", modelAboutDto.getPlatforms()));
                }
                one.setDefaultConfig(JsonUtils.writeToString(modelAboutDto.getDefaultConfig()));
                one.setSupportStyleList(JsonUtils.writeToString(modelAboutDto.getSupportStyleList()));
                result.add(one);
            }
            iGptModelAboutService.saveOrUpdateBatch(result);
        } catch (IOException e) {
            log.error("初始化模型列表失败", e);
            throw new RuntimeException("初始化模型失败", e);
        }

    }

    @Override
    public List<ModelAboutDto> getModels(GptModelAbout req) {
        List<ModelAboutDto> modelAboutDtos = new ArrayList<>();
        List<GptModelAbout> gptModelAbouts = iGptModelAboutService.selectGptModelAboutList(req);

        for (GptModelAbout gptModelAbout : gptModelAbouts) {
            ModelAboutDto modelAboutDto = new ModelAboutDto();
            BeanUtils.copyProperties(gptModelAbout, modelAboutDto);
            if (StringUtils.isNotBlank(gptModelAbout.getDefaultConfig())) {
                try {
                    modelAboutDto.setDefaultConfig(JsonUtils.fromString(gptModelAbout.getDefaultConfig(), ModelInformation.DefaultConfig.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setDefaultConfig(new ModelInformation.DefaultConfig());
                }
            }
            if (StringUtils.isNotBlank(gptModelAbout.getSupportStyleList())) {
                try {
                    modelAboutDto.setSupportStyleList(JsonUtils.writeToList(gptModelAbout.getSupportStyleList(), ModelInformation.SupportStyle.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setSupportStyleList(new ArrayList<>());
                }
            }
            List<String> platforms = Arrays.asList(gptModelAbout.getPlatform().split(","));
            modelAboutDto.setPlatforms(platforms);
            modelAboutDtos.add(modelAboutDto);
        }
        return modelAboutDtos;
    }

    /**
     * 查询模型信息
     *
     * @param id 模型信息主键
     * @return 模型信息
     */
    @Override
    public GptModelAbout selectGptModelAboutById(Long id) {
        return iGptModelAboutService.selectGptModelAboutById(id);
    }

    /**
     * 保存 模板信息
     */
    @Override
    public void saveModelAbout(GptModelAbout req) {
        try {
            if (req.getId() != null) {
                iGptModelAboutService.updateGptModelAbout(req);
            } else {
                iGptModelAboutService.insertGptModelAbout(req);
            }
        } catch (Exception e) {
            log.error("保存模型信息失败", e);
            throw new RuntimeException("模型名称 或者模型id已存在，请保持唯一性");
        }
    }

    @Override
    public int deleteGptModelAboutById(Long id)
    {
        return iGptModelAboutService.deleteGptModelAboutById(id);
    }
    
    @Override
    public void refresh(String platform){
        platform = platform.toLowerCase();
        //定义redis中不同数据的key
        String webModelListKey = "webModelListKey";
        String iosModelListKey = "iosModelListKey";
        String androidModelListKey = "androidModelListKey";

        if (StringUtils.isNotBlank(platform)) {
            switch (platform) {
                case "ios":
                    log.info("刷新redis{}",iosModelListKey);
                    redisCachePiclumen.deleteObject(iosModelListKey);
                    break;
                case "android":
                    log.info("刷新redis{}",androidModelListKey);
                    redisCachePiclumen.deleteObject(androidModelListKey);
                    break;
                case "web":
                    log.info("刷新redis{}",webModelListKey);
                    redisCachePiclumen.deleteObject(webModelListKey);
                    break;
            }
        }
    }


}
