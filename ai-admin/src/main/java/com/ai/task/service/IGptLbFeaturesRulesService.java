package com.ai.task.service;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.system.domain.SysCache;
import com.ai.task.domain.dto.FairProportionsDto;
import com.ai.task.domain.vo.GuaranteesInstanceVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.task.domain.GptLbFeaturesRules;

/**
 * 分配规则Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-22
 */
public interface IGptLbFeaturesRulesService extends IService<GptLbFeaturesRules> {
    /**
     * 查询分配规则
     * 
     * @param id 分配规则主键
     * @return 分配规则
     */
    public GptLbFeaturesRules selectGptLbFeaturesRulesById(Long id);

    /**
     * 查询分配规则列表
     * 
     * @param gptLbFeaturesRules 分配规则
     * @return 分配规则集合
     */
    public List<GptLbFeaturesRules> selectGptLbFeaturesRulesList(GptLbFeaturesRules gptLbFeaturesRules);

    /**
     * 新增分配规则
     * 
     * @param gptLbFeaturesRules 分配规则
     * @return 结果
     */
    public int insertGptLbFeaturesRules(GptLbFeaturesRules gptLbFeaturesRules);

    /**
     * 修改分配规则
     * 
     * @param gptLbFeaturesRules 分配规则
     * @return 结果
     */
    public int updateGptLbFeaturesRules(GptLbFeaturesRules gptLbFeaturesRules);

    /**
     * 批量删除分配规则
     * 
     * @param ids 需要删除的分配规则主键集合
     * @return 结果
     */
    public int deleteGptLbFeaturesRulesByIds(Long[] ids);

    /**
     * 删除分配规则信息
     * 
     * @param id 分配规则主键
     * @return 结果
     */
    public int deleteGptLbFeaturesRulesById(Long id);


    /**
     * 设置保底规则
     *
     * @param instance '实例集(多个英文逗号分隔)
     * @return 结果
     */
    AjaxResult setGuaranteesRule(String instance);

    /**
     * 设置公平比例
     *
     * @param fairProportionsDto  公平比例传入参数
     * @return 结果
     */
    AjaxResult setFairProportions(FairProportionsDto fairProportionsDto);

    /**
     * 查看保底规则
     *
     * @return 结果
     */
    List<GuaranteesInstanceVo> getGuaranteesRule();


    /**
     * 查看公平比例
     *
     * @return 结果
     */
     FairProportionsDto getFairProportions();

    /**
     * 获取任务调度缓存名称
     *
     * @return 结果
     */
     List<SysCache> getTaskCacheNames();

    /**
     * 查看黑名单规则
     *
     * @return 结果
     */
     List<GuaranteesInstanceVo> getBlacklistRule(String mark);

    /**
     * 设置黑名单规则
     *
     * @param instance '实例集(多个英文逗号分隔)
     * @return 结果
     */
     AjaxResult setBlacklistRule(String instance,Boolean isFlux);

    /**
     * 查看全能服务器规则
     *
     * @return 结果
     */
    List<GuaranteesInstanceVo>  getAllFeatureServerIds();


    /**
     * 设置全能服务器规则
     *
     * @return 结果
     */
    AjaxResult setAllFeatureServerIds(String instance);
}
