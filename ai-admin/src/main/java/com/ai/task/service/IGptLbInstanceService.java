package com.ai.task.service;

import java.util.List;
import java.util.Map;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.utils.dto.ModelInformation;
import com.ai.task.domain.dto.ShelvesInstance;
import com.ai.operation.domain.vo.FeaturesRuleDropDownVo;
import com.ai.task.domain.dto.ShelvesInstanceBatch;
import com.ai.task.domain.vo.GetInstanceRuleVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.task.domain.GptLbInstance;

/**
 * 服务器实例列Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface IGptLbInstanceService extends IService<GptLbInstance> {
    /**
     * 查询服务器实例列
     * 
     * @param id 服务器实例列主键
     * @return 服务器实例列
     */
    public GptLbInstance selectGptLbInstanceById(Long id);

    /**
     * 查询服务器实例列列表
     * 
     * @param gptLbInstance 服务器实例列
     * @return 服务器实例列集合
     */
    public List<GptLbInstance> selectGptLbInstanceList(GptLbInstance gptLbInstance);

    /**
     * 新增服务器实例列
     * 
     * @param gptLbInstance 服务器实例列
     * @return 结果
     */
    public int insertGptLbInstance(GptLbInstance gptLbInstance);

    /**
     * 修改服务器实例列
     * 
     * @param gptLbInstance 服务器实例列
     * @return 结果
     */
    public int updateGptLbInstance(GptLbInstance gptLbInstance);

    /**
     * 批量删除服务器实例列
     * 
     * @param ids 需要删除的服务器实例列主键集合
     * @return 结果
     */
    public int deleteGptLbInstanceByIds(Long[] ids);

    /**
     * 删除服务器实例列信息
     * 
     * @param id 服务器实例列主键
     * @return 结果
     */
    public int deleteGptLbInstanceById(Long id);

    /**
     * 服务器实例上架
     *
     * @param sInstance 任务调度服务器实例上架传入参数
     * @return 结果
     */
     AjaxResult shelvesInstance(ShelvesInstance sInstance);

    /**
     * 服务器实例下架
     *
     * @param id 服务器实例id
     * @return 结果
     */
     AjaxResult offTheShelvesInstance(ShelvesInstance sInstance);

    /**
     * 监控服务自动下架服务
     * @param address
     */
    Map<String, Object> downInstanceAuto(String address);

    /**
     * 监控服务自动下上架服务
     * @param address
     * @return
     */
    Map<String, Object> upInstanceAuto(String address);

    /**
     * 规则功能列表（下拉框）
     *
     * @return 结果
     */
    List <FeaturesRuleDropDownVo> getFeaturesRule(String instance);

    /**
     * 查看服务器规则模型功能组
     *
     * @return 结果
     */
    AjaxResult getRuleModelFeatureGroup(Long id);

    /**
     * 查看服务器规则
     *
     * @return 结果
     */
    List<GetInstanceRuleVo> getRule(Long id);

    /**
     * 查看服务器规则(下架下拉框)
     *
     * @return 结果
     */
    List<GetInstanceRuleVo> getOffRule(Long id);

    /**
     * redis 的规则从示例ID 改为ip
     *
     * @return 结果
     */
    AjaxResult copyInstanceToIp();

    /**
     * redis 的规则ip 改为示例
     *
     * @return 结果
     */
    AjaxResult copyIpToInstance();


    /**
     * 服务器实例上架(批量)
     *
     * @param shelvesInstanceBatch 任务调度服务器实例上架传入参数
     * @return 结果
     */
    AjaxResult shelvesInstanceBatch(ShelvesInstanceBatch shelvesInstanceBatch);

    /**
     * 服务器实例下架（批量）
     *
     * @param sInstance 服务器实例id
     * @return 结果
     */
    AjaxResult offTheShelvesInstanceBatch(ShelvesInstanceBatch sInstance);
}
