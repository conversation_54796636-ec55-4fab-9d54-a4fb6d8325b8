package com.ai.task.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "任务调度公平比例传入参数")
public class FairProportionsDto {

    @ApiModelProperty("公平队列任务阈值")
    private Integer fairTaskThreshold;   // 公平队列任务阈值

    @ApiModelProperty("非公平队列任务阈值")
    private Integer unfairTaskThreshold; // 非公平队列任务阈值

    @ApiModelProperty("预载gpu队列任务阈值")
    private Integer preloadingTaskThreshold; // 预载gpu队列任务阈值

    @ApiModelProperty("阻塞等待时长，毫秒")
    private Integer blobFairWaitTime; // 阻塞等待时长，毫秒
}
