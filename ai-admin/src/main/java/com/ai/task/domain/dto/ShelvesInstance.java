package com.ai.task.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "任务调度服务器实例上架传入参数")
public class ShelvesInstance {

    @ApiModelProperty("分配规则id")
    private Long featuresRuleId;

    @ApiModelProperty("实例id")
    private Long id;

    @ApiModelProperty("服务器标识")
    private String mark;

    @ApiModelProperty("上架类型0默认 1 头插 2 尾插")
    private Integer onTheShelvesType;
}
