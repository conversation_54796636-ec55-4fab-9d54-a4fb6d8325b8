package com.ai.task.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 分配规则对象 gpt_lb_features_rules
 * 
 * <AUTHOR>
 * @date 2024-08-22
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_lb_features_rules", description = "分配规则")
@TableName("gpt_lb_features_rules")
public class GptLbFeaturesRules extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 模型组 */
    @ApiModelProperty("模型组")
    @Excel(name = "模型组")
    private String modelGroup;

    /** 模组组标识 */
    @ApiModelProperty("模组组标识")
    @Excel(name = "模组组标识")
    private String modelMark;

    /** 功能组 */
    @ApiModelProperty("功能组")
    @Excel(name = "功能组")
    private String featureGroup;

    /** 模型组标识 */
    @ApiModelProperty("模型组标识")
    @Excel(name = "模型组标识")
    private String featureMark;

    /** 实例集(多个英文逗号分隔) */
    @ApiModelProperty("实例集(多个英文逗号分隔)")
    @Excel(name = "实例集(多个英文逗号分隔)")
    private String instance;

    /** 规则状态 */
    @ApiModelProperty("规则状态")
    @Excel(name = "规则状态")
    private Integer ruleStatus;

}
