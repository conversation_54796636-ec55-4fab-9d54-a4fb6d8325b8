package com.ai.task.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.task.domain.GptLbFeaturesRules;

/**
 * 分配规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-22
 */
public interface GptLbFeaturesRulesMapper extends BaseMapper<GptLbFeaturesRules> {
    /**
     * 查询分配规则
     * 
     * @param id 分配规则主键
     * @return 分配规则
     */
    public GptLbFeaturesRules selectGptLbFeaturesRulesById(Long id);

    /**
     * 查询分配规则列表
     * 
     * @param gptLbFeaturesRules 分配规则
     * @return 分配规则集合
     */
    public List<GptLbFeaturesRules> selectGptLbFeaturesRulesList(GptLbFeaturesRules gptLbFeaturesRules);

    /**
     * 新增分配规则
     * 
     * @param gptLbFeaturesRules 分配规则
     * @return 结果
     */
    public int insertGptLbFeaturesRules(GptLbFeaturesRules gptLbFeaturesRules);

    /**
     * 修改分配规则
     * 
     * @param gptLbFeaturesRules 分配规则
     * @return 结果
     */
    public int updateGptLbFeaturesRules(GptLbFeaturesRules gptLbFeaturesRules);

    /**
     * 删除分配规则
     * 
     * @param id 分配规则主键
     * @return 结果
     */
    public int deleteGptLbFeaturesRulesById(Long id);

    /**
     * 批量删除分配规则
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGptLbFeaturesRulesByIds(Long[] ids);
}
