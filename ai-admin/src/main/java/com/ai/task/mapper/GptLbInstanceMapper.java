package com.ai.task.mapper;

import java.util.List;

import com.ai.task.domain.vo.IntanceRuleVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.task.domain.GptLbInstance;

/**
 * 服务器实例列Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-21
 */
public interface GptLbInstanceMapper extends BaseMapper<GptLbInstance> {
    /**
     * 查询服务器实例列
     * 
     * @param id 服务器实例列主键
     * @return 服务器实例列
     */
    GptLbInstance selectGptLbInstanceById(Long id);

    /**
     * 查询服务器实例列列表
     * 
     * @param gptLbInstance 服务器实例列
     * @return 服务器实例列集合
     */
    List<GptLbInstance> selectGptLbInstanceList(GptLbInstance gptLbInstance);

    /**
     * 新增服务器实例列
     * 
     * @param gptLbInstance 服务器实例列
     * @return 结果
     */
     int insertGptLbInstance(GptLbInstance gptLbInstance);

    /**
     * 修改服务器实例列
     * 
     * @param gptLbInstance 服务器实例列
     * @return 结果
     */
    int updateGptLbInstance(GptLbInstance gptLbInstance);

    /**
     * 删除服务器实例列
     * 
     * @param id 服务器实例列主键
     * @return 结果
     */
    int deleteGptLbInstanceById(Long id);

    /**
     * 批量删除服务器实例列
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteGptLbInstanceByIds(Long[] ids);

    /**
     * 获取示例规则
     *
     * @return 结果
     */
    List<IntanceRuleVo> getIntanceRule();
}
