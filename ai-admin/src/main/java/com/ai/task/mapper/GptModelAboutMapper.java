package com.ai.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.task.domain.GptModelAbout;

import java.util.List;

/**
 * 模型信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface GptModelAboutMapper extends BaseMapper<GptModelAbout> {

    /**
     * 查询模型信息列表
     *
     * @param gptModelAbout 模型信息
     * @return 模型信息集合
     */
    List<GptModelAbout> selectGptModelAboutList(GptModelAbout gptModelAbout);


}
