package com.ai.tracking.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.tracking.domain.KpiLumenCost;
import com.ai.tracking.service.IKpiLumenCostService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * kpi 数据追踪-lumen消耗Controller
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@Api(value = "kpi 数据追踪-lumen消耗控制器", tags = {"kpi 数据追踪-lumen消耗管理"})
@RequestMapping("/tracking/cost")
public class KpiLumenCostController extends BaseController {
    @Autowired
    private IKpiLumenCostService kpiLumenCostService;

    /**
     * 查询kpi 数据追踪-lumen消耗列表
     */
    @PreAuthorize("@ss.hasPermi('tracking:cost:list')")
    @ApiOperation("查询kpi 数据追踪-lumen消耗列表")
    @GetMapping("/list")
    public TableDataInfo list(KpiLumenCost kpiLumenCost) {
        startPage();
        List<KpiLumenCost> list = kpiLumenCostService.selectKpiLumenCostList(kpiLumenCost);
        return getDataTable(list);
    }

    /**
     * 导出kpi 数据追踪-lumen消耗列表
     */
    @ApiOperation("导出kpi 数据追踪-lumen消耗列表")
    @PreAuthorize("@ss.hasPermi('tracking:cost:export')")
    @Log(title = "kpi 数据追踪-lumen消耗", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KpiLumenCost kpiLumenCost) {
        List<KpiLumenCost> list = kpiLumenCostService.selectKpiLumenCostList(kpiLumenCost);
        ExcelUtil<KpiLumenCost> util = new ExcelUtil<KpiLumenCost>(KpiLumenCost.class);
        util.exportExcel(response, list, "kpi 数据追踪-lumen消耗数据");
    }

    /**
     * 获取kpi 数据追踪-lumen消耗详细信息
     */
    @ApiOperation("获取kpi 数据追踪-lumen消耗详细信息")
    @PreAuthorize("@ss.hasPermi('tracking:cost:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(kpiLumenCostService.selectKpiLumenCostById(id));
    }

    /**
     * 新增kpi 数据追踪-lumen消耗
     */
    @ApiOperation("新增kpi 数据追踪-lumen消耗")
    @PreAuthorize("@ss.hasPermi('tracking:cost:add')")
    @Log(title = "kpi 数据追踪-lumen消耗", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KpiLumenCost kpiLumenCost) {
        return toAjax(kpiLumenCostService.insertKpiLumenCost(kpiLumenCost));
    }

    /**
     * 修改kpi 数据追踪-lumen消耗
     */
    @ApiOperation("修改kpi 数据追踪-lumen消耗")
    @PreAuthorize("@ss.hasPermi('tracking:cost:edit')")
    @Log(title = "kpi 数据追踪-lumen消耗", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KpiLumenCost kpiLumenCost) {
        return toAjax(kpiLumenCostService.updateKpiLumenCost(kpiLumenCost));
    }

    /**
     * 删除kpi 数据追踪-lumen消耗
     */
    @ApiOperation("删除kpi 数据追踪-lumen消耗")
    @PreAuthorize("@ss.hasPermi('tracking:cost:remove')")
    @Log(title = "kpi 数据追踪-lumen消耗", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(kpiLumenCostService.deleteKpiLumenCostByIds(ids));
    }
}
