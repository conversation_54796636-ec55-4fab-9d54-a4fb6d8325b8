package com.ai.tracking.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.tracking.domain.KpiBasicData;

/**
 * 基本数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-12
 */
public interface IKpiBasicDataService extends IService<KpiBasicData> {
    /**
     * 查询基本数据
     * 
     * @param id 基本数据主键
     * @return 基本数据
     */
    KpiBasicData selectKpiBasicDataById(Long id);

    /**
     * 查询基本数据列表
     * 
     * @param kpiBasicData 基本数据
     * @return 基本数据集合
     */
    List<KpiBasicData> selectKpiBasicDataList(KpiBasicData kpiBasicData);

    /**
     * 新增基本数据
     * 
     * @param kpiBasicData 基本数据
     * @return 结果
     */
    int insertKpiBasicData(KpiBasicData kpiBasicData);

    /**
     * 修改基本数据
     * 
     * @param kpiBasicData 基本数据
     * @return 结果
     */
    int updateKpiBasicData(KpiBasicData kpiBasicData);

    /**
     * 批量删除基本数据
     * 
     * @param ids 需要删除的基本数据主键集合
     * @return 结果
     */
    int deleteKpiBasicDataByIds(Long[] ids);

    /**
     * 删除基本数据信息
     * 
     * @param id 基本数据主键
     * @return 结果
     */
    int deleteKpiBasicDataById(Long id);
}
