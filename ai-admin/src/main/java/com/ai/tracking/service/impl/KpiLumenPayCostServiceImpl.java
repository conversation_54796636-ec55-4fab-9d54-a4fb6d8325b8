package com.ai.tracking.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.tracking.mapper.KpiLumenPayCostMapper;
import com.ai.tracking.domain.KpiLumenPayCost;
import com.ai.tracking.service.IKpiLumenPayCostService;

/**
 * lumen使用Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class KpiLumenPayCostServiceImpl extends ServiceImpl<KpiLumenPayCostMapper, KpiLumenPayCost> implements IKpiLumenPayCostService
{
    @Autowired
    private KpiLumenPayCostMapper kpiLumenPayCostMapper;

    /**
     * 查询lumen使用
     * 
     * @param id lumen使用主键
     * @return lumen使用
     */
    @Override
    public KpiLumenPayCost selectKpiLumenPayCostById(Long id)
    {
        return kpiLumenPayCostMapper.selectKpiLumenPayCostById(id);
    }

    /**
     * 查询lumen使用列表
     * 
     * @param kpiLumenPayCost lumen使用
     * @return lumen使用
     */
    @Override
    public List<KpiLumenPayCost> selectKpiLumenPayCostList(KpiLumenPayCost kpiLumenPayCost)
    {
        return kpiLumenPayCostMapper.selectKpiLumenPayCostList(kpiLumenPayCost);
    }

    /**
     * 新增lumen使用
     * 
     * @param kpiLumenPayCost lumen使用
     * @return 结果
     */
    @Override
    public int insertKpiLumenPayCost(KpiLumenPayCost kpiLumenPayCost)
    {
        kpiLumenPayCost.setCreateTime(LocalDateTime.now());
        return kpiLumenPayCostMapper.insertKpiLumenPayCost(kpiLumenPayCost);
    }

    /**
     * 修改lumen使用
     * 
     * @param kpiLumenPayCost lumen使用
     * @return 结果
     */
    @Override
    public int updateKpiLumenPayCost(KpiLumenPayCost kpiLumenPayCost)
    {
        kpiLumenPayCost.setUpdateTime(LocalDateTime.now());
        return kpiLumenPayCostMapper.updateKpiLumenPayCost(kpiLumenPayCost);
    }

    /**
     * 批量删除lumen使用
     * 
     * @param ids 需要删除的lumen使用主键
     * @return 结果
     */
    @Override
    public int deleteKpiLumenPayCostByIds(Long[] ids)
    {
        return kpiLumenPayCostMapper.deleteKpiLumenPayCostByIds(ids);
    }

    /**
     * 删除lumen使用信息
     * 
     * @param id lumen使用主键
     * @return 结果
     */
    @Override
    public int deleteKpiLumenPayCostById(Long id)
    {
        return kpiLumenPayCostMapper.deleteKpiLumenPayCostById(id);
    }
}
