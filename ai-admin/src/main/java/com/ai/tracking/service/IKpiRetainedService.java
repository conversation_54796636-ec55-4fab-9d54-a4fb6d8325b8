package com.ai.tracking.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.tracking.domain.KpiRetained;

/**
 * kpi 数据追踪-留存Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-18
 */
public interface IKpiRetainedService extends IService<KpiRetained> {
    /**
     * 查询kpi 数据追踪-留存
     * 
     * @param id kpi 数据追踪-留存主键
     * @return kpi 数据追踪-留存
     */
    KpiRetained selectKpiRetainedById(Long id);

    /**
     * 查询kpi 数据追踪-留存列表
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return kpi 数据追踪-留存集合
     */
    List<KpiRetained> selectKpiRetainedList(KpiRetained kpiRetained);

    /**
     * 新增kpi 数据追踪-留存
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return 结果
     */
    int insertKpiRetained(KpiRetained kpiRetained);

    /**
     * 修改kpi 数据追踪-留存
     * 
     * @param kpiRetained kpi 数据追踪-留存
     * @return 结果
     */
    int updateKpiRetained(KpiRetained kpiRetained);

    /**
     * 批量删除kpi 数据追踪-留存
     * 
     * @param ids 需要删除的kpi 数据追踪-留存主键集合
     * @return 结果
     */
    int deleteKpiRetainedByIds(Long[] ids);

    /**
     * 删除kpi 数据追踪-留存信息
     * 
     * @param id kpi 数据追踪-留存主键
     * @return 结果
     */
    int deleteKpiRetainedById(Long id);
}
