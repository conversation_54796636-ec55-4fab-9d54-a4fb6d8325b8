package com.ai.version.service.impl;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.redis.RedisCachePiclumen;
import com.ai.constants.LogicParamsCons;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.version.mapper.GptVersionControlWebMapper;
import com.ai.version.domain.GptVersionControlWeb;
import com.ai.version.service.IGptVersionControlWebService;

import javax.annotation.Resource;

/**
 * web端版本管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
public class GptVersionControlWebServiceImpl extends ServiceImpl<GptVersionControlWebMapper, GptVersionControlWeb> implements IGptVersionControlWebService
{
    @Autowired
    private GptVersionControlWebMapper gptVersionControlWebMapper;

    @Resource
    private RedisCachePiclumen redisCachePiclumen;

    /**
     * 查询web端版本管理
     * 
     * @param id web端版本管理主键
     * @return web端版本管理
     */
    @Override
    public GptVersionControlWeb selectGptVersionControlWebById(Long id)
    {
        return gptVersionControlWebMapper.selectGptVersionControlWebById(id);
    }

    /**
     * 查询web端版本管理列表
     * 
     * @param gptVersionControlWeb web端版本管理
     * @return web端版本管理
     */
    @Override
    public List<GptVersionControlWeb> selectGptVersionControlWebList(GptVersionControlWeb gptVersionControlWeb)
    {
        return gptVersionControlWebMapper.selectGptVersionControlWebList(gptVersionControlWeb);
    }

    /**
     * 新增web端版本管理
     * 
     * @param gptVersionControlWeb web端版本管理
     * @return 结果
     */
    @Override
    public AjaxResult insertGptVersionControlWeb(GptVersionControlWeb gptVersionControlWeb)
    {
        String version = gptVersionControlWeb.getVersion();
        if (!version.matches("\\d+\\.\\d+\\.\\d+\\.\\d+")) {
            return AjaxResult.error("版本号格式不正确");
        }

        Long aLong = gptVersionControlWebMapper.selectCount(new LambdaQueryWrapper<GptVersionControlWeb>()
                .eq(GptVersionControlWeb::getVersion, version));
        if (aLong >= 1) {
            return AjaxResult.error("该版本号已存在!");
        }

        gptVersionControlWeb.setCreateTime(LocalDateTime.now());
        gptVersionControlWeb.setIsCurrent(0);

        return AjaxResult.success(gptVersionControlWebMapper.insertGptVersionControlWeb(gptVersionControlWeb));
    }

    /**
     * 修改web端版本管理
     * 
     * @param gptVersionControlWeb web端版本管理
     * @return 结果
     */
    @Override
    public AjaxResult updateGptVersionControlWeb(GptVersionControlWeb gptVersionControlWeb)
    {   
        if (gptVersionControlWeb.getIsCurrent().equals(1)){
        //切换版本号时，将当前版本号置为0
            GptVersionControlWeb gptVersionControlWeb1 = gptVersionControlWebMapper.selectOne(new LambdaQueryWrapper<GptVersionControlWeb>()
                    .eq(GptVersionControlWeb::getIsCurrent, 1));
            gptVersionControlWeb.setUpdateTime(LocalDateTime.now());
            if (!Objects.isNull(gptVersionControlWeb1)){
                gptVersionControlWeb1.setIsCurrent(0);
                gptVersionControlWebMapper.updateGptVersionControlWeb(gptVersionControlWeb1);
            }
            redisCachePiclumen.setCacheObject(LogicParamsCons.WEB_VERSION,gptVersionControlWeb.getVersion());
        } else {
            Long aLong = gptVersionControlWebMapper.selectCount(new LambdaQueryWrapper<GptVersionControlWeb>()
                    .eq(GptVersionControlWeb::getIsCurrent, 1)
                    .eq(GptVersionControlWeb::getId, gptVersionControlWeb.getId()));
            if (aLong >= 1){
                return AjaxResult.error("至少保留一个当前版本!");
            }
        }
        gptVersionControlWeb.setUpdateTime(LocalDateTime.now());
        return AjaxResult.success(gptVersionControlWebMapper.updateGptVersionControlWeb(gptVersionControlWeb));
    }

    /**
     * 批量删除web端版本管理
     * 
     * @param ids 需要删除的web端版本管理主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteGptVersionControlWebByIds(Long[] ids)
    {
        Long aLong = gptVersionControlWebMapper.selectCount(new LambdaQueryWrapper<GptVersionControlWeb>()
                .eq(GptVersionControlWeb::getIsCurrent, 1)
                .in(GptVersionControlWeb::getId, ids));
        if (aLong >= 1){
            return AjaxResult.error("当前版本不能删除!");
        }
        return AjaxResult.success(gptVersionControlWebMapper.deleteGptVersionControlWebByIds(ids));
    }

    /**
     * 删除web端版本管理信息
     * 
     * @param id web端版本管理主键
     * @return 结果
     */
    @Override
    public int deleteGptVersionControlWebById(Long id)
    {
        return gptVersionControlWebMapper.deleteGptVersionControlWebById(id);
    }
}
