package com.ai.version.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.version.domain.GptVersionControlAndroid;
import com.ai.version.service.IGptVersionControlAndroidService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 安卓端版本管理Controller
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@RestController
@Api(value = "安卓端版本管理控制器", tags = {"安卓端版本管理管理"})
@RequestMapping("/version/android")
public class GptVersionControlAndroidController extends BaseController {
    @Autowired
    private IGptVersionControlAndroidService gptVersionControlAndroidService;

    /**
     * 查询安卓端版本管理列表
     */
    @PreAuthorize("@ss.hasPermi('version:android:list')")
    @ApiOperation("查询安卓端版本管理列表")
    @GetMapping("/list")
    public TableDataInfo list(GptVersionControlAndroid gptVersionControlAndroid) {
        startPage();
        List<GptVersionControlAndroid> list = gptVersionControlAndroidService.selectGptVersionControlAndroidList(gptVersionControlAndroid);
        return getDataTable(list);
    }

    /**
     * 导出安卓端版本管理列表
     */
    @ApiOperation("导出安卓端版本管理列表")
    @PreAuthorize("@ss.hasPermi('version:android:export')")
    @Log(title = "安卓端版本管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GptVersionControlAndroid gptVersionControlAndroid) {
        List<GptVersionControlAndroid> list = gptVersionControlAndroidService.selectGptVersionControlAndroidList(gptVersionControlAndroid);
        ExcelUtil<GptVersionControlAndroid> util = new ExcelUtil<GptVersionControlAndroid>(GptVersionControlAndroid.class);
        util.exportExcel(response, list, "安卓端版本管理数据");
    }

    /**
     * 获取安卓端版本管理详细信息
     */
    @ApiOperation("获取安卓端版本管理详细信息")
    @PreAuthorize("@ss.hasPermi('version:android:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gptVersionControlAndroidService.selectGptVersionControlAndroidById(id));
    }

    /**
     * 新增安卓端版本管理
     */
    @ApiOperation("新增安卓端版本管理")
    @PreAuthorize("@ss.hasPermi('version:android:add')")
    @Log(title = "安卓端版本管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GptVersionControlAndroid gptVersionControlAndroid) {
        return gptVersionControlAndroidService.insertGptVersionControlAndroid(gptVersionControlAndroid);
    }

    /**
     * 修改安卓端版本管理
     */
    @ApiOperation("修改安卓端版本管理")
    @PreAuthorize("@ss.hasPermi('version:android:edit')")
    @Log(title = "安卓端版本管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GptVersionControlAndroid gptVersionControlAndroid) {
        return gptVersionControlAndroidService.updateGptVersionControlAndroid(gptVersionControlAndroid);
    }

    /**
     * 删除安卓端版本管理
     */
    @ApiOperation("删除安卓端版本管理")
    @PreAuthorize("@ss.hasPermi('version:android:remove')")
    @Log(title = "安卓端版本管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return gptVersionControlAndroidService.deleteGptVersionControlAndroidByIds(ids);
    }
}
