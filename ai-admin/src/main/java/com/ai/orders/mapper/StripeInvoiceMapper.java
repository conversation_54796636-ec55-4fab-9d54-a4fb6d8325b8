package com.ai.orders.mapper;

import java.util.List;

import com.ai.orders.domain.vo.InvoiceAmountCountVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.StripeInvoice;
import org.apache.ibatis.annotations.Param;

/**
 * stripe账单记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface StripeInvoiceMapper extends BaseMapper<StripeInvoice> {
    /**
     * 查询stripe账单记录
     * 
     * @param id stripe账单记录主键
     * @return stripe账单记录
     */
    public StripeInvoice selectStripeInvoiceById(Long id);

    /**
     * 查询stripe账单记录列表
     * 
     * @param stripeInvoice stripe账单记录
     * @return stripe账单记录集合
     */
    public List<StripeInvoice> selectStripeInvoiceList(StripeInvoice stripeInvoice);

    /**
     * 新增stripe账单记录
     * 
     * @param stripeInvoice stripe账单记录
     * @return 结果
     */
    public int insertStripeInvoice(StripeInvoice stripeInvoice);

    /**
     * 修改stripe账单记录
     * 
     * @param stripeInvoice stripe账单记录
     * @return 结果
     */
    public int updateStripeInvoice(StripeInvoice stripeInvoice);

    /**
     * 删除stripe账单记录
     * 
     * @param id stripe账单记录主键
     * @return 结果
     */
    public int deleteStripeInvoiceById(Long id);

    /**
     * 批量删除stripe账单记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStripeInvoiceByIds(Long[] ids);


    List<InvoiceAmountCountVo> getInvoiceAmountStatistics(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
