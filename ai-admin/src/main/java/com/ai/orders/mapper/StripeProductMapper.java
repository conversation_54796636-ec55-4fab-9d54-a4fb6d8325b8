package com.ai.orders.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.orders.domain.StripeProduct;

/**
 * 存储 Stripe 商品和价格信息的Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface StripeProductMapper extends BaseMapper<StripeProduct> {
    /**
     * 查询存储 Stripe 商品和价格信息的
     * 
     * @param id 存储 Stripe 商品和价格信息的主键
     * @return 存储 Stripe 商品和价格信息的
     */
    public StripeProduct selectStripeProductById(Long id);

    /**
     * 查询存储 Stripe 商品和价格信息的列表
     * 
     * @param stripeProduct 存储 Stripe 商品和价格信息的
     * @return 存储 Stripe 商品和价格信息的集合
     */
    public List<StripeProduct> selectStripeProductList(StripeProduct stripeProduct);

    /**
     * 新增存储 Stripe 商品和价格信息的
     * 
     * @param stripeProduct 存储 Stripe 商品和价格信息的
     * @return 结果
     */
    public int insertStripeProduct(StripeProduct stripeProduct);

    /**
     * 修改存储 Stripe 商品和价格信息的
     * 
     * @param stripeProduct 存储 Stripe 商品和价格信息的
     * @return 结果
     */
    public int updateStripeProduct(StripeProduct stripeProduct);

    /**
     * 删除存储 Stripe 商品和价格信息的
     * 
     * @param id 存储 Stripe 商品和价格信息的主键
     * @return 结果
     */
    public int deleteStripeProductById(Long id);

    /**
     * 批量删除存储 Stripe 商品和价格信息的
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStripeProductByIds(Long[] ids);
}
