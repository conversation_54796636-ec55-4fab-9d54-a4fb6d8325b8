package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.StripeScheduleLog;
import com.ai.orders.service.IStripeScheduleLogService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * stripe预定阅记录日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@Api(value = "stripe预定阅记录日志控制器", tags = {"stripe预定阅记录日志管理"})
@RequestMapping("/orders/scheduleLog")
public class StripeScheduleLogController extends BaseController {
    @Autowired
    private IStripeScheduleLogService stripeScheduleLogService;

    /**
     * 查询stripe预定阅记录日志列表
     */
    @PreAuthorize("@ss.hasPermi('orders:scheduleLog:list')")
    @ApiOperation("查询stripe预定阅记录日志列表")
    @GetMapping("/list")
    public TableDataInfo list(StripeScheduleLog stripeScheduleLog) {
        startPage();
        List<StripeScheduleLog> list = stripeScheduleLogService.selectStripeScheduleLogList(stripeScheduleLog);
        return getDataTable(list);
    }

    /**
     * 导出stripe预定阅记录日志列表
     */
    @ApiOperation("导出stripe预定阅记录日志列表")
    @PreAuthorize("@ss.hasPermi('orders:scheduleLog:export')")
    @Log(title = "stripe预定阅记录日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StripeScheduleLog stripeScheduleLog) {
        List<StripeScheduleLog> list = stripeScheduleLogService.selectStripeScheduleLogList(stripeScheduleLog);
        ExcelUtil<StripeScheduleLog> util = new ExcelUtil<StripeScheduleLog>(StripeScheduleLog.class);
        util.exportExcel(response, list, "stripe预定阅记录日志数据");
    }

    /**
     * 获取stripe预定阅记录日志详细信息
     */
    @ApiOperation("获取stripe预定阅记录日志详细信息")
    @PreAuthorize("@ss.hasPermi('orders:scheduleLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(stripeScheduleLogService.selectStripeScheduleLogById(id));
    }

    /**
     * 新增stripe预定阅记录日志
     */
    @ApiOperation("新增stripe预定阅记录日志")
    @PreAuthorize("@ss.hasPermi('orders:scheduleLog:add')")
    @Log(title = "stripe预定阅记录日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StripeScheduleLog stripeScheduleLog) {
        return toAjax(stripeScheduleLogService.insertStripeScheduleLog(stripeScheduleLog));
    }

    /**
     * 修改stripe预定阅记录日志
     */
    @ApiOperation("修改stripe预定阅记录日志")
    @PreAuthorize("@ss.hasPermi('orders:scheduleLog:edit')")
    @Log(title = "stripe预定阅记录日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StripeScheduleLog stripeScheduleLog) {
        return toAjax(stripeScheduleLogService.updateStripeScheduleLog(stripeScheduleLog));
    }

    /**
     * 删除stripe预定阅记录日志
     */
    @ApiOperation("删除stripe预定阅记录日志")
    @PreAuthorize("@ss.hasPermi('orders:scheduleLog:remove')")
    @Log(title = "stripe预定阅记录日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(stripeScheduleLogService.deleteStripeScheduleLogByIds(ids));
    }
}
