package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.PayAppleProduct;
import com.ai.orders.service.IPayAppleProductService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 存储 apple 商品和价格信息的Controller
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@Api(value = "存储 apple 商品和价格信息的控制器", tags = {"存储 apple 商品和价格信息的管理"})
@RequestMapping("/orders/appleProduct")
public class  PayAppleProductController extends BaseController {
    @Autowired
    private IPayAppleProductService payAppleProductService;

    /**
     * 查询存储 apple 商品和价格信息的列表
     */
    @PreAuthorize("@ss.hasPermi('orders:appleProduct:list')")
    @ApiOperation("查询存储 apple 商品和价格信息的列表")
    @GetMapping("/list")
    public TableDataInfo list(PayAppleProduct payAppleProduct) {
        startPage();
        List<PayAppleProduct> list = payAppleProductService.selectPayAppleProductList(payAppleProduct);
        return getDataTable(list);
    }

    /**
     * 导出存储 apple 商品和价格信息的列表
     */
    @ApiOperation("导出存储 apple 商品和价格信息的列表")
    @PreAuthorize("@ss.hasPermi('orders:appleProduct:export')")
    @Log(title = "存储 apple 商品和价格信息的", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PayAppleProduct payAppleProduct) {
        List<PayAppleProduct> list = payAppleProductService.selectPayAppleProductList(payAppleProduct);
        ExcelUtil<PayAppleProduct> util = new ExcelUtil<PayAppleProduct>(PayAppleProduct.class);
        util.exportExcel(response, list, "存储 apple 商品和价格信息的数据");
    }

    /**
     * 获取存储 apple 商品和价格信息的详细信息
     */
    @ApiOperation("获取存储 apple 商品和价格信息的详细信息")
    @PreAuthorize("@ss.hasPermi('orders:appleProduct:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(payAppleProductService.selectPayAppleProductById(id));
    }

    /**
     * 新增存储 apple 商品和价格信息的
     */
    @ApiOperation("新增存储 apple 商品和价格信息的")
    @PreAuthorize("@ss.hasPermi('orders:appleProduct:add')")
    @Log(title = "存储 apple 商品和价格信息的", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PayAppleProduct payAppleProduct) {
        return toAjax(payAppleProductService.insertPayAppleProduct(payAppleProduct));
    }

    /**
     * 修改存储 apple 商品和价格信息的
     */
    @ApiOperation("修改存储 apple 商品和价格信息的")
    @PreAuthorize("@ss.hasPermi('orders:appleProduct:edit')")
    @Log(title = "存储 apple 商品和价格信息的", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PayAppleProduct payAppleProduct) {
        return toAjax(payAppleProductService.updatePayAppleProduct(payAppleProduct));
    }

    /**
     * 删除存储 apple 商品和价格信息的
     */
    @ApiOperation("删除存储 apple 商品和价格信息的")
    @PreAuthorize("@ss.hasPermi('orders:appleProduct:remove')")
    @Log(title = "存储 apple 商品和价格信息的", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(payAppleProductService.deletePayAppleProductByIds(ids));
    }
}
