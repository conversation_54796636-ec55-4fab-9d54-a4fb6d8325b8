package com.ai.orders.controller;

import java.util.List;

import com.ai.common.core.domain.model.LoginUser;
import com.ai.orders.domain.dto.SubscriptionCurrentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.SubscriptionCurrent;
import com.ai.orders.service.ISubscriptionCurrentService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 当前订阅Controller
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
@RestController
@Api(value = "当前订阅控制器", tags = {"当前订阅管理"})
@RequestMapping("/orders/current")
public class SubscriptionCurrentController extends BaseController {
    @Autowired
    private ISubscriptionCurrentService subscriptionCurrentService;

    /**
     * 查询当前订阅列表
     */
    @PreAuthorize("@ss.hasPermi('orders:current:list')")
    @ApiOperation("查询当前订阅列表")
    @GetMapping("/list")
    public TableDataInfo list(SubscriptionCurrent subscriptionCurrent) {
        startPage();
        List<SubscriptionCurrent> list = subscriptionCurrentService.selectSubscriptionCurrentList(subscriptionCurrent);
        return getDataTable(list);
    }

    /**
     * 导出当前订阅列表
     */
    @ApiOperation("导出当前订阅列表")
    @PreAuthorize("@ss.hasPermi('orders:current:export')")
    @Log(title = "当前订阅", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SubscriptionCurrent subscriptionCurrent) {
        List<SubscriptionCurrent> list = subscriptionCurrentService.selectSubscriptionCurrentList(subscriptionCurrent);
        ExcelUtil<SubscriptionCurrent> util = new ExcelUtil<SubscriptionCurrent>(SubscriptionCurrent.class);
        util.exportExcel(response, list, "当前订阅数据");
    }

    /**
     * 获取当前订阅详细信息
     */
    @ApiOperation("获取当前订阅详细信息")
    @PreAuthorize("@ss.hasPermi('orders:current:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(subscriptionCurrentService.selectSubscriptionCurrentById(id));
    }

    /**
     * 新增当前订阅
     */
    @ApiOperation("新增当前订阅")
    @PreAuthorize("@ss.hasPermi('orders:current:add')")
    @Log(title = "当前订阅", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SubscriptionCurrentDto subscriptionCurrent) {
        LoginUser loginUser = getLoginUser();
        return subscriptionCurrentService.insertSubscriptionCurrent(subscriptionCurrent,loginUser);
    }

    /**
     * 修改当前订阅
     */
    @ApiOperation("修改当前订阅")
    @PreAuthorize("@ss.hasPermi('orders:current:edit')")
    @Log(title = "当前订阅", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SubscriptionCurrent subscriptionCurrent) {
        return toAjax(subscriptionCurrentService.updateSubscriptionCurrent(subscriptionCurrent));
    }

    /**
     * 删除当前订阅
     */
    @ApiOperation("删除当前订阅")
    @PreAuthorize("@ss.hasPermi('orders:current:remove')")
    @Log(title = "当前订阅", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(subscriptionCurrentService.deleteSubscriptionCurrentByIds(ids));
    }

    /**
     * 删除当前订阅
     */
    @ApiOperation("逻辑删除当前订阅(单条)")
    @PreAuthorize("@ss.hasPermi('orders:current:remove')")
    @Log(title = "逻辑删除当前订阅(单条)", businessType = BusinessType.DELETE)
    @DeleteMapping("removeOne/{id}")
    public AjaxResult removeOne(@PathVariable("id") Long id) {
        LoginUser loginUser = getLoginUser();
        return toAjax(subscriptionCurrentService.deleteSubscriptionCurrentById(id,loginUser));
    }

    /**
     * 设置会员为无效
     */
    @ApiOperation("设置会员为无效")
    @PreAuthorize("@ss.hasPermi('orders:current:edit')")
    @Log(title = "设置会员为无效", businessType = BusinessType.UPDATE)
    @GetMapping("set-invalid")
    public AjaxResult setInvalid(@RequestParam Long id) {
        LoginUser loginUser = getLoginUser();
        return subscriptionCurrentService.setInvalid(id,loginUser);
    }
}
