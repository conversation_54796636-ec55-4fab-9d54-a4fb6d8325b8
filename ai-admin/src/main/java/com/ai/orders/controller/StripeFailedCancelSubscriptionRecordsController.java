package com.ai.orders.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ai.common.annotation.Log;
import com.ai.common.core.controller.BaseController;
import com.ai.common.core.domain.AjaxResult;
import com.ai.common.enums.BusinessType;
import com.ai.orders.domain.StripeFailedCancelSubscriptionRecords;
import com.ai.orders.service.IStripeFailedCancelSubscriptionRecordsService;
import com.ai.common.utils.poi.ExcelUtil;
import com.ai.common.core.page.TableDataInfo;

/**
 * 升级订阅时取消旧订阅失败记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@Api(value = "升级订阅时取消旧订阅失败记录控制器", tags = {"升级订阅时取消旧订阅失败记录管理"})
@RequestMapping("/orders/cancelRecords")
public class StripeFailedCancelSubscriptionRecordsController extends BaseController {
    @Autowired
    private IStripeFailedCancelSubscriptionRecordsService stripeFailedCancelSubscriptionRecordsService;

    /**
     * 查询升级订阅时取消旧订阅失败记录列表
     */
    @PreAuthorize("@ss.hasPermi('orders:cancelRecords:list')")
    @ApiOperation("查询升级订阅时取消旧订阅失败记录列表")
    @GetMapping("/list")
    public TableDataInfo list(StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords) {
        startPage();
        List<StripeFailedCancelSubscriptionRecords> list = stripeFailedCancelSubscriptionRecordsService.selectStripeFailedCancelSubscriptionRecordsList(stripeFailedCancelSubscriptionRecords);
        return getDataTable(list);
    }

    /**
     * 导出升级订阅时取消旧订阅失败记录列表
     */
    @ApiOperation("导出升级订阅时取消旧订阅失败记录列表")
    @PreAuthorize("@ss.hasPermi('orders:cancelRecords:export')")
    @Log(title = "升级订阅时取消旧订阅失败记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords) {
        List<StripeFailedCancelSubscriptionRecords> list = stripeFailedCancelSubscriptionRecordsService.selectStripeFailedCancelSubscriptionRecordsList(stripeFailedCancelSubscriptionRecords);
        ExcelUtil<StripeFailedCancelSubscriptionRecords> util = new ExcelUtil<StripeFailedCancelSubscriptionRecords>(StripeFailedCancelSubscriptionRecords.class);
        util.exportExcel(response, list, "升级订阅时取消旧订阅失败记录数据");
    }

    /**
     * 获取升级订阅时取消旧订阅失败记录详细信息
     */
    @ApiOperation("获取升级订阅时取消旧订阅失败记录详细信息")
    @PreAuthorize("@ss.hasPermi('orders:cancelRecords:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(stripeFailedCancelSubscriptionRecordsService.selectStripeFailedCancelSubscriptionRecordsById(id));
    }

    /**
     * 新增升级订阅时取消旧订阅失败记录
     */
    @ApiOperation("新增升级订阅时取消旧订阅失败记录")
    @PreAuthorize("@ss.hasPermi('orders:cancelRecords:add')")
    @Log(title = "升级订阅时取消旧订阅失败记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords) {
        return toAjax(stripeFailedCancelSubscriptionRecordsService.insertStripeFailedCancelSubscriptionRecords(stripeFailedCancelSubscriptionRecords));
    }

    /**
     * 修改升级订阅时取消旧订阅失败记录
     */
    @ApiOperation("修改升级订阅时取消旧订阅失败记录")
    @PreAuthorize("@ss.hasPermi('orders:cancelRecords:edit')")
    @Log(title = "升级订阅时取消旧订阅失败记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StripeFailedCancelSubscriptionRecords stripeFailedCancelSubscriptionRecords) {
        return toAjax(stripeFailedCancelSubscriptionRecordsService.updateStripeFailedCancelSubscriptionRecords(stripeFailedCancelSubscriptionRecords));
    }

    /**
     * 删除升级订阅时取消旧订阅失败记录
     */
    @ApiOperation("删除升级订阅时取消旧订阅失败记录")
    @PreAuthorize("@ss.hasPermi('orders:cancelRecords:remove')")
    @Log(title = "升级订阅时取消旧订阅失败记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(stripeFailedCancelSubscriptionRecordsService.deleteStripeFailedCancelSubscriptionRecordsByIds(ids));
    }
}
