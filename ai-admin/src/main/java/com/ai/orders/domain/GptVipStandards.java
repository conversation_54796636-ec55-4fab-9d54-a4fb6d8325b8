package com.ai.orders.domain;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * 会员资源标准对象 gpt_vip_standards
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "gpt_vip_standards", description = "会员资源标准")
@TableName("gpt_vip_standards")
public class GptVipStandards extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 会员类型： basic 非会员 standard 普通会员 pro 高级会员  */
    @ApiModelProperty("会员类型： basic 非会员 standard 普通会员 pro 高级会员 ")
    @Excel(name = "会员类型： basic 非会员 standard 普通会员 pro 高级会员 ")
    private String vipType;

    /** 每日免费点数 */
    @ApiModelProperty("每日免费点数")
    @Excel(name = "每日免费点数")
    private Long dailyLumens;

    /** 会员点数 */
    @ApiModelProperty("会员点数")
    @Excel(name = "会员点数")
    private Long monthlyLumens;

    @ApiModelProperty( "收藏数标准容量")
    @Excel(name = "收藏数标准容量")
    private Integer collectNum;

    /** 历史页图片保留时长： 免费用户: 30 天  会员: 120 天 超级会员: never expire */
    @ApiModelProperty("历史页图片保留时长： 免费用户: 30 天  会员: 120 天 超级会员: never expire")
    @Excel(name = "历史页图片保留时长： 免费用户: 30 天  会员: 120 天 超级会员: never expire")
    private String creationHistory;

    /** 排队任务数: 免费用户:0 会员: 5 超级会员: 10 */
    @ApiModelProperty("排队任务数: 免费用户:0 会员: 5 超级会员: 10")
    @Excel(name = "排队任务数: 免费用户:0 会员: 5 超级会员: 10")
    private Long taskQueue;

    /** 并发任务数: 免费用户:0 会员: 2 超级会员: 5 */
    @ApiModelProperty("并发任务数: 免费用户:0 会员: 2 超级会员: 5")
    @Excel(name = "并发任务数: 免费用户:0 会员: 2 超级会员: 5")
    private Long concurrentJobs;

    /** 是否可以批量下载： 0 否 1 是 */
    @ApiModelProperty("是否可以批量下载： 0 否 1 是")
    @Excel(name = "是否可以批量下载： 0 否 1 是")
    private Boolean batchDownload;

    /** 批量生图数： 2 ： 非会员 4 ： 普通会员 4 ： 高级会员 */
    @ApiModelProperty("批量生图数： 2 ： 非会员 4 ： 普通会员 4 ： 高级会员")
    @Excel(name = "批量生图数： 2 ： 非会员 4 ： 普通会员 4 ： 高级会员")
    private Long imagesPerBatch;

    /** 是否可以超分： 0 否 1 是 */
    @ApiModelProperty("是否可以超分： 0 否 1 是")
    @Excel(name = "是否可以超分： 0 否 1 是")
    private Boolean upscale;

    /** 是否可以局部重绘： 0 否 1 是 */
    @ApiModelProperty("是否可以局部重绘： 0 否 1 是")
    @Excel(name = "是否可以局部重绘： 0 否 1 是")
    private Boolean inpaint;

    /** 是否可以局部扩图： 0 否 1 是 */
    @ApiModelProperty("是否可以局部扩图： 0 否 1 是")
    @Excel(name = "是否可以局部扩图： 0 否 1 是")
    private Boolean expand;

    /** 是否可以线稿上色： 0 否 1 是 */
    @ApiModelProperty("是否可以线稿上色： 0 否 1 是")
    @Excel(name = "是否可以线稿上色： 0 否 1 是")
    private Boolean colorize;

    /** 是否可以去背景： 0 否 1 是 */
    @ApiModelProperty("是否可以去背景： 0 否 1 是")
    @Excel(name = "是否可以去背景： 0 否 1 是")
    private Boolean removeBg;

    /** 是否展示历史页 */
    @ApiModelProperty("是否展示历史页")
    @Excel(name = "是否展示历史页")
    private Boolean historyExplore;

    /** 是否可以翻译： 0 否 1是 */
    @ApiModelProperty("是否可以翻译： 0 否 1是")
    @Excel(name = "是否可以翻译： 0 否 1是")
    private Boolean translation;

    /** 是否可以增强 */
    @ApiModelProperty("是否可以增强")
    @Excel(name = "是否可以增强")
    private Boolean enhance;

}
