package com.ai.orders.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * lummen购买记录对象 pay_logic_purchase_record
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "pay_logic_purchase_record", description = "lummen购买记录")
@TableName("pay_logic_purchase_record")
public class PayLogicPurchaseRecord extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** stripe customer */
    @ApiModelProperty("stripe customer")
    @Excel(name = "stripe customer")
    private String customerId;

    /** stripe price_id */
    @ApiModelProperty("stripe price_id")
    @Excel(name = "stripe price_id")
    private String priceId;

    @ApiModelProperty("lumen 到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long currentPeriodEnd;

    @ApiModelProperty("lumen 获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long currentPeriodStart;

    @ApiModelProperty("逻辑订阅每周期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long logicPeriodEnd;

    @ApiModelProperty("逻辑订阅每周期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Long logicPeriodStart;

    /** 支付ID */
    @ApiModelProperty("支付ID")
    @Excel(name = "支付ID")
    private String stripePaymentIntentId;

    /** lumen 总数 */
    @ApiModelProperty("lumen 总数")
    @Excel(name = "lumen 总数")
    private Long lumenQty;

    /** 购买数量 */
    @ApiModelProperty("购买数量")
    @Excel(name = "购买数量")
    private Long count;

    /** 购买金额 */
    @ApiModelProperty("购买金额")
    @Excel(name = "购买金额")
    private BigDecimal amount;

    @ApiModelProperty("订阅平台")
    @Excel(name = "订阅平台")
    @TableField("vip_plat_form")  // 显式指定数据库字段名
    private String vipPlatform;

    /** 订阅id(stripe) */
    @ApiModelProperty("订阅id(stripe)")
    @Excel(name = "订阅id(stripe)")
    private String subscriptionId;

    @TableField(exist = false)
    @Excel(name = "逻辑订阅每周期开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime logicPeriodStartTime;

    @TableField(exist = false)
    @Excel(name = "逻辑订阅每周期结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime logicPeriodEndTime;

    @TableField(exist = false)
    @Excel(name = "lumen 获得时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentPeriodStartTime;


    @TableField(exist = false)
    @Excel(name = "lumen 到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentPeriodEndTime;

}
