package com.ai.orders.domain;

import java.math.BigDecimal;
import com.ai.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.ai.common.core.domain.MyBaseEntity;

/**
 * stripe账单记录对象 stripe_invoice
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "stripe_invoice", description = "stripe账单记录")
@TableName("stripe_invoice")
public class StripeInvoice extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 用户id */
    @ApiModelProperty("用户id")
    @Excel(name = "用户id")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    @Excel(name = "用户账号")
    private String loginName;

    /** 订阅ID */
    @ApiModelProperty("订阅ID")
    @Excel(name = "订阅ID")
    private String subscriptionId;

    /** 支付 */
    @ApiModelProperty("支付")
    @Excel(name = "支付")
    private String paymentIntentId;

    /** 支付 */
    @ApiModelProperty("支付")
    @Excel(name = "支付")
    private String invoiceId;

    /** 支付原因 */
    @ApiModelProperty("支付原因")
    @Excel(name = "支付原因")
    private String billingReason;

    /** 币种 */
    @ApiModelProperty("币种")
    @Excel(name = "币种")
    private String currency;

    /** customer id */
    @ApiModelProperty("customer id")
    @Excel(name = "customer id")
    private String customerId;

    /** 应付金额 */
    @ApiModelProperty("应付金额")
    @Excel(name = "应付金额")
    private Long amountDue;

    /** 支付金额 */
    @ApiModelProperty("支付金额")
    @Excel(name = "支付金额")
    private Long amountPaid;

    /** 剩余金额 */
    @ApiModelProperty("剩余金额")
    @Excel(name = "剩余金额")
    private Long amountRemaining;

    /**  */
    @ApiModelProperty("")
    @Excel(name = "")
    private Long amountExcludingTax;

    /** 金额(排除税) */
    @ApiModelProperty("金额(排除税)")
    @Excel(name = "金额(排除税)")
    private String totalExcludingTax;

    /** 总价 */
    @ApiModelProperty("总价")
    @Excel(name = "总价")
    private BigDecimal total;

    /** 状态 */
    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private String status;


    /** stripe 托管发票链接 */
    @ApiModelProperty("stripe 托管发票链接")
//    @Excel(name = "stripe 托管发票链接")
    private String hostedInvoiceUrl;

    /** 发票pdf */
    @ApiModelProperty("发票pdf")
//    @Excel(name = "发票pdf")
    private String invoicePdf;

}
