package com.ai.orders.service;

import java.util.List;

import com.ai.common.core.domain.AjaxResult;
import com.ai.common.core.domain.model.LoginUser;
import com.ai.common.core.page.TableDataInfo;
import com.ai.orders.domain.SysLumenAdminOperateLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.PayLumenRecord;
import org.springframework.transaction.annotation.Transactional;

/**
 * lumen实时记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface IPayLumenRecordService extends IService<PayLumenRecord> {
    /**
     * 查询lumen实时记录
     * 
     * @param id lumen实时记录主键
     * @return lumen实时记录
     */
    PayLumenRecord selectPayLumenRecordById(Long id);

    /**
     * 查询lumen实时记录列表
     * 
     * @param payLumenRecord lumen实时记录
     * @return lumen实时记录集合
     */
    public List<PayLumenRecord> selectPayLumenRecordList(PayLumenRecord payLumenRecord);

    /**
     * 新增lumen实时记录
     * 
     * @param payLumenRecord lumen实时记录
     * @return 结果
     */
    public int insertPayLumenRecord(PayLumenRecord payLumenRecord,LoginUser loginUser);

    /**
     * 修改lumen实时记录
     * 
     * @param payLumenRecord lumen实时记录
     * @return 结果
     */
    public int updatePayLumenRecord(PayLumenRecord payLumenRecord);

    /**
     * 批量删除lumen实时记录
     * 
     * @param ids 需要删除的lumen实时记录主键集合
     * @return 结果
     */
    public int deletePayLumenRecordByIds(Long[] ids);

    /**
     * 删除lumen实时记录信息
     * 
     * @param id lumen实时记录主键
     * @return 结果
     */
    public int deletePayLumenRecordById(Long id);

    /**
     * 减少lumen
     *
     * @param lumen 减少lumen量
     * @param id lumen实时记录主键
     * @return 结果
     */
    AjaxResult reduceLumen(Long id, Integer lumen, LoginUser loginUser);

    /**
     * 设置无效
     *
     * @param id lumen实时记录主键
     * @return 结果
     */
     AjaxResult setLumenInvalid(Long id, LoginUser loginUser);

    /**
     * 赠送lumen
     *
     * @param id lumen实时记录主键
     * @return 结果
     */
    AjaxResult getGiftLumen(Long id,Long lumen, LoginUser loginUser);

    /**
     * 查看lumen 操作日志
     *
     * @param lumenRecordId lumen实时记录主键
     * @return 结果
     */
    List<SysLumenAdminOperateLog> getLumenAdminOperateLogList(Long lumenRecordId);

    /**
     * 查看lumen 消费日志
     *
     * @param loginName 用户LoginName
     * @return 结果
     */
    TableDataInfo getLumenCosts(Integer pageSize,
                                String markFileId, Boolean isNext, String loginName);



    /**
     * 删除redis 记录
     *
     * @param loginName 用户LoginName
     * @return 结果
     */
    void resettingPersonalLumens(String loginName);

}
