package com.ai.orders.service.impl;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ai.orders.mapper.StripeScheduleRecordMapper;
import com.ai.orders.domain.StripeScheduleRecord;
import com.ai.orders.service.IStripeScheduleRecordService;

/**
 * stripe 预定阅记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class StripeScheduleRecordServiceImpl extends ServiceImpl<StripeScheduleRecordMapper, StripeScheduleRecord> implements IStripeScheduleRecordService
{
    @Autowired
    private StripeScheduleRecordMapper stripeScheduleRecordMapper;

    /**
     * 查询stripe 预定阅记录
     * 
     * @param id stripe 预定阅记录主键
     * @return stripe 预定阅记录
     */
    @Override
    public StripeScheduleRecord selectStripeScheduleRecordById(Long id)
    {
        return stripeScheduleRecordMapper.selectStripeScheduleRecordById(id);
    }

    /**
     * 查询stripe 预定阅记录列表
     * 
     * @param stripeScheduleRecord stripe 预定阅记录
     * @return stripe 预定阅记录
     */
    @Override
    public List<StripeScheduleRecord> selectStripeScheduleRecordList(StripeScheduleRecord stripeScheduleRecord)
    {
        Map<String, Object> params = stripeScheduleRecord.getParams();
        if (params != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 转换时间字符串为 Instant 类型的时间戳
            params.put("beginStartDate", convertToEpochSecond((String) params.get("beginStartDate"), formatter));
            params.put("endStartDate", convertToEpochSecond((String) params.get("endStartDate"), formatter));

            // 更新 params
            stripeScheduleRecord.setParams(params);
        }
        return stripeScheduleRecordMapper.selectStripeScheduleRecordList(stripeScheduleRecord);
    }

    /**
     * 新增stripe 预定阅记录
     * 
     * @param stripeScheduleRecord stripe 预定阅记录
     * @return 结果
     */
    @Override
    public int insertStripeScheduleRecord(StripeScheduleRecord stripeScheduleRecord)
    {
        stripeScheduleRecord.setCreateTime(LocalDateTime.now());
        return stripeScheduleRecordMapper.insertStripeScheduleRecord(stripeScheduleRecord);
    }

    /**
     * 修改stripe 预定阅记录
     * 
     * @param stripeScheduleRecord stripe 预定阅记录
     * @return 结果
     */
    @Override
    public int updateStripeScheduleRecord(StripeScheduleRecord stripeScheduleRecord)
    {
        stripeScheduleRecord.setUpdateTime(LocalDateTime.now());
        return stripeScheduleRecordMapper.updateStripeScheduleRecord(stripeScheduleRecord);
    }

    /**
     * 批量删除stripe 预定阅记录
     * 
     * @param ids 需要删除的stripe 预定阅记录主键
     * @return 结果
     */
    @Override
    public int deleteStripeScheduleRecordByIds(Long[] ids)
    {
        return stripeScheduleRecordMapper.deleteStripeScheduleRecordByIds(ids);
    }

    /**
     * 删除stripe 预定阅记录信息
     * 
     * @param id stripe 预定阅记录主键
     * @return 结果
     */
    @Override
    public int deleteStripeScheduleRecordById(Long id)
    {
        return stripeScheduleRecordMapper.deleteStripeScheduleRecordById(id);
    }

    private long convertToEpochSecond(String timeStr, DateTimeFormatter formatter) {
        if (timeStr == null || timeStr.isEmpty()) {
            return 0; // 或者根据业务需求返回默认值
        }
        LocalDateTime dateTime = LocalDateTime.parse(timeStr, formatter);
        return dateTime.atZone(ZoneId.of("Asia/Shanghai")) // 转换为北京时间（UTC+8）
                .toInstant() // 转为 Instant
                .getEpochSecond(); // 获取 epoch 秒数
    }
}
