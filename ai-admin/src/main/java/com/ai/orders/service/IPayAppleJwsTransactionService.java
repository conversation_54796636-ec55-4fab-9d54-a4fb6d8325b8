package com.ai.orders.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ai.orders.domain.PayAppleJwsTransaction;

/**
 * 苹果支付JWS交易记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface IPayAppleJwsTransactionService extends IService<PayAppleJwsTransaction> {
    /**
     * 查询苹果支付JWS交易记录
     * 
     * @param id 苹果支付JWS交易记录主键
     * @return 苹果支付JWS交易记录
     */
    public PayAppleJwsTransaction selectPayAppleJwsTransactionById(Long id);

    /**
     * 查询苹果支付JWS交易记录列表
     * 
     * @param payAppleJwsTransaction 苹果支付JWS交易记录
     * @return 苹果支付JWS交易记录集合
     */
    public List<PayAppleJwsTransaction> selectPayAppleJwsTransactionList(PayAppleJwsTransaction payAppleJwsTransaction);

    /**
     * 新增苹果支付JWS交易记录
     * 
     * @param payAppleJwsTransaction 苹果支付JWS交易记录
     * @return 结果
     */
    public int insertPayAppleJwsTransaction(PayAppleJwsTransaction payAppleJwsTransaction);

    /**
     * 修改苹果支付JWS交易记录
     * 
     * @param payAppleJwsTransaction 苹果支付JWS交易记录
     * @return 结果
     */
    public int updatePayAppleJwsTransaction(PayAppleJwsTransaction payAppleJwsTransaction);

    /**
     * 批量删除苹果支付JWS交易记录
     * 
     * @param ids 需要删除的苹果支付JWS交易记录主键集合
     * @return 结果
     */
    public int deletePayAppleJwsTransactionByIds(Long[] ids);

    /**
     * 删除苹果支付JWS交易记录信息
     * 
     * @param id 苹果支付JWS交易记录主键
     * @return 结果
     */
    public int deletePayAppleJwsTransactionById(Long id);
}
