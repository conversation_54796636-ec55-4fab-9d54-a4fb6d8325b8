package com.ai.comfy.service;

import com.ai.common.core.domain.AjaxResult;
import com.ai.task.domain.GptLbInstance;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@ResponseBody
public interface IComfyConfigService {

    /**
     * 上架comfy节点
     *
     * @param instance eg:
     *                 {
     *                 "address": "127.0.0.1:9999"
     *                 }
     * @param clientId
     * @return
     */
    AjaxResult addNew(GptLbInstance instance, String clientId);

    /**
     * 下架comfy节点
     *
     * @param instance eg: {
     *                 "address": "127.0.0.1:9999"
     *                 }
     * @return
     */
    AjaxResult remove(GptLbInstance instance);

    /**
     *
     * @param instance eg: {
     *   "address": "127.0.0.1:9999"
     * }
     * @return  eg: {
     *     "ip": "*************",
     *     "topicAddressMap": {
     *       "*************:7861": "tp_piclumen_test"
     *     },
     *     "clientIdMap": {
     *       "*************:7861": "piclumen-api-test-xr"
     *     }
     */
    AjaxResult list(GptLbInstance instance);

    /**
     * 查询comfy在线节点
     *
     * @param instance eg: {
     *                 "address": "127.0.0.1:9999"
     *                 }
     * @return
     */
    AjaxResult queryOnline(GptLbInstance instance);

    AjaxResult queryReconnect(GptLbInstance instance);

    /**
     * 初始化接口
     * @return
     */
    AjaxResult init();

    AjaxResult queryTimeCount(String dateStr, String remoteIp);

    AjaxResult queryError(String promptId, String remoteIp);

    Boolean checkInstance(GptLbInstance instance);

    AjaxResult getErrorMessage(String promptId, String queryDate);

    AjaxResult initForIp(GptLbInstance instance);
}
