package com.ai.comfy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.comfy.domain.vo.ComfyStatus;
import com.ai.comfy.service.IComfyStatusService;
import com.ai.common.core.redis.RedisCacheForComFy;
import com.ai.email.service.EmailService;
import com.ai.framework.config.ComfyConfig;
import com.ai.task.domain.GptLbInstance;
import com.ai.task.domain.vo.IntanceRuleVo;
import com.ai.task.mapper.GptLbInstanceMapper;
import com.ai.task.service.IGptLbInstanceService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * Comfy 状态监控服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ComfyStatusServiceImpl implements IComfyStatusService {

    @Autowired
    private RedisCacheForComFy redisCacheForComFy;

    @Autowired
    private IGptLbInstanceService gptLbInstanceService;

    private static final String OK = "ok";
    private static final String FAIL = "fail";

    @Autowired
    private ComfyConfig serviceMonitorConfig;
    @Autowired
    private EmailService emailService;

    @Autowired
    private GptLbInstanceMapper gptLbInstanceMapper;

    @Autowired
    private  IGptLbInstanceService iGptLbInstanceService;
    private static final String COMFY_INSTANCE_MAP_KEY = "comfy-instance-map";
    private static final String COMFY_STATUS_KEY = "comfy-status-map";
    private static final String FAILED_COMFY_STATUS_KEY = "failed-comfy-status";

    private static final  String INTANCE_RULE_MAP =  "intance-rule-map" ;

//    private static
    @Resource(name = "threadPoolTaskExecutor")
    private AsyncTaskExecutor threadPoolTaskExecutor;


    @PostConstruct
    public void init() {
        log.info("comfy status monitor init");
        initInstanceToRedis();
    }

    private void initInstanceToRedis() {
        List<GptLbInstance> gptLbInstances = gptLbInstanceService.selectGptLbInstanceList(null);
        if (CollUtil.isEmpty(gptLbInstances)) {
            return;
        }
        Map<String, String> instnceMap = new HashMap<>();
        Map<String, ComfyStatus> instnceStatusMap = new HashMap<>();
        long stamp = System.currentTimeMillis();
        for (GptLbInstance gptLbInstance : gptLbInstances) {
            instnceMap.put(gptLbInstance.getAddress(), gptLbInstance.getMark());
            ComfyStatus comfyStatus = new ComfyStatus();
            comfyStatus.setStatus("down");
            comfyStatus.setInstanceName(gptLbInstance.getMark());
            comfyStatus.setQueueRemaining(0);
            comfyStatus.setRemoteAddress(gptLbInstance.getAddress());
            comfyStatus.setUpdatedTimestamp(stamp);
        }
        List<IntanceRuleVo> intanceRule = gptLbInstanceMapper.getIntanceRule();
        Map<String, String> intanceRuleMap = new HashMap<>();
        for (IntanceRuleVo intanceRuleVo : intanceRule) {
            intanceRuleMap.put(intanceRuleVo.getAddress(), intanceRuleVo.getRule());
        }

        // loadAiEasyServer(instnceMap, instnceStatusMap, stamp);
        redisCacheForComFy.deleteObject(COMFY_INSTANCE_MAP_KEY);
        redisCacheForComFy.deleteObject(COMFY_STATUS_KEY);
        redisCacheForComFy.deleteObject(INTANCE_RULE_MAP);
        redisCacheForComFy.setCacheMap(COMFY_INSTANCE_MAP_KEY, instnceMap);
        redisCacheForComFy.setCacheMap(COMFY_STATUS_KEY, instnceStatusMap);
        redisCacheForComFy.setCacheMap(INTANCE_RULE_MAP, intanceRuleMap);

    }

    /**
     * 对于已经失败的，每30s检测一次，如果响应ok，则直接上线
     * 失败 统计次数，大于max retry 则下线
     */
    @Override
    public void failComfyStatusCheck() {
        log.info("start failComfyStatusCheck");
        Map<String, FailComfyCheck> failMap = redisCacheForComFy.getCacheMap(FAILED_COMFY_STATUS_KEY);
        Set<String> keys = failMap.keySet();
        List<ComfyStatus> comfyStatusList = redisCacheForComFy.getMultiCacheMapValue(COMFY_STATUS_KEY, keys);
        if (CollUtil.isEmpty(comfyStatusList)) {
            log.info("end failComfyStatusCheck col is null");
            return;
        }
        for (ComfyStatus cs : comfyStatusList) {
            if (cs == null) {
                log.info("cs is null");
                continue;
            }
            String stat = cs.getStatus();
            String remoteAddress = cs.getRemoteAddress();
            if (FAIL.equalsIgnoreCase(stat)) {
                FailComfyCheck retryCount = failMap.getOrDefault(remoteAddress, new FailComfyCheck(1, false));
                if (retryCount.getCount() >= serviceMonitorConfig.getMaxRetry() && !retryCount.getDown()) {
                    downAndSendMail(cs);
                    retryCount.setDown(true);
                } else {
                    retryCount.setCount(retryCount.getCount() + 1);
                }
                redisCacheForComFy.setCacheMapValue(FAILED_COMFY_STATUS_KEY, remoteAddress, retryCount);
            }
            if (OK.equalsIgnoreCase(stat)) {
                upAndSendMail(cs);
                redisCacheForComFy.deleteCacheMapValue(FAILED_COMFY_STATUS_KEY, remoteAddress);
            }
        }
        log.info("end failComfyStatusCheck");
    }

    private void upAndSendMail(ComfyStatus cs) {
        // 需要判断有没有发生下线
        Map<String, Object> rtn = gptLbInstanceService.upInstanceAuto(cs.getRemoteAddress());
        boolean success = (boolean) rtn.get("success");
        log.info("{} : {} upAndSendMail: {}", cs.getInstanceName(), cs.getRemoteAddress(), success);
        threadPoolTaskExecutor.execute(() -> {
            String remoteAddress = cs.getRemoteAddress();
            String instanceName = cs.getInstanceName();
            if (success) {
                log.info("上架成功， " + instanceName + " : " + remoteAddress + " 上架成功！");
                emailService.sendNotifyMessage(instanceName + " : " + remoteAddress + " 上架成功！", "服务监控异常（上架提醒）");
            } else {
                log.warn("服务监控异常(上架失败)" + instanceName + " : " + remoteAddress + " 上架失败！--" + rtn.get("msg"));
            }
        });
    }

    private void downAndSendMail(ComfyStatus cs) {
        Map<String, Object> rtn = gptLbInstanceService.downInstanceAuto(cs.getRemoteAddress());
        boolean success = (boolean) rtn.get("success");
        log.info("{} : {} downAndSendMail: {}", cs.getInstanceName(), cs.getRemoteAddress(), success);
        threadPoolTaskExecutor.execute(() -> {
            String remoteAddress = cs.getRemoteAddress();
            String instanceName = cs.getInstanceName();
            if (success) {
                log.info("下架成功， " + instanceName + " : " + remoteAddress + "重试检测fail状态超过" + serviceMonitorConfig.getMaxRetry() + "次， 已触发下架！");
                emailService.sendNotifyMessage(instanceName + " : " + remoteAddress + "重试检测fail状态超过" + serviceMonitorConfig.getMaxRetry() + "次， 已触发下架！",
                        "服务监控异常（下架提醒）");
            } else {
                log.warn("服务监控异常(下架失败)" + instanceName + " : " + remoteAddress + "重试检测fail状态超过" + serviceMonitorConfig.getMaxRetry() + "次， 触发下架但是下架失败！--" + rtn.get(
                        "msg"));
            }
        });
    }

    /**
     * 每60s检查comfy状态
     */
    @Override
    public void checkComfyStatus() {
        log.debug("start checkComfyStatus..");
        Map<String, ComfyStatus> cacheMap = redisCacheForComFy.getCacheMap(COMFY_STATUS_KEY);
        if (CollUtil.isEmpty(cacheMap)) {
            return;
        }
        Collection<ComfyStatus> comfyStatus = cacheMap.values();
        final Map<String, FailComfyCheck> failMap = new HashMap<>();
        for (ComfyStatus cs : comfyStatus) {
            if (cs.getInstanceName() == null || StrUtil.containsIgnoreCase(cs.getInstanceName(), "aiease")) {
                continue;
            }
            String stat = cs.getStatus();
            String remoteAddress = cs.getRemoteAddress();
            Boolean cacheMapValue = redisCacheForComFy.hasHasKey(FAILED_COMFY_STATUS_KEY, remoteAddress);
            if (FAIL.equalsIgnoreCase(stat)) {
                if (cacheMapValue != null & Boolean.TRUE.equals(cacheMapValue)) {
                    continue;
                }
                FailComfyCheck failComfyCheck = new FailComfyCheck(1, false);
                failMap.putIfAbsent(remoteAddress, failComfyCheck);
            } else {
                if (cacheMapValue != null & Boolean.TRUE.equals(cacheMapValue)) {
                    redisCacheForComFy.deleteCacheMapValue(FAILED_COMFY_STATUS_KEY, remoteAddress);
                }
            }
        }
        if (!failMap.isEmpty()) {
            redisCacheForComFy.setCacheMap(FAILED_COMFY_STATUS_KEY, failMap);
        }
    }

    @Override
    public void resetInstance() {
        log.info("comfy status monitor reset Start");
        initInstanceToRedis();
        log.info("comfy status monitor reset end");
    }

    @Getter
    @Setter
    static class FailComfyCheck {
        private int count;
        private Boolean down;

        public FailComfyCheck(int count, boolean down) {
            this.count = count;
            this.down = down;
        }
    }

}
