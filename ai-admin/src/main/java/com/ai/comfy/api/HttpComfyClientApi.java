package com.ai.comfy.api;

import com.ai.common.utils.sign.Md5Utils;
import com.ai.framework.config.ComfyConfig;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

@Service
@Slf4j
public class HttpComfyClientApi {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ComfyConfig config;
    @Value("${spring.profiles.active}")
    private String activeProfile;


    /**
     * 发送带签名的动态请求
     *
     * @param ip           请求的 URL
     * @param responseType 响应的类型
     * @param <T>          响应类型
     * @return 响应实体
     */
    public <T> T sendRequest(
            String ip,
            String path,
            Map<String, String> urlParams,
            Class<T> responseType) {

        HttpEntity<Object> requestEntity = new HttpEntity<>(buildHeaders());
        // 构建带参数的 URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(String.format(config.getRemoteUrl(), ip, path));
        if (urlParams != null) {
            urlParams.forEach(uriBuilder::queryParam);
        }
        String finalUrl = uriBuilder.toUriString();
        // 发送请求
        ResponseEntity<T> exchange = restTemplate.exchange(finalUrl, HttpMethod.POST, requestEntity, responseType);
        return exchange.getBody();
    }

    public <T> T sendBodyRequest(
            String ip,
            String path,
            Object body,
            Class<T> responseType) {
        log.info("sendBodyRequest - {}", JSON.toJSONString(body));
        HttpEntity<Object> requestEntity = new HttpEntity<>(body, buildHeaders());
        // 构建带参数的 URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(String.format(config.getRemoteUrl(), ip, path));
        String finalUrl = uriBuilder.toUriString();
        // 发送请求
        log.info("sendBodyRequest - {}", finalUrl);
        ResponseEntity<T> exchange = restTemplate.exchange(finalUrl, HttpMethod.POST, requestEntity, responseType);
        return exchange.getBody();
    }

    // build headers
    public HttpHeaders buildHeaders() {
        // timestamp
        long timestamp = System.currentTimeMillis();
        // clientCode
        String clientCode = config.getClientCode();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("timestamp", String.valueOf(timestamp));
        httpHeaders.set("clientCode", clientCode);
        httpHeaders.set("sign", Md5Utils.hash(config.getClientSecret() + clientCode + timestamp + activeProfile)
                .toUpperCase());
        return httpHeaders;
    }
}
