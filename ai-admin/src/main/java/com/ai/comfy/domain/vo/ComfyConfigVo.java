package com.ai.comfy.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComfyConfigVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 本机Ip
     */
    private String ip;


    private List<ComfyServerInfo> serverAddress;
}
