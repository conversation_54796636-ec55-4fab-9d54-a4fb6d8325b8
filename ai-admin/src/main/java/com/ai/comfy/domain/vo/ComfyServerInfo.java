package com.ai.comfy.domain.vo;

import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ComfyServerInfo {
    public ComfyServerInfo() {
    }

    public ComfyServerInfo(String address, String serverId, String port, String topic, String clientId) {
        this.address = address;
        this.serverId = serverId;
        this.port = port;
        this.topic = topic;
        this.clientId = clientId;
    }

    /**
     * 服务器地址
     */
    private String address;
    /**
     * 服务器ID
     */
    private String serverId;
    /**
     * 服务器端口
     */
    private String port;
    /**
     * 服务器订阅主题
     */
    private String topic;
    /**
     * 服务器客户端ID
     */
    private String clientId;
}