package com.ai.comfy;

import com.ai.comfy.service.IComfyConfigService;
import com.ai.common.annotation.Anonymous;
import com.ai.common.core.domain.AjaxResult;
import com.ai.task.domain.GptLbInstance;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/comfy")
public class ComfyApiController {

    @Autowired
    private IComfyConfigService comfyConfigService;

    @PostMapping("/add")
    public AjaxResult add(@RequestBody GptLbInstance instance, @RequestParam(name = "clientId", required = false) String clientId) {
        return comfyConfigService.addNew(instance, clientId);
    }

    @PostMapping("/remove")
    public AjaxResult remove(@RequestBody GptLbInstance instance) {
        return comfyConfigService.remove(instance);

    }

    @PostMapping("/list")
    public AjaxResult list(@RequestBody GptLbInstance instance) {
        return comfyConfigService.list(instance);
    }

    @PostMapping("/query-online")
    public AjaxResult queryOnline(@RequestBody GptLbInstance instance) {
        return comfyConfigService.queryOnline(instance);
    }

    @PostMapping("/query-reconnect")
    public AjaxResult queryReconnect(@RequestBody GptLbInstance instance) {
        return comfyConfigService.queryReconnect(instance);
    }

    @PostMapping("/init")
    public AjaxResult init() {
        return comfyConfigService.init();
    }

    @PostMapping("/init-for-ip")
    public AjaxResult initForIp(@RequestBody GptLbInstance instance) {
        return comfyConfigService.initForIp(instance);
    }

    @PostMapping("/query-time-count")
    public AjaxResult queryTimeCount(@RequestParam(value = "dateStr") String dateStr,
                                     @RequestParam(value = "remoteIp") String remoteIp) {
        return comfyConfigService.queryTimeCount(dateStr, remoteIp);
    }

    @PostMapping("/query-error")
    public AjaxResult queryError(@RequestParam(value = "promptId") String promptId, @RequestParam(value = "queryDate", required = false) String queryDate)  {
        return comfyConfigService.queryError(promptId, queryDate);
    }

    @PostMapping("/check-instance")
    public AjaxResult checkInstance(@RequestBody GptLbInstance instance) {
        return AjaxResult.success(comfyConfigService.checkInstance(instance));
    }

    @PostMapping("/query-error-message")
    public AjaxResult getErrorMessage(@RequestParam(value = "promptId") String promptId, @RequestParam(value = "queryDate", required = false) String queryDate)  {
        return comfyConfigService.getErrorMessage(promptId, queryDate);
    }
}
