package com.ai.purge;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.ai.admin.domain.GptPromptFile;
import com.ai.admin.domain.GptPromptRecord;
import com.ai.admin.mapper.GptPromptFileMapper;
import com.ai.admin.mapper.GptPromptRecordMapper;
import com.ai.admin.service.IGptPromptFileService;
import com.ai.admin.service.IGptPromptRecordService;
import com.ai.common.config.CommonOpexProperties;
import com.ai.common.core.redis.RedisCache;
import com.ai.cos.CosCommonService;
import com.ai.email.service.EmailService;
import com.ai.purge.vo.PurgeQueryParamVo;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.qcloud.cos.model.DeleteObjectsRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 数据清除
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataPurgeServiceImpl implements IDataPurgeService {
    @Autowired
    private IGptPromptFileService promptFileService;
    @Autowired
    private IGptPromptRecordService promptRecordService;
    @Autowired
    private CosCommonService cosCommonService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private CommonOpexProperties commonOpexProperties;

    public volatile static boolean stop_flag = false;
    private static final int CORES = Runtime.getRuntime().availableProcessors();
    private static final ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(3,
            3,
            1,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(100),
            new CustomThreadFactory("purge-thread-"),
            new ThreadPoolExecutor.CallerRunsPolicy());
    private static final ThreadPoolExecutor CHILD_THREAD_POOL = new ThreadPoolExecutor(1,
            1,
            1,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(10000),
            new CustomThreadFactory("purge-thread-child-"),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private static final String GPT_PROMPT_FILE_PREFIX = "gpt_prompt_file_";
    private static final String GPT_PROMPT_RECORD_PREFIX = "gpt_prompt_record_";

    private static final String FAIL_DB_KEY = "fail_db";

    // 1 time / per second
    private static final RateLimiter LIMITER = RateLimiter.create(1);
    private static final RateLimiter LIMITER_DELETE = RateLimiter.create(1.5D);

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    @Override
    public void startPurgeData(Integer daysBefore) {
        if (daysBefore == null) {
            daysBefore = 30;
        }
        stop_flag = false;
        List<CompletableFuture<Void>> submitTasks = new ArrayList<>(20);
        log.info("purge gpt_prompt_file is start...");
        List<SqlSession> sqlSessions = new ArrayList<>();
        long start = System.currentTimeMillis();
        for (int i = 0; i < 20; i++) {
            SqlSession sqlSession = sqlSessionFactory.openSession(true);
            sqlSessions.add(sqlSession);
            PurgeRecordTask purgeRecordTask = new PurgeRecordTask(promptRecordService, promptFileService, redisCache, i);
            purgeRecordTask.setOpexUserName(commonOpexProperties.getUserNames());
            purgeRecordTask.setCosCommonService(cosCommonService);
            purgeRecordTask.setDaysBefore(daysBefore);
            purgeRecordTask.setSqlSession(sqlSession);
            CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(purgeRecordTask, THREAD_POOL);
            submitTasks.add(voidCompletableFuture);
        }
        log.info("waiting purge processing ....");
        try {
            CompletableFuture.allOf(submitTasks.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("purge gpt_prompt_file/record case error", e);
            emailService.sendNotifyMessage("清理数据出错了, 请检查: " + e.getMessage(), "purge error");
            log.info("purge gpt_prompt_file/record  is end");
            return;
        } finally {
            for (SqlSession sqlSession : sqlSessions) {
                sqlSession.flushStatements();
                sqlSession.close();
            }
        }
        submitTasks.clear();
        log.info(" wait purge cos ..............");
        while (true) {
            // 获取正在执行的任务数
            int activeTasks = CHILD_THREAD_POOL.getActiveCount();
            // 获取等待执行的任务数
            int waitingTasks = CHILD_THREAD_POOL.getQueue().size();

            log.info("Active tasks: " + activeTasks);
            log.info("Waiting tasks: " + waitingTasks);

            // 如果所有任务都完成，退出循环
            if (activeTasks == 0 && waitingTasks == 0) {
                log.info("All tasks are finished.");
                break;
            }
            // 暂停一秒再检查
            ThreadUtil.sleep(1000);
        }
        log.info("purge gpt_prompt_record is end spent time : {}s", (System.currentTimeMillis() - start) / 1000D);
    }
    @Override
    public void purgeNotSuccessDeletedCos() {
        String yearMonthDay = DateUtil.today();
        // doProcess(yearMonthDay + ":gpt_prompt_file:f");
        // doProcess(yearMonthDay + ":gpt_prompt_file:tb");
        // doProcess(yearMonthDay + ":gpt_prompt_file:htb");
    }

    @Override
    public String stop() {
        stop_flag = true;
        return "ok";
    }

    private void doProcess(String key) {
        List<String> pathKeyList = redisCache.getCacheListLeftPopButch(key, 1000);
        while (!pathKeyList.isEmpty()) {
            LIMITER.acquire(1);
            cosCommonService.deleteObjects(pathKeyList.toArray(new String[0]));
            pathKeyList = redisCache.getCacheListLeftPopButch(key, 1000);
        }
    }

    private static void buildCosKeys(String fileUrl, List<DeleteObjectsRequest.KeyVersion> fileKeys, List<DeleteObjectsRequest.KeyVersion> fileKeysOld) {
        if (StrUtil.isNotEmpty(fileUrl)) {
            String path = URLUtil.getPath(fileUrl);
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            if (fileUrl.startsWith("https://piclumen-1324066212")) {
                fileKeysOld.add(new DeleteObjectsRequest.KeyVersion(path));
            } else {
                fileKeys.add(new DeleteObjectsRequest.KeyVersion(path));
            }
        }
    }

     @Slf4j
    static class PurgeRecordTask implements Runnable {
        private final IGptPromptRecordService promptRecordService;
        private final IGptPromptFileService promptFileService;
        private final int dbNum;

        @Setter
        private int daysBefore;
        private final RedisCache redisCache;
        @Setter
        private CosCommonService cosCommonService;
        @Setter
        private SqlSession sqlSession;
        @Setter
        private Set<String> opexUserName;

        private String recordDbName;


        public PurgeRecordTask(IGptPromptRecordService promptRecordService, IGptPromptFileService promptFileService, RedisCache redisCache, int dbName) {
            this.promptRecordService = promptRecordService;
            this.promptFileService = promptFileService;
            this.redisCache = redisCache;
            this.dbNum = dbName;
        }

        @Override
        public void run() {
            String recordDbName = GPT_PROMPT_RECORD_PREFIX + dbNum;
            this.recordDbName = recordDbName;
            String fileDbName = GPT_PROMPT_FILE_PREFIX + dbNum;
            String temDbName = "temp_file_id_" + dbNum;

            log.info("start purge record db: {} at time:{}", recordDbName, LocalDateTime.now());
            long t1 = System.currentTimeMillis();
            PurgeQueryParamVo paramVo = new PurgeQueryParamVo();
            paramVo.setLastId(0L);
            paramVo.setStartQueryTime(LocalDateTime.now().minusDays(daysBefore));
            paramVo.setLimitSize(1200);
            paramVo.setDbName(recordDbName);
            String yearMonthDay = DateUtil.today();
            int i = 0;
            // promptFileService.deleteFileByJoinTemp(temDbName, fileDbName);
            GptPromptFileMapper fileMapper = sqlSession.getMapper(GptPromptFileMapper.class);
            GptPromptRecordMapper recordMapper = sqlSession.getMapper(GptPromptRecordMapper.class);
            fileMapper.createTmpDb(temDbName);
            while (!stop_flag) {
                long tt = System.currentTimeMillis();

                int timeout = RandomUtil.randomInt(1, 5);
                log.info("{} loop :{}  sleep {}", recordDbName, 1, timeout);
                ThreadUtil.sleep(timeout * 500, TimeUnit.MILLISECONDS);
                List<GptPromptRecord> records = recordMapper.queryRecordListByCondition(paramVo);
                if (CollUtil.isEmpty(records)) {
                    break;
                }
                paramVo.setLastId(records.get(records.size() - 1).getId());
                i++;
                log.info("start new loop {} for db:{}.,{} ", i, fileDbName, recordDbName);
                List<Long> deletedRecordIds = new ArrayList<>(records.size());
                // Map<String, Map<Long, String>> loginNameMap = new HashMap<>(2048);
                List<String> promptIds = new ArrayList<>(records.size());
                for (GptPromptRecord record : records) {
                    String loginName = record.getLoginName();
                    if (opexUserName.contains(loginName)) {
                        // 运营账号不处理
                        continue;
                    }
                    if (record.getPromptId() != null) {
                        promptIds.add(record.getPromptId());
                    } else {
                        deletedRecordIds.add(record.getId());
                    }
                   /* // key: recordId value: promptId
                    if (record.getPromptId() != null) {
                        Map<Long, String> idPrompIdMap = loginNameMap.get(loginName);
                        if (idPrompIdMap  == null) {
                            idPrompIdMap = new HashMap<>();
                        }
                        idPrompIdMap.put(record.getId(), record.getPromptId());
                        loginNameMap.put(loginName, idPrompIdMap);
                    } else {
                        deletedRecordIds.add(record.getId());
                    }*/
                }

                if (promptIds.isEmpty()) {
                    // 直接删除
                    LIMITER_DELETE.acquire();
                    recordMapper.deleteGptPromptRecordByIdsWithDbName(recordDbName, deletedRecordIds);
                    continue;
                }
                // 查询file
                long tq = System.currentTimeMillis();
                List<GptPromptFile> fileList = fileMapper.queryFileByDbNameWithPromptIds(fileDbName, promptIds);
                log.info("queryFileByDbNameWithPromptIds {} size:{} loop:{} spent:{}", fileDbName, promptIds.size(), i, (System.currentTimeMillis() - tq) / 1000D);
                if (CollUtil.isEmpty(fileList)) {
                    // 没有查出有点赞等操作的file 那么说明所有的record不能删除
                    continue;
                }
                long ti = System.currentTimeMillis();
                fileMapper.truncateTemp(temDbName);
                // ThreadUtil.sleep((dbNum % 5 + 1) * 500, TimeUnit.MILLISECONDS);
                // boolean require = require();
                LIMITER_DELETE.acquire();
                fileMapper.insertTempIds(temDbName, fileDbName, promptIds);
                // release(require);
                log.info("insertTempIds {} size:{} loop:{} spent:{}", recordDbName, promptIds.size(), i, (System.currentTimeMillis() - ti) / 1000D);


                Map<String, List<GptPromptFile>> promptIdFileMap = fileList.stream().collect(Collectors.groupingBy(GptPromptFile::getPromptId));
                // List<Long> deleteFileIds = new ArrayList<>(fileList.size());
                List<DeleteObjectsRequest.KeyVersion> fileKeys = new ArrayList<>(fileList.size());
                List<DeleteObjectsRequest.KeyVersion> thumbnailKeys = new ArrayList<>(fileList.size());
                List<DeleteObjectsRequest.KeyVersion> highThumbnailKeys = new ArrayList<>(fileList.size());

                List<DeleteObjectsRequest.KeyVersion> fileKeysOld = new ArrayList<>(fileList.size());
                List<DeleteObjectsRequest.KeyVersion> thumbnailKeysOld = new ArrayList<>(fileList.size());
                List<DeleteObjectsRequest.KeyVersion> highThumbnailKeysOld = new ArrayList<>(fileList.size());

                for (GptPromptRecord record : records) {
                    Long batchSize = record.getBatchSize();
                    String promptId = record.getPromptId();
                    // 没有点赞等操作的file数量
                    List<GptPromptFile> files = promptIdFileMap.get(promptId);
                    if (CollUtil.isNotEmpty(files)) {
                        if ((long) batchSize == files.size()) {
                            deletedRecordIds.add(record.getId());
                        }
                        for (GptPromptFile file : files) {
                            // deleteFileIds.add(file.getId());
                            buildCosKeys(file.getFileUrl(), fileKeys, fileKeysOld);
                            buildCosKeys(file.getThumbnailUrl(), thumbnailKeys, thumbnailKeysOld);
                            buildCosKeys(file.getHighThumbnailUrl(), highThumbnailKeys, highThumbnailKeysOld);
                        }
                    }
                }

                try {
                    if (CollUtil.isNotEmpty(deletedRecordIds)) {
                        long tPr = System.currentTimeMillis();
                        // ThreadUtil.sleep((dbNum % 5 + 1) * 500, TimeUnit.MILLISECONDS);
                        // boolean r1 = require();
                        LIMITER_DELETE.acquire();
                        recordMapper.deleteGptPromptRecordByIdsWithDbName(recordDbName, deletedRecordIds);
                        log.info("deleteGptPromptRecordByIdsWithDbName size:{} loop:{} spent:{}", deletedRecordIds.size(), i, (System.currentTimeMillis() - tPr) / 1000D);
                    }

                    // if (CollUtil.isNotEmpty(deleteFileIds)) {
                    //     List<List<Long>> split = CollUtil.split(deleteFileIds, 1000);
                    //     for (List<Long> longs : split) {
                    //         promptFileService.deleteGptPromptFileByIdsWithDbName(fileDbName, longs);
                    //     }
                    // }
                    long tPr = System.currentTimeMillis();
                    // ThreadUtil.sleep((dbNum % 5 + 1) * 500, TimeUnit.MILLISECONDS);
                    // boolean r1 = require();
                    LIMITER_DELETE.acquire();
                    fileMapper.deleteFileByJoinTemp(temDbName, fileDbName);
                    log.info("deleteFileByJoinTemp size:{} loop:{} spent:{}", fileList.size(), i, (System.currentTimeMillis() - tPr) / 1000D);
                } catch (Exception e) {
                    log.error("error lll", e);
                    throw new RuntimeException(e);
                }


                paramVo.setLastId(records.get(records.size() - 1).getId());
                records = null;
                // deleteFileIds = null;
                deletedRecordIds = null;
                CompletableFuture.runAsync(() -> {
                    // 通过 API、SDK 发起批量删除，每次最多删除1000个对象。
                    log.info(" start delete cos file for dbName: {}", recordDbName);
                    deleteCosFile(fileKeys, yearMonthDay + ":gpt_prompt_file:f", false);
                    deleteCosFile(thumbnailKeys, yearMonthDay + ":gpt_prompt_file:tb", false);
                    deleteCosFile(highThumbnailKeys, yearMonthDay + ":gpt_prompt_file:htb", false);

                    deleteCosFile(fileKeysOld, yearMonthDay + ":gpt_prompt_file:of", true);
                    deleteCosFile(thumbnailKeysOld, yearMonthDay + ":gpt_prompt_file:otb", true);
                    deleteCosFile(highThumbnailKeysOld, yearMonthDay + ":gpt_prompt_file:ohtb", true);

                    fileKeys.clear();
                    thumbnailKeys.clear();
                    highThumbnailKeys.clear();
                    fileKeysOld.clear();
                    thumbnailKeysOld.clear();
                    highThumbnailKeysOld.clear();
                    log.info(" end delete cos file for dbName: {}", recordDbName);
                }, CHILD_THREAD_POOL);

                log.info("end new loop {} for db:{},{} spent one loop: {}s", i, fileDbName, recordDbName, (System.currentTimeMillis() - tt) / 1000D);

            }
            log.info("end purge db: {}  spent time : {}s loops: {}", recordDbName, (System.currentTimeMillis() - t1) / 1000D, i);
        }

        public void deleteCosFile(List<DeleteObjectsRequest.KeyVersion> fileKeys, String cacheKey, boolean isOld) {
            if (CollUtil.isEmpty(fileKeys)) {
                return;
            }
            List<List<DeleteObjectsRequest.KeyVersion>> split = CollUtil.split(fileKeys, 1000);
            for (List<DeleteObjectsRequest.KeyVersion> keyVersions : split) {
                int retryCount = 0;
                boolean failed = true;
                do {
                    retryCount++;
                    ThreadUtil.sleep(RandomUtil.randomInt(0, 1), TimeUnit.SECONDS);
                    try {
                        LIMITER.acquire(1);
                        if (isOld) {
                            cosCommonService.deleteObjectsOldBucket(keyVersions);
                        } else {
                            cosCommonService.deleteObjects(keyVersions);
                        }
                        failed = false;
                    } catch (Exception e) {
                        log.error("{} delete cos file fail, will be retry ", recordDbName, e);
                    }
                } while (failed && retryCount < 3);
                if (failed) {
                    log.error("{} delete cos file fail after retry {} times ", recordDbName, retryCount);
                    redisCache.setCacheList(cacheKey, keyVersions);
                    redisCache.setCacheList(FAIL_DB_KEY, Lists.newArrayList(recordDbName + "#" + cacheKey));
                }
            }

        }
    }


}
