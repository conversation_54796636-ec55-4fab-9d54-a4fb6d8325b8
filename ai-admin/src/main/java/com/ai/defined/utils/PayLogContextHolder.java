package com.ai.defined.utils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @description pay日志上下文
 */
public class PayLogContextHolder {
    private static final ThreadLocal<String> PAY_LOG_CONTEXT = new ThreadLocal<>();

    public static void init(String prefix) {
        PAY_LOG_CONTEXT.set(prefix + UUID.randomUUID());
    }

    public static String getLogUUID() {
        if (PAY_LOG_CONTEXT.get() == null) {
            init("default-");
        }
        return PAY_LOG_CONTEXT.get().toString();
    }

    public static void remove() {
        PAY_LOG_CONTEXT.remove();
    }
}