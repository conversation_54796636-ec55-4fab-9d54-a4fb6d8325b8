package com.ai.defined.utils;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.bouncycastle.openssl.PEMException;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.crypto.util.PrivateKeyFactory;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.security.PrivateKey;


import java.util.Base64;
import java.util.Date;

@Service
public class AppleApiTokenService {

    @Autowired
    private AppleApiConfig config;

    public String generateToken() throws Exception {
//        // 读取私钥文件
//        byte[] keyBytes = Files.readAllBytes(config.getPrivateKeyPath().getFile().toPath());
//        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
//        KeyFactory kf = KeyFactory.getInstance("EC");
//        PrivateKey privateKey = kf.generatePrivate(spec);
//
//        // 生成 JWT
//        return Jwts.builder()
//                .setHeaderParam("kid", config.getKeyId())
//                .setIssuer(config.getIssuerId())
//                .setAudience("appstoreconnect-v1")
//                .setExpiration(new Date(System.currentTimeMillis() + 20 * 60 * 1000)) // 20分钟有效期
//                .signWith(SignatureAlgorithm.ES256,privateKey)
//                .compact();
        // 读取 PEM 格式的私钥文件
        try (FileReader fileReader = new FileReader(config.getPrivateKeyPath().getFile());
             PEMParser pemParser = new PEMParser(fileReader)) {

            Object pemObject = pemParser.readObject();

            // 确保读取到的是 PrivateKeyInfo 对象
            if (pemObject instanceof PrivateKeyInfo) {
                PrivateKeyInfo privateKeyInfo = (PrivateKeyInfo) pemObject;

                // 使用 Bouncy Castle 的 JcaPEMKeyConverter 来生成 PrivateKey 对象
                JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");
                PrivateKey privateKey = converter.getPrivateKey(privateKeyInfo);
                long now = System.currentTimeMillis();

                // 生成 JWT
//                return Jwts.builder()
//                        .setHeaderParam("kid", config.getKeyId())
//                        .setIssuer(config.getIssuerId())
//                        .setAudience("appstoreconnect-v1")
//                        .setExpiration(new Date(System.currentTimeMillis() + 20 * 60 * 1000)) // 20分钟有效期
//                        .signWith(SignatureAlgorithm.ES256, privateKey)
//                        .compact();
                // 生成JWT Token，使用 RS256 签名算法
                String  jwt = Jwts.builder()
                        .setIssuer(config.getIssuerId())  // 设置发行者
                        .setAudience("appstoreconnect-v1")  // 设置受众
                        .setSubject("user")  // 设置主题
                        .setExpiration(new Date(System.currentTimeMillis() + 20 * 60 * 1000))  // 设置过期时间
                        .setIssuedAt(new Date(now))  // 设置签发时间
                        .setHeaderParam("kid", config.getKeyId())  // 设置key-id（kid）
                        .signWith(SignatureAlgorithm.ES256, privateKey)  // 使用 ES256 签名算法
                        .setHeaderParam("scope", "v1/salesReports")
                                .compact();
                System.out.println("Generated JWT: " + jwt);

                // 打印解码后的负载部分
                String[] jwtParts = jwt.split("\\."); // 分割JWT为 Header.Payload.Signature
                String payload = new String(Base64.getDecoder().decode(jwtParts[1])); // 解码负载部分
                System.out.println("Decoded Payload: " + payload);
                return Jwts.builder()
                        .setIssuer(config.getIssuerId())  // 设置发行者
                        .setAudience("appstoreconnect-v1")  // 设置受众
                        .setSubject("user")  // 设置主题
                        .setExpiration(new Date(System.currentTimeMillis() + 20 * 60 * 1000))  // 设置过期时间
                        .setIssuedAt(new Date(now))  // 设置签发时间
                        .setHeaderParam("kid", config.getKeyId())  // 设置key-id（kid）
                        .signWith(SignatureAlgorithm.ES256, privateKey)  // 使用 ES256 签名算法
                        .setHeaderParam("scope","v1/salesReports")
//                        .setScopes("appstoreconnect-v1")
                        .compact();
            } else {
                throw new RuntimeException("Invalid private key format: " + pemObject.getClass().getName());
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException("Private key file not found", e);
        } catch (IOException e) {
            throw new RuntimeException("Error reading private key file", e);
        }
    }

    public static void main(String[] args) throws Exception {
        AppleApiTokenService service = new AppleApiTokenService();
        String token = service.generateToken();
        System.out.println(token);
    }
}