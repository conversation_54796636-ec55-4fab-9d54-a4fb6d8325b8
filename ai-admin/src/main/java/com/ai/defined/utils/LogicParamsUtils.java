package com.ai.defined.utils;

import com.ai.operation.domain.LogicParams;
import com.ai.operation.mapper.LogicParamsMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 逻辑参数工具类
 *
 * <AUTHOR>
 * @Date 2024-08-06
 **/
@Component
public class LogicParamsUtils {

    @Resource
    private LogicParamsMapper logicParamsMapper;

    /**
     * 通过key获取对象
     */
    public Optional<LogicParams> getKeyValue(String key) {
        QueryWrapper<LogicParams> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("key_name", key);
        List<LogicParams> logicParams = logicParamsMapper.selectList(queryWrapper);
        if (logicParams != null && !logicParams.isEmpty()) {
            return Optional.of(logicParams.get(0));
        }
        return Optional.empty();
    }

    /**
     * 通过key获取值
     */
    public Optional<String> getValue(String key) {
        QueryWrapper<LogicParams> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("key_name", key);
        List<LogicParams> logicParams = logicParamsMapper.selectList(queryWrapper);
        queryWrapper.clear();
        if (logicParams != null && !logicParams.isEmpty() && logicParams.get(0) != null) {
            return Optional.of(logicParams.get(0).getValueData());
        }
        return Optional.empty();
    }
}
