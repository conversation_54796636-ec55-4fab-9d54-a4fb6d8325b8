<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.GptContactsMapper">
    
    <resultMap type="com.ai.admin.domain.GptContacts" id="GptContactsResult">
        <result property="id"    column="id"    />
        <result property="loginName"    column="login_name"    />
        <result property="fullName"    column="full_name"    />
        <result property="reason"    column="reason"    />
        <result property="message"    column="message"    />
        <result property="email"    column="email"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="thumbnailUrls"    column="thumbnail_urls"    />


    </resultMap>

    <sql id="selectGptContactsVo">
        select id, login_name, full_name, reason, message, email,thumbnail_urls,user_agent,create_by, create_time, update_by, update_time from gpt_contacts
    </sql>

    <select id="selectGptContactsList" parameterType="com.ai.admin.domain.GptContacts" resultMap="GptContactsResult">
        <include refid="selectGptContactsVo"/>
        <where>  
            <if test="loginName != null  and loginName != ''"> and login_name like concat('%', #{loginName}, '%')</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="reason != null "> and reason = #{reason}</if>
            <if test="message != null  and message != ''"> and message like concat('%', #{message}, '%')</if>
            <if test="email != null  and email != ''"> and email like concat('%', #{email}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptContactsById" parameterType="Long" resultMap="GptContactsResult">
        <include refid="selectGptContactsVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptContacts" parameterType="com.ai.admin.domain.GptContacts" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_contacts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loginName != null">login_name,</if>
            <if test="fullName != null">full_name,</if>
            <if test="reason != null">reason,</if>
            <if test="message != null">message,</if>
            <if test="email != null">email,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loginName != null">#{loginName},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="reason != null">#{reason},</if>
            <if test="message != null">#{message},</if>
            <if test="email != null">#{email},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateGptContacts" parameterType="com.ai.admin.domain.GptContacts">
        update gpt_contacts
        <trim prefix="SET" suffixOverrides=",">
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="message != null">message = #{message},</if>
            <if test="email != null">email = #{email},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptContactsById" parameterType="Long">
        delete from gpt_contacts where id = #{id}
    </delete>

    <delete id="deleteGptContactsByIds" parameterType="String">
        delete from gpt_contacts where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>