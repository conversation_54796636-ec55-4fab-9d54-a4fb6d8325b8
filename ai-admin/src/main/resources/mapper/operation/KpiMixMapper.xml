<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.KpiMixMapper">
    <resultMap type="com.ai.operation.domain.KpiMix" id="KpiMixResult">
        <result property="id"    column="id"    />
        <result property="recordDate"    column="record_date"    />
        <result property="avgChartTime"    column="avg_chart_time"    />
        <result property="avgWait"    column="avg_wait"    />
        <result property="avgChartOneTime"    column="avg_chart_one_time"    />
        <result property="avgChartTwoTime"    column="avg_chart_two_time"    />
        <result property="avgChartThreeTime"    column="avg_chart_three_time"    />
        <result property="avgChartFourTime"    column="avg_chart_four_time"    />
        <result property="avgPerChartTime"    column="avg_per_chart_time"    />
        <result property="chartCount"    column="chart_count"    />
        <result property="dau"    column="dau"    />
        <result property="wau"    column="wau"    />
        <result property="mau"    column="mau"    />
        <result property="newRegisters"    column="new_registers"    />
        <result property="totalRegisters"    column="total_registers"    />
        <result property="avgChartsPerUser"    column="avg_charts_per_user"    />
        <result property="maxConcurrentChartTasks"    column="max_concurrent_chart_tasks"    />
        <result property="maxChartsPerUser"    column="max_charts_per_user"    />
        <result property="chartCountOne"    column="chart_count_one"    />
        <result property="chartCountTwo"    column="chart_count_two"    />
        <result property="chartCountThree"    column="chart_count_three"    />
        <result property="chartCountFour"    column="chart_count_four"    />
        <result property="text2picTasks"    column="text2pic_tasks"    />
        <result property="hiresfixTasks"    column="hiresfix_tasks"    />
        <result property="removebgTasks"    column="removebg_tasks"    />
        <result property="pic2picTasks"    column="pic2pic_tasks"    />
        <result property="fastTasks"    column="fast_tasks"    />
        <result property="remixPerUser"    column="remix_per_user"    />
        <result property="chartTaskCount"    column="chart_task_count"    />
        <result property="pic2picCharacterRef"    column="pic2pic_character_ref"    />
        <result property="pic2picContentRef"    column="pic2pic_content_ref"    />
        <result property="pic2picStyleRef"    column="pic2pic_style_ref"    />
        <result property="favoriteAspectRatio"    column="favorite_aspect_ratio"    />
        <result property="realisticCount"    column="realistic_count"    />
        <result property="animeCount"    column="anime_count"    />
        <result property="lineartCount"    column="lineart_count"    />
        <result property="relaxChartTaskRate"    column="relax_chart_task_rate"    />
        <result property="fastChartTaskRate"    column="fast_chart_task_rate"    />
        <result property="chartSuccessTaskRate"    column="chart_success_task_rate"    />
        <result property="chartSuccessTaskCount"    column="chart_success_task_count"    />
        <result property="relaxOnePicTime"    column="relax_one_pic_time"    />
        <result property="relaxTaskAvgTime"    column="relax_task_avg_time"    />
        <result property="fastOnePicTime"    column="fast_one_pic_time"    />
        <result property="fastTaskAvgTime"    column="fast_task_avg_time"    />
        <result property="customUploadTasks"    column="custom_upload_tasks"    />
        <result property="enlargeImageTasks"    column="enlarge_image_tasks"    />
        <result property="lineRecolorTasks"    column="line_recolor_tasks"    />
        <result property="localRedrawTasks"    column="local_redraw_tasks"    />
        <result property="varyTasks"    column="vary_tasks"    />
        <result property="fluxCount"    column="flux_count"    />
        <result property="ponyCount"    column="pony_count"    />
        <result property="artCount"    column="art_count"    />
        <result property="unfairQueueMaxConcurrentChartTasks"    column="unfair_queue_max_concurrent_chart_tasks"    />
        <result property="fairQueueMaxConcurrentChartTasks"    column="fair_queue_max_concurrent_chart_tasks"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="webDuv"    column="web_duv"    />
        <result property="iosDuv"    column="ios_duv"    />
        <result property="androidDuv"    column="android_duv"    />
    </resultMap>

    <sql id="selectKpiMixVo">
        select id, record_date, avg_chart_time, avg_wait, avg_chart_one_time, avg_chart_two_time, avg_chart_three_time,
               avg_chart_four_time, avg_per_chart_time, chart_count, dau, wau, mau, new_registers, total_registers,
               avg_charts_per_user, max_concurrent_chart_tasks, max_charts_per_user, chart_count_one, chart_count_two,
               chart_count_three, chart_count_four, text2pic_tasks, hiresfix_tasks, removebg_tasks, pic2pic_tasks, fast_tasks,
               remix_per_user, chart_task_count, pic2pic_character_ref, pic2pic_content_ref, pic2pic_style_ref, favorite_aspect_ratio,
               realistic_count, anime_count, lineart_count,relax_chart_task_rate,fast_chart_task_rate,chart_success_task_rate,
               chart_success_task_count,relax_one_pic_time,relax_task_avg_time,fast_one_pic_time,fast_task_avg_time,
               create_by, create_time, update_by, update_time,custom_upload_tasks,enlarge_image_tasks,line_recolor_tasks,
               local_redraw_tasks,vary_tasks,flux_count,pony_count,art_count,unfair_queue_max_concurrent_chart_tasks,fair_queue_max_concurrent_chart_tasks,
               web_duv, ios_duv, android_duv
               from kpi_mix
    </sql>

    <select id="selectKpiMixList" parameterType="com.ai.operation.domain.KpiMix" resultMap="KpiMixResult">
        <include refid="selectKpiMixVo"/>
        <where>
            <if test="recordDate != null "> and record_date = #{recordDate}</if>
        </where>
        order by record_date desc
    </select>

    <select id="selectKpiMixById" parameterType="Long" resultMap="KpiMixResult">
        <include refid="selectKpiMixVo"/>
        where id = #{id}
    </select>

    <insert id="insertKpiMix" parameterType="com.ai.operation.domain.KpiMix" useGeneratedKeys="true" keyProperty="id">
        insert into kpi_mix
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recordDate != null">record_date,</if>
            <if test="avgChartTime != null">avg_chart_time,</if>
            <if test="avgWait != null">avg_wait,</if>
            <if test="avgChartOneTime != null">avg_chart_one_time,</if>
            <if test="avgChartTwoTime != null">avg_chart_two_time,</if>
            <if test="avgChartThreeTime != null">avg_chart_three_time,</if>
            <if test="avgChartFourTime != null">avg_chart_four_time,</if>
            <if test="avgPerChartTime != null">avg_per_chart_time,</if>
            <if test="chartCount != null">chart_count,</if>
            <if test="dau != null">dau,</if>
            <if test="wau != null">wau,</if>
            <if test="mau != null">mau,</if>
            <if test="newRegisters != null">new_registers,</if>
            <if test="totalRegisters != null">total_registers,</if>
            <if test="avgChartsPerUser != null">avg_charts_per_user,</if>
            <if test="maxConcurrentChartTasks != null">max_concurrent_chart_tasks,</if>
            <if test="maxChartsPerUser != null">max_charts_per_user,</if>
            <if test="chartCountOne != null">chart_count_one,</if>
            <if test="chartCountTwo != null">chart_count_two,</if>
            <if test="chartCountThree != null">chart_count_three,</if>
            <if test="chartCountFour != null">chart_count_four,</if>
            <if test="text2picTasks != null">text2pic_tasks,</if>
            <if test="hiresfixTasks != null">hiresfix_tasks,</if>
            <if test="removebgTasks != null">removebg_tasks,</if>
            <if test="pic2picTasks != null">pic2pic_tasks,</if>
            <if test="fastTasks != null">fast_tasks,</if>
            <if test="remixPerUser != null">remix_per_user,</if>
            <if test="chartTaskCount != null">chart_task_count,</if>
            <if test="pic2picCharacterRef != null">pic2pic_character_ref,</if>
            <if test="pic2picContentRef != null">pic2pic_content_ref,</if>
            <if test="pic2picStyleRef != null">pic2pic_style_ref,</if>
            <if test="favoriteAspectRatio != null">favorite_aspect_ratio,</if>
            <if test="realisticCount != null">realistic_count,</if>
            <if test="animeCount != null">anime_count,</if>
            <if test="lineartCount != null">lineart_count,</if>
            <if test="artCount != null">art_count,</if>
            <if test="unfairQueueMaxConcurrentChartTasks != null">unfair_queue_max_concurrent_chart_tasks,</if>
            <if test="fairQueueMaxConcurrentChartTasks != null">fair_queue_max_concurrent_chart_tasks,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recordDate != null">#{recordDate},</if>
            <if test="avgChartTime != null">#{avgChartTime},</if>
            <if test="avgWait != null">#{avgWait},</if>
            <if test="avgChartOneTime != null">#{avgChartOneTime},</if>
            <if test="avgChartTwoTime != null">#{avgChartTwoTime},</if>
            <if test="avgChartThreeTime != null">#{avgChartThreeTime},</if>
            <if test="avgChartFourTime != null">#{avgChartFourTime},</if>
            <if test="avgPerChartTime != null">#{avgPerChartTime},</if>
            <if test="chartCount != null">#{chartCount},</if>
            <if test="dau != null">#{dau},</if>
            <if test="wau != null">#{wau},</if>
            <if test="mau != null">#{mau},</if>
            <if test="newRegisters != null">#{newRegisters},</if>
            <if test="totalRegisters != null">#{totalRegisters},</if>
            <if test="avgChartsPerUser != null">#{avgChartsPerUser},</if>
            <if test="maxConcurrentChartTasks != null">#{maxConcurrentChartTasks},</if>
            <if test="maxChartsPerUser != null">#{maxChartsPerUser},</if>
            <if test="chartCountOne != null">#{chartCountOne},</if>
            <if test="chartCountTwo != null">#{chartCountTwo},</if>
            <if test="chartCountThree != null">#{chartCountThree},</if>
            <if test="chartCountFour != null">#{chartCountFour},</if>
            <if test="text2picTasks != null">#{text2picTasks},</if>
            <if test="hiresfixTasks != null">#{hiresfixTasks},</if>
            <if test="removebgTasks != null">#{removebgTasks},</if>
            <if test="pic2picTasks != null">#{pic2picTasks},</if>
            <if test="fastTasks != null">#{fastTasks},</if>
            <if test="remixPerUser != null">#{remixPerUser},</if>
            <if test="chartTaskCount != null">#{chartTaskCount},</if>
            <if test="pic2picCharacterRef != null">#{pic2picCharacterRef},</if>
            <if test="pic2picContentRef != null">#{pic2picContentRef},</if>
            <if test="pic2picStyleRef != null">#{pic2picStyleRef},</if>
            <if test="favoriteAspectRatio != null">#{favoriteAspectRatio},</if>
            <if test="realisticCount != null">#{realisticCount},</if>
            <if test="animeCount != null">#{animeCount},</if>
            <if test="lineartCount != null">#{lineartCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="artCount != null">#{artCount},</if>
            <if test="unfairQueueMaxConcurrentChartTasks != null">#{unfairQueueMaxConcurrentChartTasks},</if>
            <if test="fairQueueMaxConcurrentChartTasks != null">#{fairQueueMaxConcurrentChartTasks},</if>
        </trim>
    </insert>

    <update id="updateKpiMix" parameterType="com.ai.operation.domain.KpiMix">
        update kpi_mix
        <trim prefix="SET" suffixOverrides=",">
            <if test="recordDate != null">record_date = #{recordDate},</if>
            <if test="avgChartTime != null">avg_chart_time = #{avgChartTime},</if>
            <if test="avgWait != null">avg_wait = #{avgWait},</if>
            <if test="avgChartOneTime != null">avg_chart_one_time = #{avgChartOneTime},</if>
            <if test="avgChartTwoTime != null">avg_chart_two_time = #{avgChartTwoTime},</if>
            <if test="avgChartThreeTime != null">avg_chart_three_time = #{avgChartThreeTime},</if>
            <if test="avgChartFourTime != null">avg_chart_four_time = #{avgChartFourTime},</if>
            <if test="avgPerChartTime != null">avg_per_chart_time = #{avgPerChartTime},</if>
            <if test="chartCount != null">chart_count = #{chartCount},</if>
            <if test="dau != null">dau = #{dau},</if>
            <if test="wau != null">wau = #{wau},</if>
            <if test="mau != null">mau = #{mau},</if>
            <if test="newRegisters != null">new_registers = #{newRegisters},</if>
            <if test="totalRegisters != null">total_registers = #{totalRegisters},</if>
            <if test="avgChartsPerUser != null">avg_charts_per_user = #{avgChartsPerUser},</if>
            <if test="maxConcurrentChartTasks != null">max_concurrent_chart_tasks = #{maxConcurrentChartTasks},</if>
            <if test="maxChartsPerUser != null">max_charts_per_user = #{maxChartsPerUser},</if>
            <if test="chartCountOne != null">chart_count_one = #{chartCountOne},</if>
            <if test="chartCountTwo != null">chart_count_two = #{chartCountTwo},</if>
            <if test="chartCountThree != null">chart_count_three = #{chartCountThree},</if>
            <if test="chartCountFour != null">chart_count_four = #{chartCountFour},</if>
            <if test="text2picTasks != null">text2pic_tasks = #{text2picTasks},</if>
            <if test="hiresfixTasks != null">hiresfix_tasks = #{hiresfixTasks},</if>
            <if test="removebgTasks != null">removebg_tasks = #{removebgTasks},</if>
            <if test="pic2picTasks != null">pic2pic_tasks = #{pic2picTasks},</if>
            <if test="fastTasks != null">fast_tasks = #{fastTasks},</if>
            <if test="remixPerUser != null">remix_per_user = #{remixPerUser},</if>
            <if test="chartTaskCount != null">chart_task_count = #{chartTaskCount},</if>
            <if test="pic2picCharacterRef != null">pic2pic_character_ref = #{pic2picCharacterRef},</if>
            <if test="pic2picContentRef != null">pic2pic_content_ref = #{pic2picContentRef},</if>
            <if test="pic2picStyleRef != null">pic2pic_style_ref = #{pic2picStyleRef},</if>
            <if test="favoriteAspectRatio != null">favorite_aspect_ratio = #{favoriteAspectRatio},</if>
            <if test="realisticCount != null">realistic_count = #{realisticCount},</if>
            <if test="animeCount != null">anime_count = #{animeCount},</if>
            <if test="lineartCount != null">lineart_count = #{lineartCount},</if>
            <if test="artCount != null">art_count = #{artCount},</if>
            <if test="unfairQueueMaxConcurrentChartTasks != null">unfair_queue_max_concurrent_chart_tasks = #{unfairQueueMaxConcurrentChartTasks},</if>
            <if test="fairQueueMaxConcurrentChartTasks != null">fair_queue_max_concurrent_chart_tasks = #{fairQueueMaxConcurrentChartTasks},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKpiMixById" parameterType="Long">
        delete from kpi_mix where id = #{id}
    </delete>

    <delete id="deleteKpiMixByIds" parameterType="String">
        delete from kpi_mix where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectKpiMixWeek" parameterType="com.ai.operation.domain.KpiMix" resultMap="KpiMixResult">
        <include refid="selectKpiMixVo"/>
        <where>
              <if test="day != null">
              record_date >= CURDATE() - INTERVAL ${day} DAY
              </if>
        </where>
        ORDER BY record_date asc
    </select>

    <select id="getYesterdayEmailData" resultType="java.util.Map">
        SELECT
            record_date as yesterdayDate,
            COALESCE(ROUND(fast_task_avg_time,0), 0.0) AS yesterdayTaskAvgTime,
            COALESCE(ROUND(fast_one_pic_time,0), 0.0) AS yesterdayFastOnePicTime,
            COALESCE(ROUND(relax_task_avg_time,0), 0.0) AS yesterdayRelaxTaskAvgTime,
            COALESCE(ROUND(relax_one_pic_time,0), 0.0) AS yesterdayRelaxOnePicTime,
            COALESCE(dau, 0) AS yesterdayUseOperateNumsByDay,
            COALESCE(wau, 0) AS yesterdayUseOperateNumsByWeek,
            COALESCE(mau, 0) AS yesterdayUseOperateNumsByMonth,
            COALESCE(chart_count, 0) AS yesterdayCreatePictureNums,
            COALESCE(new_registers, 0) AS yesterdayUerNums,
            COALESCE(total_registers, 0) AS yesterdayAllUserNums,
            COALESCE(avg_charts_per_user, 0) AS yesterdayAvgCratePictureNums,
            COALESCE(max_concurrent_chart_tasks, 0) AS yesterdayMaxTaskSize,
            COALESCE(chart_task_count, 0) AS yesterdayGenNums,
            COALESCE(chart_success_task_count, 0) AS yesterdaySuccessTaskCount,
            COALESCE(chart_success_task_rate, 0.00) AS yesterdayGenSuccessRate,
            COALESCE(fast_chart_task_rate, 0) AS yesterdayFastChartTaskRate,
            COALESCE(relax_chart_task_rate, 0) AS yesterdayRelaxChartTaskRate,
            COALESCE(fair_queue_max_concurrent_chart_tasks, 0) AS yesterdayFairQueue,
            COALESCE(unfair_queue_max_concurrent_chart_tasks, 0) AS yesterdayUnfairQueue
        FROM kpi_mix
        WHERE record_date = DATE_SUB(CURDATE(), INTERVAL 2 DAY)
        ORDER BY record_date DESC
        LIMIT 1;
    </select>

    <select id="getSevenDaysEmailData" resultType="java.util.Map">
        SELECT
            COALESCE(ROUND(AVG(fast_task_avg_time),1), 0.0) AS sevenDaysFastTaskAvgTime,
            COALESCE(ROUND(AVG(fast_one_pic_time),1), 0.0) AS sevenDaysFastOnePicTime,
            COALESCE(ROUND(AVG(relax_task_avg_time),1), 0.0) AS sevenDaysRelaxTaskAvgTime,
            COALESCE(ROUND(AVG(relax_one_pic_time),1), 0.0) AS sevenDaysRelaxOnePicTime,
            COALESCE(ROUND(AVG(dau)), 0) AS sevenDaysUseOperateNumsByDay,
            COALESCE(ROUND(AVG(wau)), 0) AS sevenDaysUseOperateNumsByWeek,
            COALESCE(ROUND(AVG(mau)), 0) AS sevenDaysUseOperateNumsByMonth,
            COALESCE(ROUND(AVG(chart_count)), 0) AS sevenDaysCreatePictureNums,
            COALESCE(ROUND(AVG(new_registers)), 0) AS sevenDaysUerNums,
            COALESCE(ROUND(AVG(total_registers)), 0) AS sevenDaysAllUserNums,
            COALESCE(ROUND(AVG(avg_charts_per_user)), 0) AS sevenDaysAvgCratePictureNums,
            COALESCE(ROUND(AVG(max_concurrent_chart_tasks)), 0) AS sevenDaysMaxTaskSize,
            COALESCE(ROUND(AVG(chart_task_count)), 0) AS sevenDaysGenNums,
            COALESCE(ROUND(AVG(chart_success_task_count)), 0) AS sevenDaysSuccessTaskCount,
            COALESCE(ROUND(AVG(chart_success_task_rate),2), 0) AS sevenDaysGenSuccessRate,
            COALESCE(ROUND(AVG(fast_chart_task_rate)), 0) AS sevenDaysFastChartTaskRate,
            COALESCE(ROUND(AVG(relax_chart_task_rate)), 0) AS sevenDaysRelaxChartTaskRate,
            COALESCE(ROUND(AVG(fair_queue_max_concurrent_chart_tasks)), 0) AS sevenDaysFairQueue,
            COALESCE(ROUND(AVG(unfair_queue_max_concurrent_chart_tasks)), 0) AS sevenDaysUnfairQueue
        FROM kpi_mix
        WHERE record_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND DATE_SUB(CURDATE(), INTERVAL 1 DAY);
    </select>
</mapper>