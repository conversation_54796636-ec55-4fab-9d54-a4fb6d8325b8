<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.QuestionnaireAnswerMapper">

    <resultMap type="com.ai.operation.domain.QuestionnaireAnswer" id="QuestionnaireAnswerResult">
        <result property="id"    column="id"    />
        <result property="loginName"    column="login_name"    />
        <result property="questionnaireId"    column="questionnaire_id"    />
        <result property="questionnaireTitle"    column="questionnaire_title"    />
        <result property="platform"    column="platform"    />
        <result property="scAnswer1"    column="sc_answer1"    />
        <result property="scAnswer2"    column="sc_answer2"    />
        <result property="scAnswer3"    column="sc_answer3"    />
        <result property="scAnswer4"    column="sc_answer4"    />
        <result property="scAnswer5"    column="sc_answer5"    />
        <result property="scAnswer6"    column="sc_answer6"    />
        <result property="scAnswer7"    column="sc_answer7"    />
        <result property="scAnswer8"    column="sc_answer8"    />
        <result property="scAnswer9"    column="sc_answer9"    />
        <result property="scAnswer10"    column="sc_answer10"    />
        <result property="mcAnswer1"    column="mc_answer1"    />
        <result property="mcAnswer2"    column="mc_answer2"    />
        <result property="mcAnswer3"    column="mc_answer3"    />
        <result property="mcAnswer4"    column="mc_answer4"    />
        <result property="mcAnswer5"    column="mc_answer5"    />
        <result property="mcAnswer6"    column="mc_answer6"    />
        <result property="mcAnswer7"    column="mc_answer7"    />
        <result property="mcAnswer8"    column="mc_answer8"    />
        <result property="mcAnswer9"    column="mc_answer9"    />
        <result property="mcAnswer10"    column="mc_answer10"    />
        <result property="essayAnswer1"    column="essay_answer1"    />
        <result property="essayAnswer2"    column="essay_answer2"    />
        <result property="essayAnswer3"    column="essay_answer3"    />
        <result property="essayAnswer4"    column="essay_answer4"    />
        <result property="essayAnswer5"    column="essay_answer5"    />
        <result property="gradeAnswer1"    column="grade_answer1"    />
        <result property="gradeAnswer2"    column="grade_answer2"    />
        <result property="gradeAnswer3"    column="grade_answer3"    />
        <result property="gradeAnswer4"    column="grade_answer4"    />
        <result property="gradeAnswer5"    column="grade_answer5"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="del"    column="del"    />
    </resultMap>

    <sql id="selectQuestionnaireAnswerVo">
        select id, login_name, questionnaire_id, questionnaire_title, platform, sc_answer1, sc_answer2, sc_answer3, sc_answer4, sc_answer5, sc_answer6, sc_answer7, sc_answer8, sc_answer9, sc_answer10,
               mc_answer1, mc_answer2, mc_answer3, mc_answer4, mc_answer5,mc_answer6,mc_answer7,mc_answer8,mc_answer9,mc_answer10,essay_answer1, essay_answer2, essay_answer3, essay_answer4, essay_answer5,
               grade_answer1,grade_answer2,grade_answer3,grade_answer4,grade_answer5,create_time, update_time, create_by, update_by, del from questionnaire_answer
    </sql>

    <select id="selectQuestionnaireAnswerList" parameterType="com.ai.operation.domain.QuestionnaireAnswer" resultMap="QuestionnaireAnswerResult">
        <include refid="selectQuestionnaireAnswerVo"/>
        <where>
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="questionnaireId != null  and questionnaireId != ''"> and questionnaire_id = #{questionnaireId}</if>
            <if test="questionnaireTitle != null  and questionnaireTitle != ''"> and questionnaire_title = #{questionnaireTitle}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectQuestionnaireAnswerById" parameterType="Long" resultMap="QuestionnaireAnswerResult">
        <include refid="selectQuestionnaireAnswerVo"/>
        where id = #{id}
    </select>

    <insert id="insertQuestionnaireAnswer" parameterType="com.ai.operation.domain.QuestionnaireAnswer">
        insert into questionnaire_answer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="questionnaireId != null">questionnaire_id,</if>
            <if test="questionnaireTitle != null">questionnaire_title,</if>
            <if test="platform != null">platform,</if>
            <if test="scAnswer1 != null">sc_answer1,</if>
            <if test="scAnswer2 != null">sc_answer2,</if>
            <if test="scAnswer3 != null">sc_answer3,</if>
            <if test="scAnswer4 != null">sc_answer4,</if>
            <if test="scAnswer5 != null">sc_answer5,</if>
            <if test="scAnswer6 != null">sc_answer6,</if>
            <if test="scAnswer7 != null">sc_answer7,</if>
            <if test="scAnswer8 != null">sc_answer8,</if>
            <if test="scAnswer9 != null">sc_answer9,</if>
            <if test="scAnswer10 != null">sc_answer10,</if>
            <if test="mcAnswer1 != null">mc_answer1,</if>
            <if test="mcAnswer2 != null">mc_answer2,</if>
            <if test="mcAnswer3 != null">mc_answer3,</if>
            <if test="mcAnswer4 != null">mc_answer4,</if>
            <if test="mcAnswer5 != null">mc_answer5,</if>
            <if test="essayAnswer1 != null">essay_answer1,</if>
            <if test="essayAnswer2 != null">essay_answer2,</if>
            <if test="essayAnswer3 != null">essay_answer3,</if>
            <if test="essayAnswer4 != null">essay_answer4,</if>
            <if test="essayAnswer5 != null">essay_answer5,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="del != null">del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="questionnaireId != null">#{questionnaireId},</if>
            <if test="questionnaireTitle != null">#{questionnaireTitle},</if>
            <if test="platform != null">#{platform},</if>
            <if test="scAnswer1 != null">#{scAnswer1},</if>
            <if test="scAnswer2 != null">#{scAnswer2},</if>
            <if test="scAnswer3 != null">#{scAnswer3},</if>
            <if test="scAnswer4 != null">#{scAnswer4},</if>
            <if test="scAnswer5 != null">#{scAnswer5},</if>
            <if test="scAnswer6 != null">#{scAnswer6},</if>
            <if test="scAnswer7 != null">#{scAnswer7},</if>
            <if test="scAnswer8 != null">#{scAnswer8},</if>
            <if test="scAnswer9 != null">#{scAnswer9},</if>
            <if test="scAnswer10 != null">#{scAnswer10},</if>
            <if test="mcAnswer1 != null">#{mcAnswer1},</if>
            <if test="mcAnswer2 != null">#{mcAnswer2},</if>
            <if test="mcAnswer3 != null">#{mcAnswer3},</if>
            <if test="mcAnswer4 != null">#{mcAnswer4},</if>
            <if test="mcAnswer5 != null">#{mcAnswer5},</if>
            <if test="essayAnswer1 != null">#{essayAnswer1},</if>
            <if test="essayAnswer2 != null">#{essayAnswer2},</if>
            <if test="essayAnswer3 != null">#{essayAnswer3},</if>
            <if test="essayAnswer4 != null">#{essayAnswer4},</if>
            <if test="essayAnswer5 != null">#{essayAnswer5},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="del != null">#{del},</if>
        </trim>
    </insert>

    <update id="updateQuestionnaireAnswer" parameterType="com.ai.operation.domain.QuestionnaireAnswer">
        update questionnaire_answer
        <trim prefix="SET" suffixOverrides=",">
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="questionnaireId != null">questionnaire_id = #{questionnaireId},</if>
            <if test="questionnaireTitle != null">questionnaire_title = #{questionnaireTitle},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="scAnswer1 != null">sc_answer1 = #{scAnswer1},</if>
            <if test="scAnswer2 != null">sc_answer2 = #{scAnswer2},</if>
            <if test="scAnswer3 != null">sc_answer3 = #{scAnswer3},</if>
            <if test="scAnswer4 != null">sc_answer4 = #{scAnswer4},</if>
            <if test="scAnswer5 != null">sc_answer5 = #{scAnswer5},</if>
            <if test="scAnswer6 != null">sc_answer6 = #{scAnswer6},</if>
            <if test="scAnswer7 != null">sc_answer7 = #{scAnswer7},</if>
            <if test="scAnswer8 != null">sc_answer8 = #{scAnswer8},</if>
            <if test="scAnswer9 != null">sc_answer9 = #{scAnswer9},</if>
            <if test="scAnswer10 != null">sc_answer10 = #{scAnswer10},</if>
            <if test="mcAnswer1 != null">mc_answer1 = #{mcAnswer1},</if>
            <if test="mcAnswer2 != null">mc_answer2 = #{mcAnswer2},</if>
            <if test="mcAnswer3 != null">mc_answer3 = #{mcAnswer3},</if>
            <if test="mcAnswer4 != null">mc_answer4 = #{mcAnswer4},</if>
            <if test="mcAnswer5 != null">mc_answer5 = #{mcAnswer5},</if>
            <if test="essayAnswer1 != null">essay_answer1 = #{essayAnswer1},</if>
            <if test="essayAnswer2 != null">essay_answer2 = #{essayAnswer2},</if>
            <if test="essayAnswer3 != null">essay_answer3 = #{essayAnswer3},</if>
            <if test="essayAnswer4 != null">essay_answer4 = #{essayAnswer4},</if>
            <if test="essayAnswer5 != null">essay_answer5 = #{essayAnswer5},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="del != null">del = #{del},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuestionnaireAnswerById" parameterType="Long">
        delete from questionnaire_answer where id = #{id}
    </delete>

    <delete id="deleteQuestionnaireAnswerByIds" parameterType="String">
        delete from questionnaire_answer where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>