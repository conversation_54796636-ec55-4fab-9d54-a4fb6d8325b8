<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.GptExploreFileMapper">

    <resultMap type="GptExploreFile" id="GptExploreFileResult">
        <result property="id"    column="id"    />
        <result property="loginName"    column="login_name"    />
        <result property="promptId"    column="prompt_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="thumbnailName"    column="thumbnail_name"    />
        <result property="highThumbnailName"    column="high_thumbnail_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="thumbnailUrl"    column="thumbnail_url"    />
        <result property="highThumbnailUrl"    column="high_thumbnail_url"    />
        <result property="sensitiveMessage"    column="sensitive_message"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="likeNums"    column="like_nums"    />
        <result property="createType"    column="create_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="del"    column="del"    />
    </resultMap>

    <sql id="selectGptExploreFileVo">
        select id, login_name, prompt_id, file_name, thumbnail_name, high_thumbnail_name, file_url, thumbnail_url, high_thumbnail_url, sensitive_message, width, height, like_nums, create_type, create_time, update_time, create_by, update_by, del from gpt_explore_file
    </sql>

    <select id="selectGptExploreFileList" parameterType="GptExploreFile" resultMap="GptExploreFileResult">
        <include refid="selectGptExploreFileVo"/>
        <where>
            <if test="loginName != null  and loginName != ''"> and login_name like concat('%', #{loginName}, '%')</if>
            <if test="promptId != null  and promptId != ''"> and prompt_id = #{promptId}</if>
            <if test="createType != null "> and create_type = #{createType}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectGptExploreFileById" parameterType="Long" resultMap="GptExploreFileResult">
        <include refid="selectGptExploreFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptExploreFile" parameterType="GptExploreFile" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_explore_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loginName != null">login_name,</if>
            <if test="promptId != null">prompt_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="thumbnailName != null">thumbnail_name,</if>
            <if test="highThumbnailName != null">high_thumbnail_name,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="thumbnailUrl != null">thumbnail_url,</if>
            <if test="highThumbnailUrl != null">high_thumbnail_url,</if>
            <if test="sensitiveMessage != null">sensitive_message,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="likeNums != null">like_nums,</if>
            <if test="createType != null">create_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="del != null">del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loginName != null">#{loginName},</if>
            <if test="promptId != null">#{promptId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="thumbnailName != null">#{thumbnailName},</if>
            <if test="highThumbnailName != null">#{highThumbnailName},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="thumbnailUrl != null">#{thumbnailUrl},</if>
            <if test="highThumbnailUrl != null">#{highThumbnailUrl},</if>
            <if test="sensitiveMessage != null">#{sensitiveMessage},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="likeNums != null">#{likeNums},</if>
            <if test="createType != null">#{createType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="del != null">#{del},</if>
        </trim>
    </insert>

    <update id="updateGptExploreFile" parameterType="GptExploreFile">
        update gpt_explore_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="promptId != null">prompt_id = #{promptId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="thumbnailName != null">thumbnail_name = #{thumbnailName},</if>
            <if test="highThumbnailName != null">high_thumbnail_name = #{highThumbnailName},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="thumbnailUrl != null">thumbnail_url = #{thumbnailUrl},</if>
            <if test="highThumbnailUrl != null">high_thumbnail_url = #{highThumbnailUrl},</if>
            <if test="sensitiveMessage != null">sensitive_message = #{sensitiveMessage},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="likeNums != null">like_nums = #{likeNums},</if>
            <if test="createType != null">create_type = #{createType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="del != null">del = #{del},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptExploreFileById" parameterType="Long">
        delete from gpt_explore_file where id = #{id}
    </delete>

    <delete id="deleteGptExploreFileByIds" parameterType="String">
        delete from gpt_explore_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>