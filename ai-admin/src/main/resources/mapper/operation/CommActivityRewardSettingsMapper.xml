<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.CommActivityRewardSettingsMapper">
    
    <resultMap type="com.ai.operation.domain.CommActivityRewardSettings" id="CommActivityRewardSettingsResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="prizeLevel"    column="prize_level"    />
        <result property="type"    column="type"    />
        <result property="planLevel"    column="plan_level"    />
        <result property="priceInterval"    column="price_interval"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="rewardNum"    column="reward_num"    />
        <result property="winnersNum"    column="winners_num"    />
        <result property="mark"    column="mark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectCommActivityRewardSettingsVo">
        select id, activity_id, prize_level, type, plan_level, price_interval, start_time, end_time, reward_num, winners_num, mark, create_time, update_time, create_by, update_by from comm_activity_reward_settings
    </sql>

    <select id="selectCommActivityRewardSettingsList" parameterType="com.ai.operation.domain.CommActivityRewardSettings" resultMap="CommActivityRewardSettingsResult">
        <include refid="selectCommActivityRewardSettingsVo"/>
        <where>  
            <if test="activityId != null "> and activity_id = #{activityId}</if>
            <if test="prizeLevel != null "> and prize_level = #{prizeLevel}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="planLevel != null  and planLevel != ''"> and plan_level = #{planLevel}</if>
            <if test="priceInterval != null  and priceInterval != ''"> and price_interval = #{priceInterval}</if>
            <if test="params !=null and  params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''"> and start_time between #{params.beginStartTime} and #{params.endStartTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCommActivityRewardSettingsById" parameterType="Long" resultMap="CommActivityRewardSettingsResult">
        <include refid="selectCommActivityRewardSettingsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCommActivityRewardSettings" parameterType="com.ai.operation.domain.CommActivityRewardSettings">
        insert into comm_activity_reward_settings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="prizeLevel != null">prize_level,</if>
            <if test="type != null">type,</if>
            <if test="planLevel != null">plan_level,</if>
            <if test="priceInterval != null">price_interval,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="rewardNum != null">reward_num,</if>
            <if test="winnersNum != null">winners_num,</if>
            <if test="mark != null">mark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="prizeLevel != null">#{prizeLevel},</if>
            <if test="type != null">#{type},</if>
            <if test="planLevel != null">#{planLevel},</if>
            <if test="priceInterval != null">#{priceInterval},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="rewardNum != null">#{rewardNum},</if>
            <if test="winnersNum != null">#{winnersNum},</if>
            <if test="mark != null">#{mark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateCommActivityRewardSettings" parameterType="com.ai.operation.domain.CommActivityRewardSettings">
        update comm_activity_reward_settings
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="prizeLevel != null">prize_level = #{prizeLevel},</if>
            <if test="type != null">type = #{type},</if>
            <if test="planLevel != null">plan_level = #{planLevel},</if>
            <if test="priceInterval != null">price_interval = #{priceInterval},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="rewardNum != null">reward_num = #{rewardNum},</if>
            <if test="winnersNum != null">winners_num = #{winnersNum},</if>
            <if test="mark != null">mark = #{mark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommActivityRewardSettingsById" parameterType="Long">
        delete from comm_activity_reward_settings where id = #{id}
    </delete>

    <delete id="deleteCommActivityRewardSettingsByIds" parameterType="String">
        delete from comm_activity_reward_settings where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>