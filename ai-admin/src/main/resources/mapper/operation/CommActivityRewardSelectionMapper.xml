<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.operation.mapper.CommActivityRewardSelectionMapper">
    
    <resultMap type="com.ai.operation.domain.CommActivityRewardSelection" id="CommActivityRewardSelectionResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="fileId"    column="file_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="likesNum"    column="likes_num"    />
        <result property="commentNum"    column="comment_num"    />
        <result property="prizeLevel"    column="prize_level"    />
        <result property="rewardContent"    column="reward_content"    />
        <result property="publish"    column="publish"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectCommActivityRewardSelectionVo">
        select id, activity_id, user_id, login_name, file_id, file_url, likes_num, comment_num, prize_level, reward_content, publish, create_time, update_time, create_by, update_by from comm_activity_reward_selection
    </sql>

    <select id="selectCommActivityRewardSelectionList" parameterType="com.ai.operation.domain.CommActivityRewardSelection" resultMap="CommActivityRewardSelectionResult">
        <include refid="selectCommActivityRewardSelectionVo"/>
        <where>  
            <if test="activityId != null "> and activity_id = #{activityId}</if>
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="fileId != null  and fileId != ''"> and file_id = #{fileId}</if>
            <if test="prizeLevel != null "> and prize_level = #{prizeLevel}</if>
            <if test="publish != null "> and publish = #{publish}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectCommActivityRewardSelectionById" parameterType="Long" resultMap="CommActivityRewardSelectionResult">
        <include refid="selectCommActivityRewardSelectionVo"/>
        where id = #{id}
    </select>

    <insert id="insertCommActivityRewardSelection" parameterType="com.ai.operation.domain.CommActivityRewardSelection">
        insert into comm_activity_reward_selection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="activityId != null">activity_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="fileId != null">file_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="likesNum != null">likes_num,</if>
            <if test="commentNum != null">comment_num,</if>
            <if test="prizeLevel != null">prize_level,</if>
            <if test="rewardContent != null">reward_content,</if>
            <if test="publish != null">publish,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="activityId != null">#{activityId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="likesNum != null">#{likesNum},</if>
            <if test="commentNum != null">#{commentNum},</if>
            <if test="prizeLevel != null">#{prizeLevel},</if>
            <if test="rewardContent != null">#{rewardContent},</if>
            <if test="publish != null">#{publish},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateCommActivityRewardSelection" parameterType="com.ai.operation.domain.CommActivityRewardSelection">
        update comm_activity_reward_selection
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="likesNum != null">likes_num = #{likesNum},</if>
            <if test="commentNum != null">comment_num = #{commentNum},</if>
            <if test="prizeLevel != null">prize_level = #{prizeLevel},</if>
            <if test="rewardContent != null">reward_content = #{rewardContent},</if>
            <if test="publish != null">publish = #{publish},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCommActivityRewardSelectionById" parameterType="Long">
        delete from comm_activity_reward_selection where id = #{id}
    </delete>

    <delete id="deleteCommActivityRewardSelectionByIds" parameterType="String">
        delete from comm_activity_reward_selection where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id ="selectRewardSelectionUser" resultType="com.ai.operation.domain.dto.SendRewardSelectionDto">
        select cars.id, cars.activity_id, cars.user_id, cars.login_name, cars.file_id, cars.file_url,cars.prize_level, cars.publish,gu.user_name,gu.avatar_url,cars.mini_thumbnail_url
        from comm_activity_reward_selection cars left join gpt_user gu on gu.id = cars.user_id where cars.activity_id = #{activityId} and cars.publish = 0 ;
    </select>
</mapper>