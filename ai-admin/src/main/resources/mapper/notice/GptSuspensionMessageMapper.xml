<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.notice.mapper.GptSuspensionMessageMapper">
    
    <resultMap type="com.ai.notice.domain.GptSuspensionMessage" id="GptSuspensionMessageResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="isEfficient"    column="is_efficient"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectGptSuspensionMessageVo">
        select id, version, is_efficient, start_time, end_time, create_time, update_time, create_by, update_by from gpt_suspension_message
    </sql>

    <select id="selectGptSuspensionMessageList" parameterType="com.ai.notice.domain.GptSuspensionMessage" resultMap="GptSuspensionMessageResult">
        <include refid="selectGptSuspensionMessageVo"/>
        <where>  
            <if test="isEfficient != null "> and is_efficient = #{isEfficient}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and start_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptSuspensionMessageById" parameterType="Long" resultMap="GptSuspensionMessageResult">
        <include refid="selectGptSuspensionMessageVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptSuspensionMessage" parameterType="com.ai.notice.domain.GptSuspensionMessage">
        insert into gpt_suspension_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="version != null">version,</if>
            <if test="isEfficient != null">is_efficient,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="version != null">#{version},</if>
            <if test="isEfficient != null">#{isEfficient},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateGptSuspensionMessage" parameterType="com.ai.notice.domain.GptSuspensionMessage">
        update gpt_suspension_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="isEfficient != null">is_efficient = #{isEfficient},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptSuspensionMessageById" parameterType="Long">
        delete from gpt_suspension_message where id = #{id}
    </delete>

    <delete id="deleteGptSuspensionMessageByIds" parameterType="String">
        delete from gpt_suspension_message where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>