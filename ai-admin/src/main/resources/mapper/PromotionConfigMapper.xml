<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.PromotionConfigMapper">
    
    <resultMap type="PromotionConfig" id="PromotionConfigResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="name"    column="name"    />
        <result property="couponId"    column="coupon_id"    />
        <result property="off"    column="off"    />
        <result property="description"    column="description"    />
        <result property="startTime"    column="start_time"    />
        <result property="redeemBy"    column="redeem_by"    />
        <result property="enable"    column="enable"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPromotionConfigVo">
        select id, type, name, coupon_id, off, description, start_time, redeem_by, enable, create_by, create_time, update_by, update_time from promotion_config
    </sql>

    <select id="selectPromotionConfigList" parameterType="PromotionConfig" resultMap="PromotionConfigResult">
        <include refid="selectPromotionConfigVo"/>
        <where>  
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="couponId != null  and couponId != ''"> and coupon_id = #{couponId}</if>
            <if test="off != null "> and off = #{off}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="redeemBy != null "> and redeem_by = #{redeemBy}</if>
            <if test="enable != null "> and enable = #{enable}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPromotionConfigById" parameterType="Long" resultMap="PromotionConfigResult">
        <include refid="selectPromotionConfigVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPromotionConfig" parameterType="PromotionConfig" useGeneratedKeys="true" keyProperty="id">
        insert into promotion_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="couponId != null and couponId != ''">coupon_id,</if>
            <if test="off != null">off,</if>
            <if test="description != null">description,</if>
            <if test="startTime != null">start_time,</if>
            <if test="redeemBy != null">redeem_by,</if>
            <if test="enable != null">enable,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="couponId != null and couponId != ''">#{couponId},</if>
            <if test="off != null">#{off},</if>
            <if test="description != null">#{description},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="redeemBy != null">#{redeemBy},</if>
            <if test="enable != null">#{enable},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePromotionConfig" parameterType="PromotionConfig">
        update promotion_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="couponId != null and couponId != ''">coupon_id = #{couponId},</if>
            <if test="off != null">off = #{off},</if>
            <if test="description != null">description = #{description},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="redeemBy != null">redeem_by = #{redeemBy},</if>
            <if test="enable != null">enable = #{enable},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePromotionConfigById" parameterType="Long">
        delete from promotion_config where id = #{id}
    </delete>

    <delete id="deletePromotionConfigByIds" parameterType="String">
        delete from promotion_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
