<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.GptVipStandardsMapper">
    
    <resultMap type="com.ai.orders.domain.GptVipStandards" id="GptVipStandardsResult">
        <result property="id"    column="id"    />
        <result property="vipType"    column="vip_type"    />
        <result property="dailyLumens"    column="daily_lumens"    />
        <result property="monthlyLumens"    column="monthly_lumens"    />
        <result property="creationHistory"    column="creation_history"    />
        <result property="taskQueue"    column="task_queue"    />
        <result property="concurrentJobs"    column="concurrent_jobs"    />
        <result property="batchDownload"    column="batch_download"    />
        <result property="imagesPerBatch"    column="images_per_batch"    />
        <result property="upscale"    column="upscale"    />
        <result property="inpaint"    column="inpaint"    />
        <result property="expand"    column="expand"    />
        <result property="colorize"    column="colorize"    />
        <result property="removeBg"    column="remove_bg"    />
        <result property="historyExplore"    column="history_explore"    />
        <result property="translation"    column="translation"    />
        <result property="enhance"    column="enhance"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectGptVipStandardsVo">
        select id, vip_type, daily_lumens, monthly_lumens, creation_history, task_queue, concurrent_jobs, batch_download,collect_num, images_per_batch, upscale, inpaint, expand, colorize, remove_bg, history_explore, translation, enhance, create_by, create_time, update_by, update_time from gpt_vip_standards
    </sql>

    <select id="selectGptVipStandardsList" parameterType="com.ai.orders.domain.GptVipStandards" resultMap="GptVipStandardsResult">
        <include refid="selectGptVipStandardsVo"/>
        <where>  
            <if test="vipType != null  and vipType != ''"> and vip_type = #{vipType}</if>
            <if test="batchDownload != null "> and batch_download = #{batchDownload}</if>
            <if test="upscale != null "> and upscale = #{upscale}</if>
            <if test="inpaint != null "> and inpaint = #{inpaint}</if>
            <if test="expand != null "> and expand = #{expand}</if>
            <if test="colorize != null "> and colorize = #{colorize}</if>
            <if test="removeBg != null "> and remove_bg = #{removeBg}</if>
            <if test="historyExplore != null "> and history_explore = #{historyExplore}</if>
            <if test="translation != null "> and translation = #{translation}</if>
            <if test="enhance != null "> and enhance = #{enhance}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptVipStandardsById" parameterType="Long" resultMap="GptVipStandardsResult">
        <include refid="selectGptVipStandardsVo"/>
        where id = #{id}
    </select>

    <insert id="insertGptVipStandards" parameterType="com.ai.orders.domain.GptVipStandards">
        insert into gpt_vip_standards
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="vipType != null">vip_type,</if>
            <if test="dailyLumens != null">daily_lumens,</if>
            <if test="monthlyLumens != null">monthly_lumens,</if>
            <if test="creationHistory != null">creation_history,</if>
            <if test="taskQueue != null">task_queue,</if>
            <if test="concurrentJobs != null">concurrent_jobs,</if>
            <if test="batchDownload != null">batch_download,</if>
            <if test="imagesPerBatch != null">images_per_batch,</if>
            <if test="upscale != null">upscale,</if>
            <if test="inpaint != null">inpaint,</if>
            <if test="expand != null">expand,</if>
            <if test="colorize != null">colorize,</if>
            <if test="removeBg != null">remove_bg,</if>
            <if test="historyExplore != null">history_explore,</if>
            <if test="translation != null">translation,</if>
            <if test="enhance != null">enhance,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="collectNum != null">collect_num,</if>

         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="vipType != null">#{vipType},</if>
            <if test="dailyLumens != null">#{dailyLumens},</if>
            <if test="monthlyLumens != null">#{monthlyLumens},</if>
            <if test="creationHistory != null">#{creationHistory},</if>
            <if test="taskQueue != null">#{taskQueue},</if>
            <if test="concurrentJobs != null">#{concurrentJobs},</if>
            <if test="batchDownload != null">#{batchDownload},</if>
            <if test="imagesPerBatch != null">#{imagesPerBatch},</if>
            <if test="upscale != null">#{upscale},</if>
            <if test="inpaint != null">#{inpaint},</if>
            <if test="expand != null">#{expand},</if>
            <if test="colorize != null">#{colorize},</if>
            <if test="removeBg != null">#{removeBg},</if>
            <if test="historyExplore != null">#{historyExplore},</if>
            <if test="translation != null">#{translation},</if>
            <if test="enhance != null">#{enhance},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="collectNum != null">#{collectNum},</if>

        </trim>
    </insert>

    <update id="updateGptVipStandards" parameterType="com.ai.orders.domain.GptVipStandards">
        update gpt_vip_standards
        <trim prefix="SET" suffixOverrides=",">
            <if test="vipType != null">vip_type = #{vipType},</if>
            <if test="dailyLumens != null">daily_lumens = #{dailyLumens},</if>
            <if test="monthlyLumens != null">monthly_lumens = #{monthlyLumens},</if>
            <if test="creationHistory != null">creation_history = #{creationHistory},</if>
            <if test="taskQueue != null">task_queue = #{taskQueue},</if>
            <if test="concurrentJobs != null">concurrent_jobs = #{concurrentJobs},</if>
            <if test="batchDownload != null">batch_download = #{batchDownload},</if>
            <if test="imagesPerBatch != null">images_per_batch = #{imagesPerBatch},</if>
            <if test="upscale != null">upscale = #{upscale},</if>
            <if test="inpaint != null">inpaint = #{inpaint},</if>
            <if test="expand != null">expand = #{expand},</if>
            <if test="colorize != null">colorize = #{colorize},</if>
            <if test="removeBg != null">remove_bg = #{removeBg},</if>
            <if test="historyExplore != null">history_explore = #{historyExplore},</if>
            <if test="translation != null">translation = #{translation},</if>
            <if test="enhance != null">enhance = #{enhance},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="collectNum != null">collect_num = #{collectNum},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGptVipStandardsById" parameterType="Long">
        delete from gpt_vip_standards where id = #{id}
    </delete>

    <delete id="deleteGptVipStandardsByIds" parameterType="String">
        delete from gpt_vip_standards where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>