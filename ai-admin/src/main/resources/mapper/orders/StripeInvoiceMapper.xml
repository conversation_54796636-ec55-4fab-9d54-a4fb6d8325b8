<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.StripeInvoiceMapper">
    
    <resultMap type="com.ai.orders.domain.StripeInvoice" id="StripeInvoiceResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="subscriptionId"    column="subscription_id"    />
        <result property="paymentIntentId"    column="payment_intent_id"    />
        <result property="invoiceId"    column="invoice_id"    />
        <result property="billingReason"    column="billing_reason"    />
        <result property="currency"    column="currency"    />
        <result property="customerId"    column="customer_id"    />
        <result property="amountDue"    column="amount_due"    />
        <result property="amountPaid"    column="amount_paid"    />
        <result property="amountRemaining"    column="amount_remaining"    />
        <result property="amountExcludingTax"    column="amount_excluding_tax"    />
        <result property="totalExcludingTax"    column="total_excluding_tax"    />
        <result property="total"    column="total"    />
        <result property="status"    column="status"    />
        <result property="hostedInvoiceUrl"    column="hosted_invoice_url"    />
        <result property="invoicePdf"    column="invoice_pdf"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectStripeInvoiceVo">
        select id, user_id, login_name, subscription_id, payment_intent_id, invoice_id, billing_reason, currency, customer_id, amount_due, amount_paid, amount_remaining, amount_excluding_tax, total_excluding_tax, total, status, hosted_invoice_url, invoice_pdf, create_time, update_time, create_by, update_by from stripe_invoice
    </sql>

    <select id="selectStripeInvoiceList" parameterType="com.ai.orders.domain.StripeInvoice" resultMap="StripeInvoiceResult">
        <include refid="selectStripeInvoiceVo"/>
        <where>  
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="subscriptionId != null  and subscriptionId != ''"> and subscription_id = #{subscriptionId}</if>
            <if test="billingReason != null  and billingReason != ''"> and billing_reason = #{billingReason}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectStripeInvoiceById" parameterType="Long" resultMap="StripeInvoiceResult">
        <include refid="selectStripeInvoiceVo"/>
        where id = #{id}
    </select>

    <insert id="insertStripeInvoice" parameterType="com.ai.orders.domain.StripeInvoice">
        insert into stripe_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="subscriptionId != null and subscriptionId != ''">subscription_id,</if>
            <if test="paymentIntentId != null and paymentIntentId != ''">payment_intent_id,</if>
            <if test="invoiceId != null and invoiceId != ''">invoice_id,</if>
            <if test="billingReason != null and billingReason != ''">billing_reason,</if>
            <if test="currency != null and currency != ''">currency,</if>
            <if test="customerId != null and customerId != ''">customer_id,</if>
            <if test="amountDue != null">amount_due,</if>
            <if test="amountPaid != null">amount_paid,</if>
            <if test="amountRemaining != null">amount_remaining,</if>
            <if test="amountExcludingTax != null">amount_excluding_tax,</if>
            <if test="totalExcludingTax != null and totalExcludingTax != ''">total_excluding_tax,</if>
            <if test="total != null">total,</if>
            <if test="status != null">status,</if>
            <if test="hostedInvoiceUrl != null">hosted_invoice_url,</if>
            <if test="invoicePdf != null">invoice_pdf,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="subscriptionId != null and subscriptionId != ''">#{subscriptionId},</if>
            <if test="paymentIntentId != null and paymentIntentId != ''">#{paymentIntentId},</if>
            <if test="invoiceId != null and invoiceId != ''">#{invoiceId},</if>
            <if test="billingReason != null and billingReason != ''">#{billingReason},</if>
            <if test="currency != null and currency != ''">#{currency},</if>
            <if test="customerId != null and customerId != ''">#{customerId},</if>
            <if test="amountDue != null">#{amountDue},</if>
            <if test="amountPaid != null">#{amountPaid},</if>
            <if test="amountRemaining != null">#{amountRemaining},</if>
            <if test="amountExcludingTax != null">#{amountExcludingTax},</if>
            <if test="totalExcludingTax != null and totalExcludingTax != ''">#{totalExcludingTax},</if>
            <if test="total != null">#{total},</if>
            <if test="status != null">#{status},</if>
            <if test="hostedInvoiceUrl != null">#{hostedInvoiceUrl},</if>
            <if test="invoicePdf != null">#{invoicePdf},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateStripeInvoice" parameterType="com.ai.orders.domain.StripeInvoice">
        update stripe_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="subscriptionId != null and subscriptionId != ''">subscription_id = #{subscriptionId},</if>
            <if test="paymentIntentId != null and paymentIntentId != ''">payment_intent_id = #{paymentIntentId},</if>
            <if test="invoiceId != null and invoiceId != ''">invoice_id = #{invoiceId},</if>
            <if test="billingReason != null and billingReason != ''">billing_reason = #{billingReason},</if>
            <if test="currency != null and currency != ''">currency = #{currency},</if>
            <if test="customerId != null and customerId != ''">customer_id = #{customerId},</if>
            <if test="amountDue != null">amount_due = #{amountDue},</if>
            <if test="amountPaid != null">amount_paid = #{amountPaid},</if>
            <if test="amountRemaining != null">amount_remaining = #{amountRemaining},</if>
            <if test="amountExcludingTax != null">amount_excluding_tax = #{amountExcludingTax},</if>
            <if test="totalExcludingTax != null and totalExcludingTax != ''">total_excluding_tax = #{totalExcludingTax},</if>
            <if test="total != null">total = #{total},</if>
            <if test="status != null">status = #{status},</if>
            <if test="hostedInvoiceUrl != null">hosted_invoice_url = #{hostedInvoiceUrl},</if>
            <if test="invoicePdf != null">invoice_pdf = #{invoicePdf},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStripeInvoiceById" parameterType="Long">
        delete from stripe_invoice where id = #{id}
    </delete>

    <delete id="deleteStripeInvoiceByIds" parameterType="String">
        delete from stripe_invoice where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getInvoiceAmountStatistics" resultType="com.ai.orders.domain.vo.InvoiceAmountCountVo">
        select  sum(si.total)/100,sul.price_id  from  stripe_invoice si
                                                          left join  stripe_subscription_log sul on sul.subscription_id = si.subscription_id where si.status ='paid'
        <if test="startDate != null and '' != startDate">
            and  si.create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > si.create_time
        </if>
        group by sul.price_id ;
    </select>
</mapper>