<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.PayAppleUserRelationMapper">
    
    <resultMap type="com.ai.orders.domain.PayAppleUserRelation" id="PayAppleUserRelationResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="email"    column="email"    />
        <result property="originalTransactionId"    column="original_transaction_id"    />
        <result property="appAccountToken"    column="app_account_token"    />
        <result property="valid"    column="valid"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectPayAppleUserRelationVo">
        select id, user_id, login_name, email, original_transaction_id, app_account_token, valid, create_time, update_time, create_by, update_by from pay_apple_user_relation
    </sql>

    <select id="selectPayAppleUserRelationList" parameterType="com.ai.orders.domain.PayAppleUserRelation" resultMap="PayAppleUserRelationResult">
        <include refid="selectPayAppleUserRelationVo"/>
        <where>  
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="originalTransactionId != null  and originalTransactionId != ''"> and original_transaction_id = #{originalTransactionId}</if>
            <if test="appAccountToken != null  and appAccountToken != ''"> and app_account_token = #{appAccountToken}</if>
            <if test="valid != null "> and valid = #{valid}</if>
            <if test="params !=null and  params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPayAppleUserRelationById" parameterType="Long" resultMap="PayAppleUserRelationResult">
        <include refid="selectPayAppleUserRelationVo"/>
        where id = #{id}
    </select>

    <insert id="insertPayAppleUserRelation" parameterType="com.ai.orders.domain.PayAppleUserRelation" useGeneratedKeys="true" keyProperty="id">
        insert into pay_apple_user_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="email != null">email,</if>
            <if test="originalTransactionId != null">original_transaction_id,</if>
            <if test="appAccountToken != null">app_account_token,</if>
            <if test="valid != null">valid,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="email != null">#{email},</if>
            <if test="originalTransactionId != null">#{originalTransactionId},</if>
            <if test="appAccountToken != null">#{appAccountToken},</if>
            <if test="valid != null">#{valid},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePayAppleUserRelation" parameterType="com.ai.orders.domain.PayAppleUserRelation">
        update pay_apple_user_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="email != null">email = #{email},</if>
            <if test="originalTransactionId != null">original_transaction_id = #{originalTransactionId},</if>
            <if test="appAccountToken != null">app_account_token = #{appAccountToken},</if>
            <if test="valid != null">valid = #{valid},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayAppleUserRelationById" parameterType="Long">
        delete from pay_apple_user_relation where id = #{id}
    </delete>

    <delete id="deletePayAppleUserRelationByIds" parameterType="String">
        delete from pay_apple_user_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>