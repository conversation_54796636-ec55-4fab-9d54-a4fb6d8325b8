<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.orders.mapper.PayLogicPurchaseRecordMapper">
    
    <resultMap type="com.ai.orders.domain.PayLogicPurchaseRecord" id="PayLogicPurchaseRecordResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="priceId"    column="price_id"    />
        <result property="currentPeriodEnd" column="current_period_end" />
        <result property="currentPeriodStart" column="current_period_start" />
        <result property="logicPeriodEnd" column="logic_period_end" />
        <result property="logicPeriodStart" column="logic_period_start" />
        <result property="stripePaymentIntentId"    column="stripe_payment_intent_id"    />
        <result property="vipPlatform"    column="vip_plat_form"    />
        <result property="lumenQty"    column="lumen_qty"    />
        <result property="count"    column="count"    />
        <result property="amount"    column="amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectPayLogicPurchaseRecordVo">
        select id, user_id, login_name, customer_id, price_id, current_period_end, current_period_start, logic_period_end, logic_period_start, stripe_payment_intent_id, lumen_qty, count, amount,vip_plat_form, create_time, update_time, create_by, update_by from pay_logic_purchase_record
    </sql>

    <select id="selectPayLogicPurchaseRecordList" parameterType="com.ai.orders.domain.PayLogicPurchaseRecord"
            resultMap="PayLogicPurchaseRecordResult">
        <include refid="selectPayLogicPurchaseRecordVo"/>
        <where>
            <if test="loginName != null  and loginName != ''">
                and login_name = #{loginName}
            </if>
            <if test="customerId != null  and customerId != ''">
                and customer_id = #{customerId}
            </if>
            <if test="priceId != null  and priceId != ''">
                and price_id = #{priceId}
            </if>
            <if test="stripePaymentIntentId != null  and stripePaymentIntentId != ''">
                and stripe_payment_intent_id = #{stripePaymentIntentId}
            </if>
            <if test="params != null and params.beginCurrentPeriodStart != null and params.beginCurrentPeriodStart != ''
                      and params.endCurrentPeriodStart != null and params.endCurrentPeriodStart != ''">
                AND current_period_start BETWEEN #{params.beginCurrentPeriodStart} AND #{params.endCurrentPeriodStart}
            </if>
            <if test="vipPlatform != null  and vipPlatform != ''"> and vip_plat_form = #{vipPlatform}</if>

        </where>
        order by create_time desc
    </select>
    
    <select id="selectPayLogicPurchaseRecordById" parameterType="Long" resultMap="PayLogicPurchaseRecordResult">
        <include refid="selectPayLogicPurchaseRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertPayLogicPurchaseRecord" parameterType="com.ai.orders.domain.PayLogicPurchaseRecord">
        insert into pay_logic_purchase_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginName != null">login_name,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="priceId != null and priceId != ''">price_id,</if>
            <if test="currentPeriodEnd != null">current_period_end,</if>
            <if test="currentPeriodStart != null">current_period_start,</if>
            <if test="logicPeriodEnd != null">logic_period_end,</if>
            <if test="logicPeriodStart != null">logic_period_start,</if>
            <if test="stripePaymentIntentId != null and stripePaymentIntentId != ''">stripe_payment_intent_id,</if>
            <if test="lumenQty != null">lumen_qty,</if>
            <if test="count != null">count,</if>
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginName != null">#{loginName},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="priceId != null and priceId != ''">#{priceId},</if>
            <if test="currentPeriodEnd != null">#{currentPeriodEnd},</if>
            <if test="currentPeriodStart != null">#{currentPeriodStart},</if>
            <if test="logicPeriodEnd != null">#{logicPeriodEnd},</if>
            <if test="logicPeriodStart != null">#{logicPeriodStart},</if>
            <if test="stripePaymentIntentId != null and stripePaymentIntentId != ''">#{stripePaymentIntentId},</if>
            <if test="lumenQty != null">#{lumenQty},</if>
            <if test="count != null">#{count},</if>
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePayLogicPurchaseRecord" parameterType="com.ai.orders.domain.PayLogicPurchaseRecord">
        update pay_logic_purchase_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="priceId != null and priceId != ''">price_id = #{priceId},</if>
            <if test="currentPeriodEnd != null">current_period_end = #{currentPeriodEnd},</if>
            <if test="currentPeriodStart != null">current_period_start = #{currentPeriodStart},</if>
            <if test="logicPeriodEnd != null">logic_period_end = #{logicPeriodEnd},</if>
            <if test="logicPeriodStart != null">logic_period_start = #{logicPeriodStart},</if>
            <if test="stripePaymentIntentId != null and stripePaymentIntentId != ''">stripe_payment_intent_id = #{stripePaymentIntentId},</if>
            <if test="lumenQty != null">lumen_qty = #{lumenQty},</if>
            <if test="count != null">count = #{count},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayLogicPurchaseRecordById" parameterType="Long">
        delete from pay_logic_purchase_record where id = #{id}
    </delete>

    <delete id="deletePayLogicPurchaseRecordByIds" parameterType="String">
        delete from pay_logic_purchase_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectOrderTradeRecordList" parameterType="com.ai.orders.domain.vo.OrderTradeRecordVo" resultType="com.ai.orders.domain.vo.OrderTradeRecordVo">
        SELECT id, user_id, login_name, currency, price, platform, create_time
        FROM (
        SELECT
        CONVERT(id, CHAR) AS id,
        CONVERT(user_id, CHAR) AS user_id,
        CONVERT(login_name, CHAR) AS login_name,
        CONVERT(currency, CHAR) AS currency,
        CONVERT(total / 100, CHAR) AS price,
        'stripe' AS platform,
        CONVERT(create_time, CHAR) AS create_time
        FROM stripe_invoice
        WHERE status = 'paid'

        UNION

        SELECT
        CONVERT(id, CHAR) AS id,
        CONVERT(user_id, CHAR) AS user_id,
        CONVERT(login_name, CHAR) AS login_name,
        CONVERT(currency, CHAR) AS currency,
        CONVERT(price / 1000, CHAR) AS price,
        'ios' AS platform,
        CONVERT(create_time, CHAR) AS create_time
        FROM pay_apple_jws_transaction
        ) k
        <where>
            <if test="loginName != null and loginName != ''"> and login_name = #{loginName}</if>
            <if test="currency != null and currency != ''"> and currency = #{currency}</if>
            <if test="platform != null and platform != ''"> and platform = #{platform}</if>
            <if test="params != null and params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>