<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.PayCouponMapper">
    
    <resultMap type="PayCoupon" id="PayCouponResult">
        <result property="id"    column="id"    />
        <result property="productType"    column="product_type"    />
        <result property="level"    column="level"    />
        <result property="code"    column="code"    />
        <result property="percentOff"    column="percent_off"    />
        <result property="startTime"    column="start_time"    />
        <result property="redeemBy"    column="redeem_by"    />
        <result property="maxRedemptions"    column="max_redemptions"    />
        <result property="valid"    column="valid"    />
        <result property="mark"    column="mark"    />
        <result property="celebrityName"    column="celebrity_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPayCouponVo">
        select id, product_type, level, code, percent_off, start_time, redeem_by, max_redemptions, valid, mark, celebrity_name, create_by, create_time, update_by, update_time from pay_coupon
    </sql>

    <select id="selectPayCouponList" parameterType="PayCoupon" resultMap="PayCouponResult">
        <include refid="selectPayCouponVo"/>
        <where>  
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="level != null  and level != ''"> and level = #{level}</if>
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
            <if test="percentOff != null "> and percent_off = #{percentOff}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="redeemBy != null "> and redeem_by = #{redeemBy}</if>
            <if test="maxRedemptions != null "> and max_redemptions = #{maxRedemptions}</if>
            <if test="valid != null "> and valid = #{valid}</if>
            <if test="mark != null  and mark != ''"> and mark like concat('%', #{mark}, '%')</if>
            <if test="celebrityName != null  and celebrityName != ''"> and celebrity_name like concat('%', #{celebrityName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectPayCouponById" parameterType="Long" resultMap="PayCouponResult">
        <include refid="selectPayCouponVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPayCoupon" parameterType="PayCoupon" useGeneratedKeys="true" keyProperty="id">
        insert into pay_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productType != null and productType != ''">product_type,</if>
            <if test="level != null and level != ''">level,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="percentOff != null">percent_off,</if>
            <if test="startTime != null">start_time,</if>
            <if test="redeemBy != null">redeem_by,</if>
            <if test="maxRedemptions != null">max_redemptions,</if>
            <if test="valid != null">valid,</if>
            <if test="mark != null">mark,</if>
            <if test="celebrityName != null">celebrity_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productType != null and productType != ''">#{productType},</if>
            <if test="level != null and level != ''">#{level},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="percentOff != null">#{percentOff},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="redeemBy != null">#{redeemBy},</if>
            <if test="maxRedemptions != null">#{maxRedemptions},</if>
            <if test="valid != null">#{valid},</if>
            <if test="mark != null">#{mark},</if>
            <if test="celebrityName != null">#{celebrityName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePayCoupon" parameterType="PayCoupon">
        update pay_coupon
        <trim prefix="SET" suffixOverrides=",">
            <if test="productType != null and productType != ''">product_type = #{productType},</if>
            <if test="level != null and level != ''">level = #{level},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="percentOff != null">percent_off = #{percentOff},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="redeemBy != null">redeem_by = #{redeemBy},</if>
            <if test="maxRedemptions != null">max_redemptions = #{maxRedemptions},</if>
            <if test="valid != null">valid = #{valid},</if>
            <if test="mark != null">mark = #{mark},</if>
            <if test="celebrityName != null">celebrity_name = #{celebrityName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayCouponById" parameterType="Long">
        delete from pay_coupon where id = #{id}
    </delete>

    <delete id="deletePayCouponByIds" parameterType="String">
        delete from pay_coupon where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByCode" parameterType="String" resultType="int">
        select count(*) from pay_coupon where code = #{code}
    </select>

</mapper>
