<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.GptPromptRecordMapper">
    
    <resultMap type="com.ai.admin.domain.GptPromptRecord" id="GptPromptRecordResult">
        <result property="id"    column="id"    />
        <result property="loginName"    column="login_name"    />
        <result property="avatar"    column="avatar"    />
        <result property="genMode"    column="gen_mode"    />
        <result property="markId"    column="mark_id"    />
        <result property="promptId"    column="prompt_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskNumber"    column="task_number"    />
        <result property="prompt"    column="prompt"    />
        <result property="negativePrompt"    column="negative_prompt"    />
        <result property="originCreate"    column="origin_create"    />
        <result property="sendWsFailure"    column="send_ws_failure"    />
        <result property="genStartTime"    column="gen_start_time"    />
        <result property="genEndTime"    column="gen_end_time"    />
        <result property="batchSize"    column="batch_size"    />
        <result property="aspectRatio"    column="aspect_ratio"    />
        <result property="modelId"    column="model_id"    />
        <result property="genInfo"    column="gen_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="del"    column="del"    />
    </resultMap>

    <sql id="selectGptPromptRecordVo">
        select id, login_name, avatar, gen_info, gen_mode, mark_id, prompt_id, task_id, task_number, prompt, negative_prompt, origin_create, send_ws_failure, gen_start_time, gen_end_time, batch_size, aspect_ratio, model_id, create_time, update_time, create_by, update_by, del from gpt_prompt_record
    </sql>

    <select id="selectGptPromptRecordList" parameterType="com.ai.admin.domain.GptPromptRecord" resultMap="GptPromptRecordResult">
        <include refid="selectGptPromptRecordVo"/>
        <where>
            <if test="id !=null">
                <if test="isNext == 1">
                    and  #{id} > id
                </if>
                <if test="isNext == 0">
                    and id > #{id}
                </if>
            </if>
            <if test="loginName != null  and loginName != ''"> and login_name = #{loginName}</if>
            <if test="genMode != null  and genMode != ''"> and gen_mode = #{genMode}</if>
            <if test="prompt != null  and prompt != ''"> and prompt like concat('%', #{prompt}, '%')</if>
            <if test="negativePrompt != null  and negativePrompt != ''"> and negative_prompt like concat('%', #{negativePrompt}, '%')</if>
            <if test="originCreate != null  and originCreate != ''"> and origin_create = #{originCreate}</if>
            <if test="sendWsFailure != null "> and send_ws_failure = #{sendWsFailure}</if>
            <if test="batchSize != null "> and batch_size = #{batchSize}</if>
            <if test="aspectRatio != null  and aspectRatio != ''"> and aspect_ratio = #{aspectRatio}</if>
            <if test="modelId != null  and modelId != ''"> and model_id = #{modelId}</if>
            <if test="beginGenStartTime != null  and endGenStartTime != null "> and gen_start_time between #{beginGenStartTime} and #{endGenStartTime}</if>
            <if test="beginGenEndTime != null   and endGenEndTime != null"> and gen_end_time between #{beginGenEndTime} and #{endGenEndTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectGptPromptRecordById"  resultMap="GptPromptRecordResult">
        <include refid="selectGptPromptRecordVo"/>
        where id = #{id} and  login_name = #{loginName}
    </select>

    <insert id="insertGptPromptRecord" parameterType="com.ai.admin.domain.GptPromptRecord" useGeneratedKeys="true" keyProperty="id">
        insert into gpt_prompt_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loginName != null">login_name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="genMode != null">gen_mode,</if>
            <if test="markId != null">mark_id,</if>
            <if test="promptId != null">prompt_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskNumber != null">task_number,</if>
            <if test="prompt != null">prompt,</if>
            <if test="negativePrompt != null">negative_prompt,</if>
            <if test="originCreate != null">origin_create,</if>
            <if test="sendWsFailure != null">send_ws_failure,</if>
            <if test="genStartTime != null">gen_start_time,</if>
            <if test="genEndTime != null">gen_end_time,</if>
            <if test="batchSize != null">batch_size,</if>
            <if test="aspectRatio != null">aspect_ratio,</if>
            <if test="modelId != null">model_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="del != null">del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loginName != null">#{loginName},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="genMode != null">#{genMode},</if>
            <if test="markId != null">#{markId},</if>
            <if test="promptId != null">#{promptId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskNumber != null">#{taskNumber},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="negativePrompt != null">#{negativePrompt},</if>
            <if test="originCreate != null">#{originCreate},</if>
            <if test="sendWsFailure != null">#{sendWsFailure},</if>
            <if test="genStartTime != null">#{genStartTime},</if>
            <if test="genEndTime != null">#{genEndTime},</if>
            <if test="batchSize != null">#{batchSize},</if>
            <if test="aspectRatio != null">#{aspectRatio},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="del != null">#{del},</if>
         </trim>
    </insert>

    <update id="updateGptPromptRecord" parameterType="com.ai.admin.domain.GptPromptRecord">
        update gpt_prompt_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="loginName != null">login_name = #{loginName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="genMode != null">gen_mode = #{genMode},</if>
            <if test="markId != null">mark_id = #{markId},</if>
            <if test="promptId != null">prompt_id = #{promptId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskNumber != null">task_number = #{taskNumber},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="negativePrompt != null">negative_prompt = #{negativePrompt},</if>
            <if test="originCreate != null">origin_create = #{originCreate},</if>
            <if test="sendWsFailure != null">send_ws_failure = #{sendWsFailure},</if>
            <if test="genStartTime != null">gen_start_time = #{genStartTime},</if>
            <if test="genEndTime != null">gen_end_time = #{genEndTime},</if>
            <if test="batchSize != null">batch_size = #{batchSize},</if>
            <if test="aspectRatio != null">aspect_ratio = #{aspectRatio},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="del != null">del = #{del},</if>
        </trim>
        where id = #{id}  and  login_name = #{loginName}
    </update>

    <delete id="deleteGptPromptRecordById" parameterType="Long">
        delete from gpt_prompt_record where id = #{id}
    </delete>

    <delete id="deleteGptPromptRecordByIds" parameterType="String">
        delete from gpt_prompt_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectGptPromptRecordsWithFiles" parameterType="UserProfileDto" resultType="UserProfileVo">
        SELECT
            gpr.prompt_id,
            gpr.login_name,
            gpr.create_time,
            gpr.gen_start_time,
            gpr.gen_end_time,
            GROUP_CONCAT(gpf.id ORDER BY gpf.id ASC) AS fileIds,
            GROUP_CONCAT(gpf.thumbnail_url ORDER BY gpf.id ASC) AS thumbnailUrls,
            gpr.gen_info
        FROM
            gpt_prompt_record gpr
                LEFT JOIN
            gpt_prompt_file gpf ON gpr.prompt_id = gpf.prompt_id
        <where>
            gpr.create_time between #{beginGenCreateTime} and #{endGenCreateTime}
            <if test="isSuccess!=null">
               <if  test="isSuccess==1">
                 and gpf.id is not null
               </if>
               <if  test="isSuccess==0">
                 and gpf.id is null
               </if>
            </if>
            <if test="loginName != null and loginName!=''">
                and gpr.login_name like concat('%', #{loginName}, '%')
            </if>
            <if test="promptId != null and promptId!=''">
                and gpr.prompt_id like concat('%', #{promptId}, '%')
            </if>
        </where>
        GROUP BY
        gpr.prompt_id, gpr.login_name, gpr.create_time, gpr.gen_start_time, gpr.gen_end_time, gpr.gen_info
        ORDER BY
        gpr.create_time DESC
    </select>

    <select id="queryRecordListByCondition" resultType="com.ai.admin.domain.GptPromptRecord">
        select id,
                batch_size,
               prompt_Id
        from ${param.dbName}
        where
            id &gt; #{param.lastId}
          and create_time &lt; #{param.startQueryTime}
        order by id asc
            limit   #{param.limitSize}
    </select>

    <delete id="deleteGptPromptRecordByIdsWithDbName" parameterType="String">
        delete from ${dbName} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGenPicAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
        select avg(timestampdiff(SECOND,create_time,gen_start_time)) as genPicAvgTime from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
    </select>

    <select id="getGenCreate" resultType="java.lang.Long">
        SELECT  COUNT(*) as nums  from gpt_prompt_record
        where   origin_create != 'customUpload'
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="genModeType != null and '' != genModeType">
            and  gen_mode = #{genModeType}
        </if>
        <if test="fastHour != null">
            and fast_hour = #{fastHour}
        </if>
    </select>

    <select id="getGenBatchSize" parameterType="java.lang.String" resultType="com.ai.admin.domain.vo.Statistics">
        SELECT  batch_size as label,COUNT(*) as value  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY batch_size
        order by batch_size
    </select>

    <select id="getGenOriginCreate" parameterType="java.lang.String" resultType="com.ai.admin.domain.vo.Statistics">
        SELECT  origin_create as label,COUNT(*) as value  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY origin_create
        order by origin_create
    </select>

    <select id="getGenModelId" parameterType="java.lang.String" resultType="com.ai.admin.domain.vo.Statistics">
        SELECT  model_id as label,COUNT(*) as value  from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY model_id
        order by model_id
    </select>

    <select id="getMaxAspectRatio" parameterType="java.util.List" resultType="com.ai.admin.domain.vo.Statistics">
        select  aspect_ratio as label,max(num) as value from (
        SELECT count(id) as num,aspect_ratio  FROM gpt_prompt_record where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY aspect_ratio) t
        GROUP BY aspect_ratio;
    </select>

    <select id="getGenBatchSizePicAvgTime" parameterType="java.lang.String" resultType="java.lang.Double">
        select avg(timestampdiff(SECOND,gen_start_time,gen_end_time)) as genPicAvgTime from gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="batchSize != null and '' != batchSize">
            and  batch_size = #{batchSize}
        </if>
    </select>


    <select id="getImgToImgStyleNums" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT count(id) FROM gpt_prompt_record
        where 1 = 1
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        <if test="style != null and '' != style">
            and  JSON_EXTRACT(gen_info, '$.img2img_info.style')  = #{style};
        </if>
    </select>

    <select id="getSuccessTaskCount" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(*) from gpt_prompt_record
        where  origin_create != 'customUpload' and prompt_id is not null and gen_end_time is not null
        and failure_message is null
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
    </select>

    <select id="getCancelTaskCount" parameterType="java.lang.String" resultType="java.lang.Long">
        select count(*) from gpt_prompt_record
        where  origin_create != 'customUpload'
        and   failure_message in ('cancel', 'gen image error No face detected, please upload a new image contains a face')
        <if test="startDate != null and '' != startDate">
            and  create_time > #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
    </select>

    <delete id="physicRemoveDeleteDataBatch">
        delete from ${recordDbNum} where del = 1
        <if test="endTime != null">
            and create_time <![CDATA[<=]]> #{endTime}
        </if>
        limit ${limit}
    </delete>

    <update id="updateToDelByCondition" >
        update ${recordDbNum} set del = 1
        where del =0
        and create_time <![CDATA[<]]> #{startTime}
        <if test="endTime != null">
            and create_time <![CDATA[>]]> #{endTime}
        </if>
        order by id desc
        limit ${limit}
    </update>

    <update id="updateToDelByConditionWithVip">
        update ${recordDbNum} pr
        join (
            SELECT pf.id from ${recordDbNum} pf
            JOIN gpt_user u ON u.login_name = pf.login_name
            WHERE pf.del = 0 and (
                (
                    u.vip_end_time IS NULL
                    AND (u.vip_type IS NULL OR u.vip_type = 'basic')
                    AND pf.create_time &lt; #{startTime}
                )
                OR (
                    u.vip_end_time IS NOT NULL
                    AND u.vip_end_time &lt; UNIX_TIMESTAMP(NOW() - INTERVAL #{vipExpireDays} DAY)
                    AND pf.create_time &lt; #{startTime}
                )
            )
            order by pf.id asc
            limit ${limit}
        ) tt on pr.id = tt.id
        set del = 1
        WHERE pr.id is not null
    </update>

    <select id="getCreatePictureNums" parameterType="java.lang.String" resultType="java.lang.Long">
        select sum(batch_size) as nums  from gpt_prompt_record
        where  origin_create != 'customUpload' and prompt_id is not null and gen_end_time is not null
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
    </select>

    <select id="getNumsModelId" parameterType="java.lang.String" resultType="com.ai.admin.domain.vo.ModelNumVo">
        SELECT
        model_id,
        SUM(batch_size) AS file_num,
        COUNT(*) AS record_num,
        COUNT(DISTINCT login_name) AS user_num
        FROM (
        SELECT * FROM gpt_prompt_record_0
        UNION ALL
        SELECT * FROM gpt_prompt_record_1
        UNION ALL
        SELECT * FROM gpt_prompt_record_2
        UNION ALL
        SELECT * FROM gpt_prompt_record_3
        UNION ALL
        SELECT * FROM gpt_prompt_record_4
        UNION ALL
        SELECT * FROM gpt_prompt_record_5
        UNION ALL
        SELECT * FROM gpt_prompt_record_6
        UNION ALL
        SELECT * FROM gpt_prompt_record_7
        UNION ALL
        SELECT * FROM gpt_prompt_record_8
        UNION ALL
        SELECT * FROM gpt_prompt_record_9
        UNION ALL
        SELECT * FROM gpt_prompt_record_10
        UNION ALL
        SELECT * FROM gpt_prompt_record_11
        UNION ALL
        SELECT * FROM gpt_prompt_record_12
        UNION ALL
        SELECT * FROM gpt_prompt_record_13
        UNION ALL
        SELECT * FROM gpt_prompt_record_14
        UNION ALL
        SELECT * FROM gpt_prompt_record_15
        UNION ALL
        SELECT * FROM gpt_prompt_record_16
        UNION ALL
        SELECT * FROM gpt_prompt_record_17
        UNION ALL
        SELECT * FROM gpt_prompt_record_18
        UNION ALL
        SELECT * FROM gpt_prompt_record_19
        ) AS combined
        WHERE
        origin_create != 'customUpload'
        AND prompt_id IS NOT NULL
        AND gen_end_time IS NOT NULL
        <if test="startDate != null and '' != startDate">
            and  create_time >= #{startDate}
        </if>
        <if test="endDate != null and '' != endDate">
            and  #{endDate} > create_time
        </if>
        GROUP BY model_id
        ORDER BY model_id;
    </select>

    <select id="getVipDau" resultType="java.lang.Long">
        select count(distinct sc.login_name) from gpt_prompt_record gpr left join  subscription_current  sc on gpr.login_name = sc.login_name and gpr.create_time >= '2025-01-15 00:00:00'
        where sc.id is not null and gpr.create_time >= #{startDate}
          and  #{endDate} > gpr.create_time
    </select>
</mapper>