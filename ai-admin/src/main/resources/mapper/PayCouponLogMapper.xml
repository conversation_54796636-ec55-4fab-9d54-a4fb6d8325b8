<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.admin.mapper.PayCouponLogMapper">
    
    <resultMap type="PayCouponLog" id="PayCouponLogResult">
        <result property="id"    column="id"    />
        <result property="couponCode"    column="coupon_code"    />
        <result property="off"    column="off"    />
        <result property="relationId"    column="relation_id"    />
        <result property="type"    column="type"    />
        <result property="productType"    column="product_type"    />
        <result property="userId"    column="user_id"    />
        <result property="loginName"    column="login_name"    />
        <result property="usedTime"    column="used_time"    />
        <result property="productName"    column="product_name"    />
        <result property="qty"    column="qty"    />
        <result property="planId"    column="plan_id"    />
        <result property="priceId"    column="price_id"    />
        <result property="amount"    column="amount"    />
        <result property="srcAmount"    column="src_amount"    />
        <result property="discountAmount"    column="discount_amount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.ai.admin.domain.dto.PayCouponStatsDTO" id="PayCouponStatsResult">
        <result property="couponCode"    column="coupon_code"    />
        <result property="type"    column="type"    />
        <result property="totalLumenAmount"    column="total_lumen_amount"    />
        <result property="memberCount"    column="member_count"    />
        <result property="totalMemberAmount"    column="total_member_amount"    />
        <result property="lumenPackageCount"    column="lumen_package_count"    />
        <result property="lumenAmount"    column="lumen_amount"    />
    </resultMap>

    <resultMap type="com.ai.admin.domain.dto.PayCouponPurchaseDetailDTO" id="PayCouponPurchaseDetailResult">
        <result property="loginName"    column="login_name"    />
        <result property="productName"    column="product_name"    />
        <result property="qty"    column="qty"    />
        <result property="amount"    column="amount"    />
        <result property="usedTime"    column="used_time"    />
        <result property="couponCode"    column="coupon_code"    />
        <result property="productType"    column="product_type"    />
    </resultMap>

    <sql id="selectPayCouponLogVo">
        select id, coupon_code, off, relation_id, type, product_type, user_id, login_name, used_time, product_name, qty, plan_id, price_id, amount, src_amount, discount_amount, create_by, create_time, update_by, update_time from pay_coupon_log
    </sql>

    <select id="selectPayCouponLogList" parameterType="PayCouponLog" resultMap="PayCouponLogResult">
        <include refid="selectPayCouponLogVo"/>
        <where>  
            <if test="couponCode != null  and couponCode != ''"> and coupon_code like concat('%', #{couponCode}, '%')</if>
            <if test="loginName != null  and loginName != ''"> and login_name like concat('%', #{loginName}, '%')</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="usedTime != null "> and used_time = #{usedTime}</if>
        </where>
        order by used_time desc
    </select>

    <select id="selectStatsByCouponCode" parameterType="String" resultMap="PayCouponStatsResult">
        SELECT
            #{couponCode} as coupon_code,
            type,
            COALESCE(SUM(CASE WHEN product_type = 'one' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as total_lumen_amount,
            COUNT(DISTINCT CASE WHEN product_type = 'plan' THEN user_id END) as member_count,
            COALESCE(SUM(CASE WHEN product_type = 'plan' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as total_member_amount,
            COUNT(DISTINCT CASE WHEN product_type = 'one' THEN user_id END) as lumen_package_count,
            COALESCE(SUM(CASE WHEN product_type = 'one' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as lumen_amount
        FROM pay_coupon_log
        WHERE coupon_code = #{couponCode}
        GROUP BY type
    </select>

    <select id="selectPurchaseDetailsByCouponCode" parameterType="String" resultMap="PayCouponPurchaseDetailResult">
        SELECT 
            login_name,
            product_name,
            qty,
            CAST(amount AS DECIMAL(10,2)) as amount,
            used_time,
            coupon_code,
            product_type
        FROM pay_coupon_log 
        WHERE coupon_code = #{couponCode}
        ORDER BY used_time DESC
    </select>

    <select id="selectAllCouponStats" resultMap="PayCouponStatsResult">
        SELECT
            coupon_code,
            type,
            COALESCE(SUM(CASE WHEN product_type = 'one' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as total_lumen_amount,
            COUNT(DISTINCT CASE WHEN product_type = 'plan' THEN user_id END) as member_count,
            COALESCE(SUM(CASE WHEN product_type = 'plan' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as total_member_amount,
            COUNT(DISTINCT CASE WHEN product_type = 'one' THEN user_id END) as lumen_package_count,
            COALESCE(SUM(CASE WHEN product_type = 'one' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as lumen_amount
        FROM pay_coupon_log
        GROUP BY coupon_code, type
        ORDER BY type, coupon_code
    </select>

    <select id="selectCouponStatsByType" parameterType="String" resultMap="PayCouponStatsResult">
        SELECT
            coupon_code,
            type,
            COALESCE(SUM(CASE WHEN product_type = 'one' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as total_lumen_amount,
            COUNT(DISTINCT CASE WHEN product_type = 'plan' THEN user_id END) as member_count,
            COALESCE(SUM(CASE WHEN product_type = 'plan' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as total_member_amount,
            COUNT(DISTINCT CASE WHEN product_type = 'one' THEN user_id END) as lumen_package_count,
            COALESCE(SUM(CASE WHEN product_type = 'one' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END), 0) as lumen_amount
        FROM pay_coupon_log
        WHERE type = #{type}
        GROUP BY coupon_code, type
        ORDER BY coupon_code
    </select>

    <select id="selectAllTypes" resultType="String">
        SELECT DISTINCT type
        FROM pay_coupon_log
        WHERE type IS NOT NULL AND type != ''
        ORDER BY type
    </select>

</mapper>
