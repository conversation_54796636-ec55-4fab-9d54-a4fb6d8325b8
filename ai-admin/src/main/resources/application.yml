# Spring配置
spring:
  profiles:
    active: dev
  # 配置flyway数据版本管理
  flyway:
    enabled: false
    baseline-on-migrate: true
    clean-on-validation-error: false
    sql-migration-prefix: V
    sql-migration-suffixes: .sql
    locations: classpath:db/migration
  # 邮件
  mail:
    # from 要和 username 一致, 否则报错
    from: <EMAIL>
#    from: <EMAIL>
    # 邮件服务地址
#    host: smtp.qq.com
    host: smtp.qiye.aliyun.com
    # 用户名
#    username: <EMAIL>
    username: <EMAIL>
    # 授权码 (设置 - 账户 - POP3/SMTP服务)
#    password: nvvqefzuwzuxbbgi
    password: 2xle10KnKjJcKxxi
    # QQ邮箱加密端口，不同邮箱的端口不一样
    port: 465
    toUsers:
      - <EMAIL>
#      - <EMAIL>
#      - hesen<PERSON>@info.easeus.com.cn
#      - <EMAIL>
    properties:
      mail:
        smtp:
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          ssl:
#            trust: smtp.qq.com
            trust: smtp.qiye.aliyun.com
          # 是否需要用户认证
          auth: true
          starttls:
            # 启用TLS加密
            enable: true
            required: true
#home页运维账户
  opex:
    user-names:
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
      - <EMAIL>
tencent-cloud:
  monitor:
    gpu:
      secret-id: "AKIDb9XjhRyaVMsBHSbaM1Yt593i9duxnUHd"
      secret-key: "IWxEa8X2TjsvnSKEpJbhZDejmeSL6e9V"
      region: "ap-guangzhou"
      namespace: "QCE/CVM"
      instance-ids: "ins-6q1zimju,ins-luu3w816,ins-okjxtfpe,ins-a7sja5ya,ins-k2znufm0,ins-kab72wq4,ins-bip3g0jk,ins-0gn84x9o,ins-lisc33b4"
      specifyStatistics: 1
  storage:
    secret-id: "AKIDb9XjhRyaVMsBHSbaM1Yt593i9duxnUHd"
    secret-key: "IWxEa8X2TjsvnSKEpJbhZDejmeSL6e9V"
    region: "ap-shanghai"
    bucket-name: "example-bucket"

quartz:
  un-log-task-names:
    - "Fail Comfy Check"
    - "monitor comfy status"
apple:
  key-id: NM58A7N22Y
  issuer-id: 69a6de80-ea80-47e3-e053-5b8c7c11a4d1
  private-key-path: classpath:AuthKey_NM58A7N22Y.p8


