package com.ai.framework.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "email")
@Getter
@Setter
public class EmailConfig {

    @Value("${email.sendCloudUrl:https://api.sendcloud.net/apiv2/mail/send}")
    private String sendCloudUrl;

    @Value("${email.sendCloudUrl:39411ea65c526a551434263b62f72e4b}")
    private String apikey;

    @Value("${endcloud.api.user:piclumen_api}")
    private String apiUser;


}
