package com.ai.framework.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "spring.redis")
public class RedisProperties {

    private Map<String, DatabaseProperties> db = new HashMap<>();

    public Map<String, DatabaseProperties> getDb() {
        return db;
    }
    public void setDb(Map<String, DatabaseProperties> db) {
        this.db = db;
    }

    public static class DatabaseProperties {
        private String host;
        private int port;
        private int database;
        private int timeout;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public int getDatabase() {
            return database;
        }

        public void setDatabase(int database) {
            this.database = database;
        }

        public int getTimeout() {
            return timeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }
    }
}
